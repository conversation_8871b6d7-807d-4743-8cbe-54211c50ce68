import { Env, OpenAIRequest, OpenAIResponse, ExpenseParseRequest, APIError } from './types';

export class OpenAIService {
  private readonly baseURL: string;
  private readonly apiKey: string;
  private readonly model: string;

  constructor(private env: Env) {
    // Support both OpenAI and ARK APIs
    if (env.ARK_API_KEY && env.ARK_BASE_URL) {
      this.baseURL = env.ARK_BASE_URL;
      this.apiKey = env.ARK_API_KEY;
      this.model = env.ARK_MODEL || 'doubao-1-5-lite-32k-250115';
    } else if (env.OPENAI_API_KEY && env.OPENAI_BASE_URL) {
      this.baseURL = env.OPENAI_BASE_URL;
      this.apiKey = env.OPENAI_API_KEY;
      this.model = env.OPENAI_MODEL || 'gpt-3.5-turbo';
    } else {
      throw new Error('No valid AI provider configuration found. Please set either OpenAI or ARK credentials.');
    }
  }

  /**
   * Parse expense text using OpenAI GPT
   */
  async parseExpenseText(request: ExpenseParseRequest): Promise<any> {
    const timezoneOffset = request.context?.timezone_offset ?? null;
    const systemPrompt = this.buildSystemPrompt(timezoneOffset);
    const userPrompt = this.buildUserPrompt(request);

    const openaiRequest: OpenAIRequest = {
      model: this.model,
      messages: [
        { role: 'system', content: systemPrompt },
        { role: 'user', content: userPrompt }
      ],
      temperature: 0.1,
      max_tokens: 1000,
      response_format: { type: 'json_object' }
    };

    try {
      const response = await this.callOpenAI(openaiRequest);
      const content = response.choices[0]?.message?.content;
      if (!content) {
        throw new APIError('Empty response from AI API', 500, 'AI_API_ERROR');
      }
      const result = JSON.parse(content);

      // Validate and normalize the response
      return this.normalizeResponse(result, request);
    } catch (error) {
      console.error('AI API error:', error);
      throw new APIError('Failed to parse expense text', 500, 'AI_API_ERROR');
    }
  }

  /**
   * Build system prompt for expense parsing
   */
  private buildSystemPrompt(timezoneOffset: number | null): string {
    return `You are an expert financial transaction parser specializing in mobile payment receipts and bank transaction records. Extract structured information from payment text in any language with high accuracy.

CRITICAL: Respond with valid JSON only. No explanations, comments, or additional text.

EXTRACTION TARGETS:
- amount: Numerical value only (string, remove currency symbols, commas, spaces)
- currency: ISO 4217 code (USD, GBP, EUR, CNY, JPY, ISK, etc.)
- merchant: Primary business/merchant name (clean, without extra info)
- payment_method: Payment method (Apple Pay, Google Pay, Alipay, WeChat Pay, Credit Card, Debit Card, etc.)
- payment_card: Specific card/bank (Monzo, HSBC, Starling, Chase, Visa, Mastercard, etc.)
- location: Geographic location (city, country, or address)
- timestamp: Local time format (YYYY-MM-DDTHH:mm:ss) WITHOUT timezone, representing the actual transaction time from receipt
- confidence: Accuracy score (0.0-1.0)
- extensions: Additional metadata

PARSING RULES:
1. AMOUNT: Look for numerical values with currency symbols (£, $, ¥, €, kr, ISK, etc.)
   - Remove all non-numeric characters except decimal points
   - Handle formats: "£9.65", "ISK 2,799", "¥25.00", "$12.50"

2. CURRENCY: Map symbols and codes to ISO standards
   - £ → GBP, $ → USD, ¥ → CNY/JPY, € → EUR, kr/ISK → ISK
   - Look for explicit codes: USD, GBP, EUR, CNY, JPY, ISK
   - If no explicit currency is mentioned but the text contains Chinese payment-related keywords (e.g., "财付通", "零钱", "小程序", "淘宝", "支付宝", "余额宝"), infer CNY.
   - For English/UK contexts without explicit currency, infer GBP.
   - For US contexts, infer USD.
   - When no explicit currency is detected, use comprehensive inference (in priority order):
     - PRIORITY 1: User's location context (e.g., user in UK → GBP, user in China → CNY, user in Iceland → ISK)
     - PRIORITY 2: Merchant/location information from text (e.g., "Iceland" → ISK, "Japan" → JPY, "China/中国" → CNY)
     - PRIORITY 3: Text language context (e.g., Chinese characters → CNY, Icelandic text → ISK, Japanese text → JPY)
     - PRIORITY 4: Payment method context (e.g., Alipay/支付宝 → CNY, WeChat Pay/微信支付 → CNY)
     - PRIORITY 5: Merchant brand context (e.g., known Chinese brands → CNY, known UK brands → GBP)
   - Apply inference even for ambiguous amounts without clear currency symbols
   - Default to USD only if all inference methods fail

3. MERCHANT: Extract primary business name
   - Prioritize recognizable brand names: "Costa Coffee", "Starbucks", "McDonald's"
   - Clean up: "Costa Coffee，英格兰 Hounslow" → "Costa Coffee"
   - Ignore: transaction IDs, addresses, extra descriptors

4. PAYMENT_METHOD: Identify payment method
   - Mobile: Apple Pay, Google Pay, Samsung Pay
   - Chinese: Alipay, WeChat Pay, 支付宝, 微信支付
   - Cards: Credit Card, Debit Card, Visa, Mastercard
   - If card type mentioned: "Visa Debit Card" → "Debit Card"

5. PAYMENT_CARD: Identify bank/card provider
   - Banks: Monzo, HSBC, Chase, Starling, Revolut, etc.
   - Card types: Visa, Mastercard, American Express
   - Format: "HSBC UK Visa Debit Card" → "HSBC"

6. LOCATION: Extract geographic information
   - Cities: "Hounslow", "凱夫拉維克", "Beijing"
   - Countries: "英格兰" → "England", "Iceland"
   - Airports: "机场" indicates airport location

7. TIMESTAMP: Parse actual transaction date/time from the receipt text
   - PRIORITIZE time found in the receipt text over any user provided timestamp
   - Look for explicit transaction time labels: "Transaction Time:", "Time:", "Date:", "交易时间:", "时间:", "日期:", "打印时间:", "创单时间:", etc.
   - Prioritize dates with clear transaction context over general dates
   - Formats: "2023-09-20 01:47", "2022/12/16 14:09", "09:41", "2023年09月20日 01:47"
   - CRITICAL TIMESTAMP RULES:
     * Extract the EXACT time from the receipt text
     * Use the EXACT format found in the text, do NOT add timezone suffixes like 'Z' or '+08:00'
     * If time format is "2025-08-16 17:20:51", return "2025-08-16T17:20:51" (add T but NO timezone)
     * The time represents LOCAL time where the transaction occurred
     * Do NOT convert to UTC or add any timezone information
     * NEVER add 'Z' suffix or any timezone offset to the timestamp
   - Use user provided timestamp ONLY as fallback if no time is found in text
   - Set to null if no time information available at all

8. EXTENSIONS: Add contextual information
   - category: Food & Beverage, Transportation, Shopping, Accommodation, etc.
   - tags: Relevant keywords ["coffee", "airport", "hotel", "restaurant"]
   - description: Brief summary of the transaction

9. CONFIDENCE: Base on information clarity
   - 0.9-1.0: All key fields clearly identified
   - 0.7-0.9: Most fields identified, some ambiguity
   - 0.5-0.7: Basic info only, significant ambiguity
   - <0.5: Very unclear or incomplete

LANGUAGE HANDLING:
- English: Standard processing
- Chinese: Handle mixed Chinese/English text
- Other languages: Extract recognizable elements

EXAMPLE RESPONSES:

Costa Coffee Transaction:
{
  "amount": "9.65",
  "currency": "GBP",
  "merchant": "Costa Coffee",
  "payment_method": "Debit Card",
  "payment_card": "HSBC",
  "location": "Hounslow, England",
  "timestamp": "2023-09-20T01:47:00Z",
  "confidence": 0.92,
  "extensions": {
    "category": "Food & Beverage",
    "tags": ["coffee", "food", "airport"],
    "description": "Costa Coffee purchase at Hounslow"
  }
}

Chinese Receipt Transaction:
{
  "amount": "25.00",
  "currency": "CNY",
  "merchant": "星巴克",
  "payment_method": "支付宝",
  "payment_card": null,
  "location": "北京",
  "timestamp": "2023-09-20T14:30:00Z",
  "confidence": 0.95,
  "extensions": {
    "category": "Food & Beverage",
    "tags": ["coffee", "chinese"],
    "description": "星巴克消费"
  }
}

Hotel Transaction:
{
  "amount": "2799",
  "currency": "ISK",
  "merchant": "Aurora Star Airport Hotel",
  "payment_method": "Credit Card",
  "payment_card": "Monzo",
  "location": "Keflavik, Iceland",
  "timestamp": "2022-12-16T14:09:00Z",
  "confidence": 0.88,
  "extensions": {
    "category": "Accommodation",
    "tags": ["hotel", "airport", "travel"],
    "description": "Airport hotel payment in Iceland"
  }
}`;
  }

  /**
   * Build user prompt with context
   */
  private buildUserPrompt(request: ExpenseParseRequest): string {
    let prompt = `TRANSACTION TEXT TO PARSE:\n\n"${request.text}"\n\n`;

    // Add context information if available
    if (request.context) {
      prompt += `ADDITIONAL CONTEXT:\n`;
      if (request.context.location) {
        prompt += `- User Location: ${request.context.location}\n`;
      }
      if (request.context.timestamp) {
        prompt += `- Transaction Time: ${request.context.timestamp}\n`;
      }
      if (request.context.image_metadata) {
        prompt += `- Source: Image/Screenshot (${request.context.image_metadata.format})\n`;
      }
      prompt += `\n`;
    }

    prompt += `PARSING INSTRUCTIONS:
1. Carefully analyze the text for financial transaction information
2. Extract all identifiable elements according to the system rules
3. Pay special attention to currency symbols and amount formatting
4. CRITICAL: For currency inference when no explicit symbol is present, use ALL available context:
   - User location, merchant location, text language, payment method, and merchant brand
   - Do NOT default to USD without attempting comprehensive inference first
5. Identify merchant names even if mixed with location/address info
6. Handle multi-language text (English, Chinese, etc.)
7. CRITICAL: For timestamp, look specifically for transaction time labels (Time:, Date:, 交易时间:, etc.) and prioritize these over general dates
8. Return only the JSON response, no other text

PARSE NOW:`;

    return prompt;
  }

  /**
   * Call OpenAI-compatible API (OpenAI, ARK, or other providers)
   */
  private async callOpenAI(request: OpenAIRequest): Promise<OpenAIResponse> {
    const response = await fetch(`${this.baseURL}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(request),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error('ARK API error:', response.status, error);
      throw new APIError(`ARK API error: ${response.status}`, response.status);
    }

    return await response.json();
  }

  /**
   * Normalize and validate OpenAI response
   */
  private normalizeResponse(result: any, originalRequest: ExpenseParseRequest): any {
    // Validate required fields
    if (!result.amount || !result.currency) {
      throw new APIError('Invalid response: missing amount or currency');
    }

    // Normalize currency code
    result.currency = this.normalizeCurrency(result.currency);

    // Ensure confidence is within valid range
    if (typeof result.confidence !== 'number' || result.confidence < 0 || result.confidence > 1) {
      result.confidence = 0.5;
    }

    // Ensure extensions object exists
    if (!result.extensions) {
      result.extensions = {};
    }

    // Add parsing metadata
    result.extensions.parsed_at = new Date().toISOString();
    result.extensions.source = 'openai_gpt';
    result.extensions.original_text = originalRequest.text;

    return result;
  }

  /**
   * Normalize currency codes
   */
  private normalizeCurrency(currency: string): string {
    const currencyMap: { [key: string]: string } = {
      '¥': 'CNY',
      '￥': 'CNY',
      '元': 'CNY',
      '$': 'USD',
      '美元': 'USD',
      '€': 'EUR',
      '欧元': 'EUR',
      '£': 'GBP',
      '英镑': 'GBP',
      '港币': 'HKD',
      '港元': 'HKD',
      '台币': 'TWD',
      '新台币': 'TWD',
      '日元': 'JPY',
      '韩元': 'KRW',
    };

    // Direct mapping
    if (currencyMap[currency]) {
      return currencyMap[currency];
    }

    // Already ISO code
    if (/^[A-Z]{3}$/.test(currency)) {
      return currency;
    }

    // Default fallback
    return 'USD';
  }

  /**
   * Health check for ARK service
   */
  async healthCheck(): Promise<boolean> {
    try {
      const testRequest: OpenAIRequest = {
        model: this.env.ARK_MODEL || 'doubao-1-5-lite-32k-250115',
        messages: [
          { role: 'user', content: 'Respond with "OK" if you can process this request.' }
        ],
        max_tokens: 10
      };

      const response = await this.callOpenAI(testRequest);
      return response.choices?.[0]?.message?.content?.includes('OK') || false;
    } catch (error) {
      console.error('AI API health check failed:', error);
      return false;
    }
  }
}
