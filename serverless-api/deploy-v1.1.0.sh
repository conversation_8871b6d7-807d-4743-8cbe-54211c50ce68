#!/bin/bash

# Deploy script for FinPin API v1.1.0
# This script deploys the new version to v1.finpin.app domain

set -e

echo "🚀 Deploying FinPin API v1.1.0..."

# Check if wrangler is installed
if ! command -v wrangler &> /dev/null; then
    echo "❌ Wrangler CLI not found. Please install it first:"
    echo "npm install -g wrangler"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "wrangler.toml" ]; then
    echo "❌ wrangler.toml not found. Please run this script from the serverless-api directory."
    exit 1
fi

# Build the project
echo "🔨 Building project..."
npm run build

# Deploy to v1.1.0 environment
echo "🌐 Deploying to v1.1.0 environment..."
wrangler deploy --env v1_1_0

echo "✅ Deployment completed!"
echo ""
echo "📍 New API endpoint: https://finpin-api-v1-1-0.jaffron.workers.dev"
echo "🔗 Custom domain: https://v110.finpin.app (configure DNS manually)"
echo ""
echo "🔧 Next steps:"
echo "1. Configure DNS for v110.finpin.app to point to the worker"
echo "2. Test the new endpoint"
echo "3. Update client applications to use the new endpoint"
echo ""
echo "🧪 Test commands:"
echo "curl https://finpin-api-v1-1-0.jaffron.workers.dev/health"
echo "curl https://v110.finpin.app/health"
