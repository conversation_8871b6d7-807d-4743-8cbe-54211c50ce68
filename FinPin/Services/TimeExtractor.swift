import Foundation

// MARK: - Time Extraction Utilities
class TimeExtractor {
    static let shared = TimeExtractor()
    
    private let timePatterns: [NSRegularExpression] = {
        let patterns = [
            // 中文时间格式
            "(?:打印时间|创单时间|交易时间|消费时间|时间)[:：]?\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 英文时间格式
            "(?:Time|Date|Transaction Time|Print Time)[:：]?\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 纯时间格式 YYYY-MM-DD HH:mm:ss
            "(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 带AM/PM的时间
            "(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?\\s*(?:AM|PM|am|pm))",
            // ISO格式但不带时区
            "(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2})",
        ]
        
        return patterns.compactMap { pattern in
            try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
        }
    }()
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()
    
    func extractLocalTime(from text: String) -> Date? {
        print("🕐 TimeExtractor: Extracting time from text...")
        
        for pattern in timePatterns {
            let range = NSRange(text.startIndex..<text.endIndex, in: text)
            let matches = pattern.matches(in: text, options: [], range: range)
            
            for match in matches {
                if match.numberOfRanges > 1 {
                    let timeRange = match.range(at: 1)
                    if let swiftRange = Range(timeRange, in: text) {
                        let timeString = String(text[swiftRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                        print("🕐 TimeExtractor: Found time string: '\(timeString)'")
                        
                        if let date = parseTimeString(timeString) {
                            print("✅ TimeExtractor: Successfully parsed local time: \(date)")
                            return date
                        }
                    }
                }
            }
        }
        
        print("❌ TimeExtractor: No valid time found in text")
        return nil
    }
    
    private func parseTimeString(_ timeString: String) -> Date? {
        let formats = [
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss a",
            "yyyy/MM/dd HH:mm:ss a"
        ]
        
        for format in formats {
            dateFormatter.dateFormat = format
            if let date = dateFormatter.date(from: timeString) {
                return date
            }
        }
        
        return nil
    }
    
    func formatLocalTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }
}
