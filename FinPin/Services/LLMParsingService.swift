import Foundation
import Vision
import UIKit
import CoreLocation

// MARK: - LLM Parsing Service
@MainActor
class LLMParsingService: ObservableObject {
    static let shared = LLMParsingService()
    
    private let apiClient = APIClient.shared
    @Published var isProcessing = false
    @Published var lastError: String?
    
    private init() {}
    
    // MARK: - Parse Image
    func parseExpenseFromImage(_ image: UIImage, location: CLLocation? = nil) async throws -> ExpenseRecord {
        isProcessing = true
        lastError = nil
        
        defer {
            isProcessing = false
        }
        
        do {
            // Step 1: Extract text from image using Vision
            let extractedText = try await extractTextFromImage(image)
            
            guard !extractedText.isEmpty else {
                throw LLMParsingError.noTextFound
            }
            
            // Step 2: Parse text using LLM API
            let parsedExpense = try await parseTextWithLLM(
                text: extractedText,
                image: image,
                location: location
            )
            
            // Step 3: Convert to ExpenseRecord
            return try convertToExpenseRecord(parsedExpense, originalText: extractedText)
            
        } catch {
            lastError = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Parse Text Directly
    func parseExpenseFromText(_ text: String, location: CLLocation? = nil) async throws -> ExpenseRecord {
        isProcessing = true
        lastError = nil
        
        defer {
            isProcessing = false
        }
        
        do {
            let parsedExpense = try await parseTextWithLLM(
                text: text,
                image: nil,
                location: location
            )
            
            return try convertToExpenseRecord(parsedExpense, originalText: text)
            
        } catch {
            lastError = error.localizedDescription
            throw error
        }
    }
    
    // MARK: - Private Methods
    
    private func extractTextFromImage(_ image: UIImage) async throws -> String {
        return try await withCheckedThrowingContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(throwing: LLMParsingError.invalidImage)
                return
            }
            
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }
                
                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(throwing: LLMParsingError.textRecognitionFailed)
                    return
                }
                
                let recognizedText = observations.compactMap { observation in
                    observation.topCandidates(1).first?.string
                }.joined(separator: "\n")
                
                continuation.resume(returning: recognizedText)
            }
            
            // Configure text recognition for better accuracy
            request.recognitionLevel = .accurate
            request.usesLanguageCorrection = true
            request.recognitionLanguages = ["zh-Hans", "zh-Hant", "en-US"]
            
            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            
            do {
                try handler.perform([request])
            } catch {
                continuation.resume(throwing: error)
            }
        }
    }
    
    private func parseTextWithLLM(
        text: String,
        image: UIImage?,
        location: CLLocation?
    ) async throws -> ExpenseParseResponse.ParsedExpense {
        
        // Prepare context
        var context: ExpenseParseRequest.ParseContext?
        
        if location != nil || image != nil {
            context = ExpenseParseRequest.ParseContext(
                location: location.map { "\($0.coordinate.latitude),\($0.coordinate.longitude)" },
                timestamp: ISO8601DateFormatter().string(from: Date()),
                imageMetadata: image.map { img in
                    ExpenseParseRequest.ParseContext.ImageMetadata(
                        width: Int(img.size.width),
                        height: Int(img.size.height),
                        format: "UIImage"
                    )
                }
            )
        }
        
        // Call API
        return try await apiClient.parseExpense(text: text, context: context)
    }
    
    private func convertToExpenseRecord(
        _ parsedExpense: ExpenseParseResponse.ParsedExpense,
        originalText: String
    ) throws -> ExpenseRecord {

        // Convert amount string to Decimal
        guard let amount = Decimal(string: parsedExpense.amount) else {
            throw LLMParsingError.invalidAmount(parsedExpense.amount)
        }

        // Priority 1: Extract time using regex from original text (preserves local time)
        var date = Date() // Current time as record creation time
        var transactionDate: Date? = nil // Actual transaction time from receipt

        if let regexExtractedTime = TimeExtractor.shared.extractLocalTime(from: originalText) {
            print("✅ LLMParsingService: Using regex-extracted local time: \(TimeExtractor.shared.formatLocalTime(regexExtractedTime))")
            transactionDate = regexExtractedTime
        } else if let timestampString = parsedExpense.timestamp {
            print("🕐 LLMParsingService: Fallback to LLM timestamp: \(timestampString)")

            // Try to parse as local time first (without timezone)
            let localFormatter = DateFormatter()
            localFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            localFormatter.timeZone = TimeZone.current

            if let localTime = localFormatter.date(from: timestampString.replacingOccurrences(of: "Z", with: "")) {
                print("✅ LLMParsingService: Parsed LLM timestamp as local time: \(TimeExtractor.shared.formatLocalTime(localTime))")
                transactionDate = localTime
            } else {
                // Fallback to ISO8601 parsing
                let formatter = ISO8601DateFormatter()
                transactionDate = formatter.date(from: timestampString)
                print("🕐 LLMParsingService: Parsed as ISO8601: \(transactionDate?.description ?? "nil")")
            }
        } else {
            print("❌ LLMParsingService: No timestamp found in API response or original text")
        }
        
        // Extract tags from extensions
        let tags = parsedExpense.extensions?.tags ?? []
        
        // Create ExpenseRecord
        let expenseRecord = ExpenseRecord(
            amount: amount,
            currency: parsedExpense.currency,
            merchant: parsedExpense.merchant,
            date: date,
            transactionDate: transactionDate,
            locationName: parsedExpense.location,
            tags: tags,
            paymentMethod: parsedExpense.paymentMethod,
            paymentCard: parsedExpense.paymentCard,
            notes: parsedExpense.extensions?.description,
            rawText: originalText
        )
        
        print("🕐 LLMParsingService: Created ExpenseRecord:")
        print("   - date (record creation): \(date)")
        print("   - transactionDate (actual): \(transactionDate?.description ?? "nil")")
        print("   - effectiveDate: \(expenseRecord.effectiveDate)")
        
        return expenseRecord
    }
}

// MARK: - LLM Parsing Errors
enum LLMParsingError: Error, LocalizedError {
    case invalidImage
    case noTextFound
    case textRecognitionFailed
    case invalidAmount(String)
    case lowConfidence(Double)
    case apiError(String)
    
    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "无法处理图片"
        case .noTextFound:
            return "图片中未找到文本"
        case .textRecognitionFailed:
            return "文本识别失败"
        case .invalidAmount(let amount):
            return "无法解析金额: \(amount)"
        case .lowConfidence(let confidence):
            return "解析置信度过低: \(String(format: "%.1f%%", confidence * 100))"
        case .apiError(let message):
            return "API错误: \(message)"
        }
    }
}

// MARK: - Parsing Result
struct ParsingResult {
    let expense: ExpenseRecord
    let confidence: Double
    let processingTime: TimeInterval
    let source: ParsingSource
    
    enum ParsingSource {
        case image
        case text
        case debug
    }
}

// MARK: - Extensions
extension LLMParsingService {
    
    // Validate parsing result
    func validateParsingResult(_ parsedExpense: ExpenseParseResponse.ParsedExpense) throws {
        // Check confidence threshold
        if parsedExpense.confidence < 0.5 {
            throw LLMParsingError.lowConfidence(parsedExpense.confidence)
        }
        
        // Validate amount
        guard let _ = Decimal(string: parsedExpense.amount) else {
            throw LLMParsingError.invalidAmount(parsedExpense.amount)
        }
        
        // Validate currency
        let validCurrencies = ["USD", "CNY", "EUR", "GBP", "JPY", "HKD", "TWD", "KRW"]
        if !validCurrencies.contains(parsedExpense.currency) {
            print("Warning: Unusual currency code: \(parsedExpense.currency)")
        }
    }
    
    // Get parsing statistics
    func getParsingStats() -> [String: Any] {
        return [
            "api_registered": apiClient.isRegistered,
            "api_loading": apiClient.isLoading,
            "service_processing": isProcessing,
            "last_error": lastError ?? "none"
        ]
    }
}
