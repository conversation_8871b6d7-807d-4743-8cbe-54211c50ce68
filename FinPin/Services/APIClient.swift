import Foundation
import CryptoKit
import UIKit

// MARK: - API Configuration
struct APIConfig {
    static let defaultBaseURL = "https://api.finpin.app"
    static let apiVersion = "v1"
    static let requestTimeout: TimeInterval = 30
    static let maxRetries = 3

    // Custom endpoint support
    private static var customBaseURL: String?

    static var baseURL: String {
        return customBaseURL ?? defaultBaseURL
    }

    static var fullBaseURL: String {
        return "\(baseURL)/api/\(apiVersion)"
    }

    static func setCustomBaseURL(_ url: String?) {
        customBaseURL = url
    }

    static func resetToDefault() {
        customBaseURL = nil
    }

    static var isUsingCustomEndpoint: Bool {
        return customBaseURL != nil
    }
}

// MARK: - API Provider Adapters
protocol APIProviderAdapter {
    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext?, configuration: APIConfiguration) async throws -> ExpenseParseResponse.ParsedExpense
    func createRequest(for configuration: APIConfiguration, text: String, context: ExpenseParseRequest.ParseContext?) throws -> URLRequest
}

// MARK: - FinPin API Adapter
class FinPinAPIAdapter: APIProviderAdapter {
    private let security = SecurityManager()

    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext?, configuration: APIConfiguration) async throws -> ExpenseParseResponse.ParsedExpense {
        // Use existing FinPin API logic
        var parseContext = context ?? ExpenseParseRequest.ParseContext(location: nil, timestamp: nil, imageMetadata: nil, timezoneOffset: nil)
        let timezoneOffset = TimeZone.current.secondsFromGMT() / 3600
        parseContext = ExpenseParseRequest.ParseContext(location: parseContext.location, timestamp: parseContext.timestamp, imageMetadata: parseContext.imageMetadata, timezoneOffset: timezoneOffset)

        let request = ExpenseParseRequest(text: text, context: parseContext)

        // This will be handled by the main APIClient performRequest method
        throw APIError.serverError("Should be handled by main client")
    }

    func createRequest(for configuration: APIConfiguration, text: String, context: ExpenseParseRequest.ParseContext?) throws -> URLRequest {
        guard let url = URL(string: "\(configuration.baseURL)/api/v1/parse/expense") else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        var parseContext = context ?? ExpenseParseRequest.ParseContext(location: nil, timestamp: nil, imageMetadata: nil, timezoneOffset: nil)
        let timezoneOffset = TimeZone.current.secondsFromGMT() / 3600
        parseContext = ExpenseParseRequest.ParseContext(location: parseContext.location, timestamp: parseContext.timestamp, imageMetadata: parseContext.imageMetadata, timezoneOffset: timezoneOffset)

        let requestBody = ExpenseParseRequest(text: text, context: parseContext)
        let bodyData = try JSONEncoder().encode(requestBody)
        request.httpBody = bodyData

        return request
    }
}

// MARK: - OpenAI API Adapter
class OpenAIAPIAdapter: APIProviderAdapter {
    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext?, configuration: APIConfiguration) async throws -> ExpenseParseResponse.ParsedExpense {
        let request = try createRequest(for: configuration, text: text, context: context)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(URLError(.badServerResponse))
        }

        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw APIError.serverError("HTTP \(httpResponse.statusCode): \(errorMessage)")
        }

        // Parse OpenAI response and convert to FinPin format
        let openAIResponse = try JSONDecoder().decode(OpenAIResponse.self, from: data)
        return try parseOpenAIResponse(openAIResponse, originalText: text)
    }

    func createRequest(for configuration: APIConfiguration, text: String, context: ExpenseParseRequest.ParseContext?) throws -> URLRequest {
        guard let url = URL(string: "\(configuration.baseURL)/chat/completions") else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(configuration.apiKey)", forHTTPHeaderField: "Authorization")

        let systemPrompt = createExpenseParsingPrompt()
        let userPrompt = createUserPrompt(text: text, context: context)

        let requestBody = OpenAIRequest(
            model: configuration.modelName ?? "gpt-4",
            messages: [
                OpenAIMessage(role: "system", content: systemPrompt),
                OpenAIMessage(role: "user", content: userPrompt)
            ],
            temperature: configuration.temperature ?? 0.7,
            max_tokens: configuration.maxTokens ?? 4096
        )

        let bodyData = try JSONEncoder().encode(requestBody)
        request.httpBody = bodyData

        return request
    }

    private func createExpenseParsingPrompt() -> String {
        return """
        You are an expense parsing assistant. Parse the given text and extract expense information in JSON format.

        Return a JSON object with the following structure:
        {
            "amount": "string (numeric value only, no currency symbols)",
            "currency": "string (3-letter currency code like CNY, USD, EUR)",
            "merchant": "string (store/business name)",
            "payment_method": "string (payment method like 支付宝, 微信支付, 现金, 银行卡)",
            "payment_card": "string (card number if visible, optional)",
            "location": "string (city or location name, optional)",
            "timestamp": "string (IMPORTANT: Use EXACT format from receipt WITHOUT timezone, e.g., '2025-08-16T17:20:51')",
            "confidence": number (0-1),
            "extensions": {
                "category": "string (Food & Beverage, Transportation, Shopping, etc.)",
                "tags": ["array of relevant tags"],
                "description": "string (brief description of the purchase)"
            }
        }

        CRITICAL TIMESTAMP RULES:
        1. Extract the EXACT time from the receipt text (打印时间, 创单时间, 交易时间, etc.)
        2. Use the EXACT format found in the text, do NOT add timezone suffixes like 'Z' or '+08:00'
        3. If time format is "2025-08-16 17:20:51", return "2025-08-16T17:20:51" (add T but NO timezone)
        4. The time represents LOCAL time where the transaction occurred
        5. Do NOT convert to UTC or add any timezone information

        If you cannot parse the expense information, return:
        {
            "error": "Unable to parse expense information",
            "confidence": 0
        }
        """
    }

    private func createUserPrompt(text: String, context: ExpenseParseRequest.ParseContext?) -> String {
        var prompt = "Parse this expense: \(text)"

        if let context = context {
            if let location = context.location {
                prompt += "\nLocation: \(location)"
            }
            if let timestamp = context.timestamp {
                prompt += "\nTimestamp: \(timestamp)"
            }
        }

        return prompt
    }

    private func parseOpenAIResponse(_ response: OpenAIResponse, originalText: String) throws -> ExpenseParseResponse.ParsedExpense {
        guard let choice = response.choices.first,
              let content = choice.message.content else {
            throw APIError.decodingError(NSError(domain: "OpenAI", code: 0, userInfo: [NSLocalizedDescriptionKey: "No response content"]))
        }

        // Parse JSON from OpenAI response
        guard let jsonData = content.data(using: .utf8) else {
            throw APIError.decodingError(NSError(domain: "OpenAI", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON"]))
        }

        let parsedData = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any]
        guard let parsedData = parsedData else {
            throw APIError.decodingError(NSError(domain: "OpenAI", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON structure"]))
        }

        // Check for error
        if let error = parsedData["error"] as? String {
            throw APIError.serverError(error)
        }

        // Extract expense data
        guard let amount = parsedData["amount"] as? Double,
              let currency = parsedData["currency"] as? String,
              let description = parsedData["description"] as? String,
              let category = parsedData["category"] as? String else {
            throw APIError.decodingError(NSError(domain: "OpenAI", code: 0, userInfo: [NSLocalizedDescriptionKey: "Missing required fields"]))
        }

        let merchant = parsedData["merchant"] as? String
        let timestamp = parsedData["timestamp"] as? String
        let confidence = parsedData["confidence"] as? Double ?? 0.8

        return ExpenseParseResponse.ParsedExpense(
            amount: amount,
            currency: currency,
            description: description,
            category: category,
            merchant: merchant,
            timestamp: timestamp,
            confidence: confidence,
            originalText: originalText
        )
    }
}

// MARK: - Anthropic API Adapter
class AnthropicAPIAdapter: APIProviderAdapter {
    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext?, configuration: APIConfiguration) async throws -> ExpenseParseResponse.ParsedExpense {
        let request = try createRequest(for: configuration, text: text, context: context)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw APIError.networkError(URLError(.badServerResponse))
        }

        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw APIError.serverError("HTTP \(httpResponse.statusCode): \(errorMessage)")
        }

        // Parse Anthropic response and convert to FinPin format
        let anthropicResponse = try JSONDecoder().decode(AnthropicResponse.self, from: data)
        return try parseAnthropicResponse(anthropicResponse, originalText: text)
    }

    func createRequest(for configuration: APIConfiguration, text: String, context: ExpenseParseRequest.ParseContext?) throws -> URLRequest {
        guard let url = URL(string: "\(configuration.baseURL)/messages") else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(configuration.apiKey, forHTTPHeaderField: "x-api-key")
        request.setValue("2023-06-01", forHTTPHeaderField: "anthropic-version")

        let systemPrompt = createExpenseParsingPrompt()
        let userPrompt = createUserPrompt(text: text, context: context)

        let requestBody = AnthropicRequest(
            model: configuration.modelName ?? "claude-3-5-sonnet-20241022",
            max_tokens: configuration.maxTokens ?? 4096,
            temperature: configuration.temperature ?? 0.7,
            system: systemPrompt,
            messages: [
                AnthropicMessage(role: "user", content: userPrompt)
            ]
        )

        let bodyData = try JSONEncoder().encode(requestBody)
        request.httpBody = bodyData

        return request
    }

    private func createExpenseParsingPrompt() -> String {
        return """
        You are an expense parsing assistant. Parse the given text and extract expense information in JSON format.

        Return a JSON object with the following structure:
        {
            "amount": "string (numeric value only, no currency symbols)",
            "currency": "string (3-letter currency code like CNY, USD, EUR)",
            "merchant": "string (store/business name)",
            "payment_method": "string (payment method like 支付宝, 微信支付, 现金, 银行卡)",
            "payment_card": "string (card number if visible, optional)",
            "location": "string (city or location name, optional)",
            "timestamp": "string (IMPORTANT: Use EXACT format from receipt WITHOUT timezone, e.g., '2025-08-16T17:20:51')",
            "confidence": number (0-1),
            "extensions": {
                "category": "string (Food & Beverage, Transportation, Shopping, etc.)",
                "tags": ["array of relevant tags"],
                "description": "string (brief description of the purchase)"
            }
        }

        CRITICAL TIMESTAMP RULES:
        1. Extract the EXACT time from the receipt text (打印时间, 创单时间, 交易时间, etc.)
        2. Use the EXACT format found in the text, do NOT add timezone suffixes like 'Z' or '+08:00'
        3. If time format is "2025-08-16 17:20:51", return "2025-08-16T17:20:51" (add T but NO timezone)
        4. The time represents LOCAL time where the transaction occurred
        5. Do NOT convert to UTC or add any timezone information

        If you cannot parse the expense information, return:
        {
            "error": "Unable to parse expense information",
            "confidence": 0
        }
        """
    }

    private func createUserPrompt(text: String, context: ExpenseParseRequest.ParseContext?) -> String {
        var prompt = "Parse this expense: \(text)"

        if let context = context {
            if let location = context.location {
                prompt += "\nLocation: \(location)"
            }
            if let timestamp = context.timestamp {
                prompt += "\nTimestamp: \(timestamp)"
            }
        }

        return prompt
    }

    private func parseAnthropicResponse(_ response: AnthropicResponse, originalText: String) throws -> ExpenseParseResponse.ParsedExpense {
        guard let content = response.content.first?.text else {
            throw APIError.decodingError(NSError(domain: "Anthropic", code: 0, userInfo: [NSLocalizedDescriptionKey: "No response content"]))
        }

        // Parse JSON from Anthropic response
        guard let jsonData = content.data(using: .utf8) else {
            throw APIError.decodingError(NSError(domain: "Anthropic", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON"]))
        }

        let parsedData = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any]
        guard let parsedData = parsedData else {
            throw APIError.decodingError(NSError(domain: "Anthropic", code: 0, userInfo: [NSLocalizedDescriptionKey: "Invalid JSON structure"]))
        }

        // Check for error
        if let error = parsedData["error"] as? String {
            throw APIError.serverError(error)
        }

        // Extract expense data
        guard let amount = parsedData["amount"] as? Double,
              let currency = parsedData["currency"] as? String,
              let description = parsedData["description"] as? String,
              let category = parsedData["category"] as? String else {
            throw APIError.decodingError(NSError(domain: "Anthropic", code: 0, userInfo: [NSLocalizedDescriptionKey: "Missing required fields"]))
        }

        let merchant = parsedData["merchant"] as? String
        let timestamp = parsedData["timestamp"] as? String
        let confidence = parsedData["confidence"] as? Double ?? 0.8

        return ExpenseParseResponse.ParsedExpense(
            amount: amount,
            currency: currency,
            description: description,
            category: category,
            merchant: merchant,
            timestamp: timestamp,
            confidence: confidence,
            originalText: originalText
        )
    }
}

// MARK: - OpenAI API Models
struct OpenAIRequest: Codable {
    let model: String
    let messages: [OpenAIMessage]
    let temperature: Double
    let max_tokens: Int

    enum CodingKeys: String, CodingKey {
        case model, messages, temperature
        case max_tokens = "max_tokens"
    }
}

struct OpenAIMessage: Codable {
    let role: String
    let content: String
}

struct OpenAIResponse: Codable {
    let choices: [OpenAIChoice]
}

struct OpenAIChoice: Codable {
    let message: OpenAIMessage
}

// MARK: - Anthropic API Models
struct AnthropicRequest: Codable {
    let model: String
    let max_tokens: Int
    let temperature: Double
    let system: String
    let messages: [AnthropicMessage]

    enum CodingKeys: String, CodingKey {
        case model, system, messages, temperature
        case max_tokens = "max_tokens"
    }
}

struct AnthropicMessage: Codable {
    let role: String
    let content: String
}

struct AnthropicResponse: Codable {
    let content: [AnthropicContent]
}

struct AnthropicContent: Codable {
    let text: String
}

// MARK: - FinPin API Models
struct DeviceRegistrationRequest: Codable {
    let deviceId: String
    let deviceInfo: DeviceInfo
    
    enum CodingKeys: String, CodingKey {
        case deviceId = "device_id"
        case deviceInfo = "device_info"
    }
    
    struct DeviceInfo: Codable {
        let model: String
        let osVersion: String
        let appVersion: String
        let platform: String
        
        enum CodingKeys: String, CodingKey {
            case model
            case osVersion = "os_version"
            case appVersion = "app_version"
            case platform
        }
    }
}

struct DeviceRegistrationResponse: Codable {
    let success: Bool
    let data: RegistrationData?
    let error: String?
    
    struct RegistrationData: Codable {
        let keySeed: String
        let expiresAt: String
        let deviceToken: String
        
        enum CodingKeys: String, CodingKey {
            case keySeed = "key_seed"
            case expiresAt = "expires_at"
            case deviceToken = "device_token"
        }
    }
}

struct ExpenseParseRequest: Codable {
    let text: String
    let context: ParseContext?
    
    struct ParseContext: Codable {
        let location: String?
        let timestamp: String?
        let imageMetadata: ImageMetadata?
        let timezoneOffset: Int? // in hours from UTC
        
        enum CodingKeys: String, CodingKey {
            case location
            case timestamp
            case imageMetadata = "image_metadata"
            case timezoneOffset = "timezone_offset"
        }
        
        struct ImageMetadata: Codable {
            let width: Int
            let height: Int
            let format: String
        }
    }
}

struct ExpenseParseResponse: Codable {
    let success: Bool
    let data: ParsedExpense?
    let error: String?
    
    struct ParsedExpense: Codable {
        let amount: String
        let currency: String
        let merchant: String?
        let paymentMethod: String?
        let paymentCard: String?
        let location: String?
        let timestamp: String?
        let confidence: Double
        let extensions: Extensions?
        
        enum CodingKeys: String, CodingKey {
            case amount
            case currency
            case merchant
            case paymentMethod = "payment_method"
            case paymentCard = "payment_card"
            case location
            case timestamp
            case confidence
            case extensions
        }
        
        struct Extensions: Codable {
            let category: String?
            let tags: [String]?
            let description: String?
        }
    }
}

// MARK: - API Errors
enum APIError: Error, LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(String)
    case authenticationError(String)
    case rateLimitExceeded
    case deviceNotRegistered
    
    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "Invalid API URL"
        case .noData:
            return "No data received from server"
        case .decodingError(let error):
            return "Failed to decode response: \(error.localizedDescription)"
        case .networkError(let error):
            return "Network error: \(error.localizedDescription)"
        case .serverError(let message):
            return "Server error: \(message)"
        case .authenticationError(let message):
            return "Authentication error: \(message)"
        case .rateLimitExceeded:
            return "Rate limit exceeded. Please try again later."
        case .deviceNotRegistered:
            return "Device not registered. Please restart the app."
        }
    }
}

// MARK: - Security Manager
class SecurityManager {
    private let keychain = KeychainManager()
    
    func getDeviceId() -> String {
        if let existingId = keychain.getString(for: "device_id") {
            return existingId
        }
        
        let newId = generateDeviceId()
        keychain.setString(newId, for: "device_id")
        return newId
    }
    
    private func generateDeviceId() -> String {
        let device = UIDevice.current
        let identifierData = "\(device.model)-\(device.systemVersion)-\(Bundle.main.bundleIdentifier ?? "")"
        
        let data = Data(identifierData.utf8)
        let hash = SHA256.hash(data: data)
        return hash.compactMap { String(format: "%02x", $0) }.joined()
    }
    
    func getKeySeed() -> String? {
        return keychain.getString(for: "key_seed")
    }
    
    func setKeySeed(_ keySeed: String) {
        keychain.setString(keySeed, for: "key_seed")
    }
    
    func generateSignature(deviceId: String, timestamp: String, requestBody: String) throws -> String {
        guard let keySeed = getKeySeed() else {
            throw APIError.deviceNotRegistered
        }
        
        let requestBodyHash = SHA256.hash(data: Data(requestBody.utf8))
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()
        
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)
        let keyData = Data(keySeed.utf8)
        
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: SymmetricKey(data: keyData))
        return Data(signature).base64EncodedString()
    }
}

// MARK: - Keychain Manager
class KeychainManager {
    func setString(_ value: String, for key: String) {
        let data = Data(value.utf8)

        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecValueData as String: data
        ]

        SecItemDelete(query as CFDictionary)
        SecItemAdd(query as CFDictionary, nil)
    }

    func getString(for key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]

        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)

        guard status == errSecSuccess,
              let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }

        return string
    }
}

// MARK: - API Client
@MainActor
class APIClient: ObservableObject {
    static let shared = APIClient()

    private let security = SecurityManager()
    private let session: URLSession
    private let configManager = APIConfigurationManager.shared

    // API Adapters
    private let finpinAdapter = FinPinAPIAdapter()
    private let openaiAdapter = OpenAIAPIAdapter()
    private let anthropicAdapter = AnthropicAPIAdapter()

    @Published var isRegistered = false
    @Published var isLoading = false

    private init() {
        let config = URLSessionConfiguration.default
        config.timeoutIntervalForRequest = APIConfig.requestTimeout
        config.timeoutIntervalForResource = APIConfig.requestTimeout * 2
        self.session = URLSession(configuration: config)

        // Check if device is already registered (only for FinPin)
        // Support migration from old registration systems
        self.isRegistered = checkDeviceRegistrationStatus()

        // Update legacy settings if needed
        migrateLegacySettings()
    }

    private func checkDeviceRegistrationStatus() -> Bool {
        // Check new keychain-based registration
        if security.getKeySeed() != nil {
            print("✅ Device registered via keychain system")
            return true
        }

        // Check legacy UserDefaults registration and migrate
        if let legacyDeviceId = UserDefaults.standard.string(forKey: "debug_device_id"),
           let legacyKeySeed = UserDefaults.standard.string(forKey: "debug_key_seed") {
            print("🔄 Migrating legacy device registration...")

            // Migrate to keychain
            security.setKeySeed(legacyKeySeed)

            // Clean up old UserDefaults
            UserDefaults.standard.removeObject(forKey: "debug_device_id")
            UserDefaults.standard.removeObject(forKey: "debug_key_seed")

            print("✅ Legacy device registration migrated to keychain")
            return true
        }

        print("❌ Device not registered in any system")
        return false
    }

    private func migrateLegacySettings() {
        // Migrate old custom endpoint settings to new configuration system
        let isUsingCustomEndpoint = UserDefaults.standard.bool(forKey: "isUsingCustomEndpoint")
        let customEndpoint = UserDefaults.standard.string(forKey: "customAPIEndpoint") ?? ""

        if isUsingCustomEndpoint && !customEndpoint.isEmpty {
            // Check if we already have this configuration
            let existingConfigs = configManager.configurations
            let hasLegacyConfig = existingConfigs.contains { config in
                config.baseURL == customEndpoint && config.provider == .finpin
            }

            if !hasLegacyConfig {
                // Create a legacy FinPin configuration
                let legacyConfig = APIConfiguration(
                    name: "Legacy Custom Endpoint",
                    provider: .finpin,
                    baseURL: customEndpoint,
                    isActive: true
                )
                configManager.addConfiguration(legacyConfig)
                configManager.setMode(.custom)
            }

            // Clear old settings
            UserDefaults.standard.removeObject(forKey: "isUsingCustomEndpoint")
            UserDefaults.standard.removeObject(forKey: "customAPIEndpoint")
        }
    }

    // MARK: - Device Registration
    func registerDevice() async throws {
        // Only register for FinPin provider
        guard let activeConfig = configManager.activeConfiguration,
              activeConfig.provider == .finpin else {
            throw APIError.serverError("Device registration only available for FinPin provider")
        }

        isLoading = true
        defer { isLoading = false }

        let deviceId = security.getDeviceId()
        let device = UIDevice.current

        let request = DeviceRegistrationRequest(
            deviceId: deviceId,
            deviceInfo: DeviceRegistrationRequest.DeviceInfo(
                model: device.model,
                osVersion: device.systemVersion,
                appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                platform: "ios"
            )
        )

        let response: DeviceRegistrationResponse = try await performFinPinRequest(
            endpoint: "/device/register",
            method: "POST",
            body: request,
            requiresAuth: false,
            configuration: activeConfig
        )

        guard response.success, let data = response.data else {
            throw APIError.serverError(response.error ?? "Registration failed")
        }

        security.setKeySeed(data.keySeed)
        isRegistered = true
    }

    // MARK: - Expense Parsing
    func parseExpense(text: String, context: ExpenseParseRequest.ParseContext? = nil) async throws -> ExpenseParseResponse.ParsedExpense {
        isLoading = true
        defer { isLoading = false }

        // Check if custom mode is enabled and has active configuration
        if configManager.isCustomModeEnabled, let activeConfig = configManager.activeConfiguration {
            print("DEBUG: Using custom API configuration: \(activeConfig.name) (\(activeConfig.provider.displayName))")

            // Handle different providers
            switch activeConfig.provider {
            case .finpin:
                return try await parseExpenseWithFinPin(text: text, context: context, configuration: activeConfig)
            case .openai:
                return try await openaiAdapter.parseExpense(text: text, context: context, configuration: activeConfig)
            case .anthropic:
                return try await anthropicAdapter.parseExpense(text: text, context: context, configuration: activeConfig)
            }
        } else {
            // Fall back to default FinPin hosted mode
            print("DEBUG: Using default FinPin hosted mode")
            let defaultConfig = APIConfiguration.defaultFinPin()
            return try await parseExpenseWithFinPin(text: text, context: context, configuration: defaultConfig)
        }
    }

    private func parseExpenseWithFinPin(text: String, context: ExpenseParseRequest.ParseContext?, configuration: APIConfiguration) async throws -> ExpenseParseResponse.ParsedExpense {
        // Only register device for FinPin
        if !isRegistered {
            try await registerDevice()
        }

        var parseContext = context ?? ExpenseParseRequest.ParseContext(location: nil, timestamp: nil, imageMetadata: nil, timezoneOffset: nil)
        let timezoneOffset = TimeZone.current.secondsFromGMT() / 3600
        parseContext = ExpenseParseRequest.ParseContext(location: parseContext.location, timestamp: parseContext.timestamp, imageMetadata: parseContext.imageMetadata, timezoneOffset: timezoneOffset)
        let request = ExpenseParseRequest(text: text, context: parseContext)

        let response: ExpenseParseResponse = try await performFinPinRequest(
            endpoint: "/parse/expense",
            method: "POST",
            body: request,
            requiresAuth: true,
            configuration: configuration
        )

        guard response.success, let data = response.data else {
            throw APIError.serverError(response.error ?? "Parsing failed")
        }

        print("DEBUG: Parsed timestamp: \(data.timestamp ?? \"nil\")")
        return data
    }

    // MARK: - Configuration Management
    var currentEndpoint: String {
        return configManager.getActiveEndpoint()
    }

    var isUsingCustomEndpoint: Bool {
        return configManager.isCustomModeEnabled
    }

    var isSignatureVerificationDisabled: Bool {
        return configManager.isSignatureVerificationDisabled
    }

    // MARK: - FinPin Request Method
    private func performFinPinRequest<T: Codable, R: Codable>(
        endpoint: String,
        method: String,
        body: T? = nil,
        requiresAuth: Bool = false,
        configuration: APIConfiguration
    ) async throws -> R {

        guard let url = URL(string: "\(configuration.baseURL)/api/v1\(endpoint)") else {
            throw APIError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        // Add request body if provided
        var requestBodyString = ""
        if let body = body {
            let bodyData = try JSONEncoder().encode(body)
            request.httpBody = bodyData
            requestBodyString = String(data: bodyData, encoding: .utf8) ?? ""
        }

        // Add authentication headers if required and signature verification is not disabled
        if requiresAuth && !isSignatureVerificationDisabled {
            let deviceId = security.getDeviceId()
            let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))

            let signature = try security.generateSignature(
                deviceId: deviceId,
                timestamp: timestamp,
                requestBody: requestBodyString
            )

            request.setValue(deviceId, forHTTPHeaderField: "x-device-id")
            request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
            request.setValue(signature, forHTTPHeaderField: "x-signature")
        }

        // Perform request with retry logic
        var lastError: Error?

        for attempt in 1...APIConfig.maxRetries {
            do {
                let (data, response) = try await session.data(for: request)

                guard let httpResponse = response as? HTTPURLResponse else {
                    throw APIError.networkError(URLError(.badServerResponse))
                }

                // Handle HTTP status codes
                switch httpResponse.statusCode {
                case 200...299:
                    // Print server response for debugging
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("🔍 SERVER RESPONSE JSON:")
                        print(responseString)
                        print("🔍 END SERVER RESPONSE")
                    }
                    break
                case 401:
                    // Authentication error - try to re-register
                    if requiresAuth && attempt == 1 {
                        try await registerDevice()
                        continue
                    }
                    throw APIError.authenticationError("Authentication failed")
                case 429:
                    throw APIError.rateLimitExceeded
                default:
                    let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
                    throw APIError.serverError("HTTP \(httpResponse.statusCode): \(errorMessage)")
                }

                // Decode response
                do {
                    let decoder = JSONDecoder()
                    return try decoder.decode(R.self, from: data)
                } catch {
                    throw APIError.decodingError(error)
                }

            } catch {
                lastError = error

                // Don't retry on certain errors
                if case APIError.authenticationError = error,
                   case APIError.rateLimitExceeded = error {
                    throw error
                }

                // Wait before retry (exponential backoff)
                if attempt < APIConfig.maxRetries {
                    let delay = pow(2.0, Double(attempt - 1))
                    try await Task.sleep(nanoseconds: UInt64(delay * 1_000_000_000))
                }
            }
        }

        throw lastError ?? APIError.networkError(URLError(.unknown))
    }
}
