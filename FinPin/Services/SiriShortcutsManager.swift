import Foundation
import Intents
import IntentsUI
import SwiftUI
import AppIntents

// MARK: - Siri Shortcuts Manager
@MainActor
class SiriShortcutsManager: ObservableObject {
    static let shared = SiriShortcutsManager()
    
    @Published var isEnabled = false
    @Published var availableShortcuts: [INShortcut] = []
    
    private init() {
        loadSettings()
        setupShortcuts()
    }
    
    // MARK: - Settings Management
    private func loadSettings() {
        isEnabled = UserDefaults.standard.bool(forKey: "siri_shortcuts_enabled")
    }
    
    func saveSettings() {
        UserDefaults.standard.set(isEnabled, forKey: "siri_shortcuts_enabled")
    }
    
    func toggleShortcuts(_ enabled: Bool) {
        isEnabled = enabled
        saveSettings()
        
        if enabled {
            setupShortcuts()
        } else {
            removeAllShortcuts()
        }
    }
    
    // MARK: - Shortcuts Setup
    private func setupShortcuts() {
        guard isEnabled else { return }
        
        // For iOS 16+, donate App Intents
        if #available(iOS 16.0, *) {
            donateAppIntents()
        }
        
        // Create "Add Expense" shortcut for legacy support
        let addExpenseShortcut = createAddExpenseShortcut()
        availableShortcuts = [addExpenseShortcut]
        
        // Donate shortcuts to Siri
        donateShortcuts()
    }
    
    @available(iOS 16.0, *)
    private func donateAppIntents() {
        Task {
            let intent = AddExpenseAppIntent()
            intent.expenseText = "Sample expense $10 at Starbucks"
            
            do {
                try await intent.donate()
                print("✅ Successfully donated AddExpenseAppIntent for Shortcuts discovery")
            } catch {
                print("❌ Failed to donate AddExpenseAppIntent: \(error)")
            }
        }
    }
    
    private func createAddExpenseShortcut() -> INShortcut {
        let intent = AddExpenseIntent()
        intent.suggestedInvocationPhrase = "Add expense"
        
        let shortcut = INShortcut(intent: intent)
        return shortcut
    }
    
    private func donateShortcuts() {
        for shortcut in availableShortcuts {
            if let intent = shortcut.intent {
                let interaction = INInteraction(intent: intent, response: nil)
                interaction.donate { error in
                    if let error = error {
                        print("❌ Failed to donate shortcut: \(error)")
                    } else {
                        print("✅ Successfully donated shortcut")
                    }
                }
            }
        }
    }
    
    private func removeAllShortcuts() {
        INInteraction.deleteAll { error in
            if let error = error {
                print("❌ Failed to remove shortcuts: \(error)")
            } else {
                print("✅ Successfully removed all shortcuts")
            }
        }
        availableShortcuts.removeAll()
    }
    
    // MARK: - Expense Processing
    func processExpenseFromSiri(text: String) async -> Bool {
        print("🎤 SiriShortcutsManager.processExpenseFromSiri called with: \(text)")
        
        do {
            // Ensure device is registered using unified system
            try await ensureDeviceRegistration()

            // Use the same CloudflareExpenseParser that the main app uses
            let parsedExpense = try await CloudflareExpenseParser.shared.parseExpense(from: text)
            
            // Debug: Log the payment method from server response
            print("💳 SiriShortcutsManager (Siri): Server returned payment method: \(parsedExpense.paymentMethod ?? "nil")")
            print("💳 SiriShortcutsManager (Siri): ParsedExpense details:")
            print("   - amount: \(parsedExpense.amount)")
            print("   - currency: \(parsedExpense.currency)")
            print("   - merchant: \(parsedExpense.merchant ?? "nil")")
            print("   - paymentMethod: \(parsedExpense.paymentMethod ?? "nil")")
            print("   - date: \(parsedExpense.date?.description ?? "nil")")
            print("   - transactionDate: \(parsedExpense.transactionDate?.description ?? "nil")")
            
            // Create expense record with parsed data
            let expense = ExpenseRecord(
                id: UUID(),
                amount: parsedExpense.amount,
                currency: parsedExpense.currency,
                merchant: parsedExpense.merchant,
                date: parsedExpense.date ?? Date(),
                transactionDate: parsedExpense.transactionDate,
                tags: parsedExpense.tags,
                paymentMethod: parsedExpense.paymentMethod,
                notes: parsedExpense.notes,
                rawText: text
            )
            
            // Debug: Log the final expense payment method and validate it's not hardcoded
            print("💳 SiriShortcutsManager (Siri): Final expense payment method: \(expense.paymentMethod ?? "nil")")
            if let pm = expense.paymentMethod, pm.contains("Siri") || pm.contains("Voice") {
                print("⚠️ WARNING: Payment method contains 'Siri' or 'Voice': \(pm)")
                print("⚠️ This suggests a fallback or hardcoded value is being used!")
            }
            
            // Save the expense
            await saveExpense(expense)
            
            return true
        } catch {
            print("❌ SiriShortcutsManager: CloudflareExpenseParser failed: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            
            // Fallback to basic parsing
            print("🔄 Falling back to basic parsing for Siri input")
            return await processFallbackExpense(text: text, source: "Siri")
        }
    }
    
    func processExpenseFromText(text: String) async -> Bool {
        print("📝 SiriShortcutsManager.processExpenseFromText called with: \(text)")
        
        do {
            // Ensure device is registered using unified system
            try await ensureDeviceRegistration()

            // Use the same CloudflareExpenseParser that the main app uses
            let parsedExpense = try await CloudflareExpenseParser.shared.parseExpense(from: text)
            
            // Debug: Log the payment method from server response
            print("💳 SiriShortcutsManager (Shortcuts): Server returned payment method: \(parsedExpense.paymentMethod ?? "nil")")
            print("💳 SiriShortcutsManager (Shortcuts): ParsedExpense details:")
            print("   - amount: \(parsedExpense.amount)")
            print("   - currency: \(parsedExpense.currency)")
            print("   - merchant: \(parsedExpense.merchant ?? "nil")")
            print("   - paymentMethod: \(parsedExpense.paymentMethod ?? "nil")")
            print("   - date: \(parsedExpense.date?.description ?? "nil")")
            print("   - transactionDate: \(parsedExpense.transactionDate?.description ?? "nil")")
            
            // Create expense record with parsed data
            let expense = ExpenseRecord(
                id: UUID(),
                amount: parsedExpense.amount,
                currency: parsedExpense.currency,
                merchant: parsedExpense.merchant,
                date: parsedExpense.date ?? Date(),
                transactionDate: parsedExpense.transactionDate,
                tags: parsedExpense.tags,
                paymentMethod: parsedExpense.paymentMethod,
                notes: parsedExpense.notes,
                rawText: text
            )
            
            // Debug: Log the final expense payment method and validate it's not hardcoded
            print("💳 SiriShortcutsManager (Shortcuts): Final expense payment method: \(expense.paymentMethod ?? "nil")")
            if let pm = expense.paymentMethod, pm.contains("Siri") || pm.contains("Voice") {
                print("⚠️ WARNING: Payment method contains 'Siri' or 'Voice': \(pm)")
                print("⚠️ This suggests a fallback or hardcoded value is being used!")
            }
            
            // Save the expense
            await saveExpense(expense)
            
            return true
        } catch {
            print("❌ SiriShortcutsManager: CloudflareExpenseParser failed: \(error)")
            print("❌ Error details: \(error.localizedDescription)")
            
            // Fallback to basic parsing
            print("🔄 Falling back to basic parsing for Shortcuts input")
            return await processFallbackExpense(text: text, source: "Shortcuts")
        }
    }
    
    // MARK: - Fallback Parsing
    private func processFallbackExpense(text: String, source: String) async -> Bool {
        print("🔄 Processing fallback expense from \(source): \(text)")
        
        // Simple local parsing for basic expense text
        var amount: Decimal = 0
        var merchant: String?
        var currency = "USD"
        
        // Extract amount and currency using regex
        let amountPattern = #"([\$\u00a3\u20ac\u00a5]?)([0-9]+\.?[0-9]*)"#
        let regex = try? NSRegularExpression(pattern: amountPattern, options: [])
        let range = NSRange(text.startIndex..<text.endIndex, in: text)
        
        if let match = regex?.firstMatch(in: text, options: [], range: range) {
            if let amountRange = Range(match.range(at: 2), in: text) {
                let amountString = String(text[amountRange])
                amount = Decimal(string: amountString) ?? 0
            }
            
            // Detect currency from symbol
            if let symbolRange = Range(match.range(at: 1), in: text) {
                let symbol = String(text[symbolRange])
                switch symbol {
                case "$": currency = "USD"
                case "£": currency = "GBP"
                case "€": currency = "EUR"
                case "¥": currency = "CNY"
                default: currency = "USD"
                }
            }
        }
        
        // Extract merchant name (simple heuristic)
        let words = text.split(separator: " ")
        for word in words {
            let cleanWord = String(word).trimmingCharacters(in: .punctuationCharacters)
            if !cleanWord.contains(where: { "0123456789$£€¥".contains($0) }) && cleanWord.count > 2 {
                merchant = cleanWord
                break
            }
        }
        
        let expense = ExpenseRecord(
            id: UUID(),
            amount: amount,
            currency: currency,
            merchant: merchant ?? "\(source) Entry",
            date: Date(),
            tags: [],
            paymentMethod: nil,
            notes: nil,
            rawText: text
        )
        
        await saveExpense(expense)
        print("✅ Fallback expense saved: \(amount) \(currency) at \(merchant ?? "Unknown")")
        
        return true
    }
    
    // MARK: - Device Registration
    private func ensureDeviceRegistration() async throws {
        print("🔄 SiriShortcutsManager: Ensuring device registration...")

        // Check if device is already registered using legacy UserDefaults
        let deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId != nil && keySeed != nil {
            print("✅ Device already registered for Siri Shortcuts")
            return
        }

        print("🔄 Device not registered, registering now...")

        let newDeviceId = UUID().uuidString

        let endpointManager = APIEndpointManager.shared
        let url = URL(string: endpointManager.getDeviceRegisterURL())!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-SiriShortcuts/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 30.0

        let requestBody: [String: Any] = [
            "device_id": newDeviceId,
            "device_info": [
                "model": "iOS Device",
                "os_version": "iOS 16+",
                "app_version": "1.0.0",
                "platform": "ios"
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NSError(domain: "RegistrationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Device registration failed"])
        }

        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]

        if let success = responseData?["success"] as? Bool, success,
           let data = responseData?["data"] as? [String: Any],
           let keySeed = data["key_seed"] as? String {

            UserDefaults.standard.set(newDeviceId, forKey: "debug_device_id")
            UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")

            print("✅ Device registered successfully for Siri Shortcuts")
        } else {
            throw NSError(domain: "RegistrationError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid registration response"])
        }
    }
        
        let jsonData = try JSONSerialization.data(withJSONObject: requestBody)
        
        // Determine API endpoint
        let isUsingCustomEndpoint = UserDefaults.standard.bool(forKey: "isUsingCustomEndpoint")
        let customEndpoint = UserDefaults.standard.string(forKey: "customAPIEndpoint") ?? ""
        
        var baseURL = "https://api.finpin.app"
        if isUsingCustomEndpoint && !customEndpoint.isEmpty {
            if let url = URL(string: customEndpoint), url.scheme == "https" {
                baseURL = customEndpoint
            }
        }
        
        let url = URL(string: "\(baseURL)/api/v1/device/register")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-Shortcuts/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 10.0
        request.httpBody = jsonData
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "SiriShortcutsManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid response type"])
        }
        
        guard httpResponse.statusCode == 200 else {
            let errorMessage = "HTTP \(httpResponse.statusCode): Device registration failed"
            throw NSError(domain: "SiriShortcutsManager", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
        }
        
        // Parse response
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let success = json["success"] as? Bool, success,
              let responseData = json["data"] as? [String: Any],
              let keySeed = responseData["key_seed"] as? String else {
            throw NSError(domain: "SiriShortcutsManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid registration response"])
        }
        
        // Store credentials
        UserDefaults.standard.set(deviceId, forKey: "debug_device_id")
        UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")
        
        if let authToken = responseData["device_token"] as? String {
            UserDefaults.standard.set(authToken, forKey: "debug_auth_token")
        }
        
        print("✅ SiriShortcutsManager: Device registered successfully with ID: \(deviceId.prefix(8))...")
    }
    
    private func saveExpense(_ expense: ExpenseRecord) async {
        // Save to DataManager
        let dataManager = DataManager.shared
        dataManager.addExpense(expense)
        print("✅ Expense saved from \(expense.paymentMethod ?? "Unknown"): \(expense.merchant ?? "Unknown") - \(expense.amount)")
    }
}
