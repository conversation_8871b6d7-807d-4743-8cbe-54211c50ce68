import XCTest
@testable import FinPin

class APIConfigurationTests: XCTestCase {
    
    var configManager: APIConfigurationManager!
    
    override func setUp() {
        super.setUp()
        // Use a fresh instance for testing
        configManager = APIConfigurationManager()
    }
    
    override func tearDown() {
        // Clean up UserDefaults after each test
        let userDefaults = UserDefaults.standard
        userDefaults.removeObject(forKey: "api_configurations")
        userDefaults.removeObject(forKey: "api_configuration_mode")
        userDefaults.removeObject(forKey: "active_api_configuration_id")
        super.tearDown()
    }
    
    // MARK: - API Provider Tests
    func testAPIProviderDisplayNames() {
        XCTAssertEqual(APIProvider.finpin.displayName, "FinPin (Hosted)")
        XCTAssertEqual(APIProvider.openai.displayName, "OpenAI")
        XCTAssertEqual(APIProvider.anthropic.displayName, "Anthropic")
    }
    
    func testAPIProviderDefaultURLs() {
        XCTAssertEqual(APIProvider.finpin.defaultBaseURL, "https://api.finpin.app")
        XCTAssertEqual(APIProvider.openai.defaultBaseURL, "https://api.openai.com/v1")
        XCTAssertEqual(APIProvider.anthropic.defaultBaseURL, "https://api.anthropic.com/v1")
    }
    
    func testAPIProviderRequiresAPIKey() {
        XCTAssertFalse(APIProvider.finpin.requiresAPIKey)
        XCTAssertTrue(APIProvider.openai.requiresAPIKey)
        XCTAssertTrue(APIProvider.anthropic.requiresAPIKey)
    }
    
    // MARK: - API Configuration Tests
    func testAPIConfigurationCreation() {
        let config = APIConfiguration.defaultOpenAI()
        
        XCTAssertEqual(config.name, "OpenAI GPT-4")
        XCTAssertEqual(config.provider, .openai)
        XCTAssertEqual(config.baseURL, "https://api.openai.com/v1")
        XCTAssertEqual(config.modelName, "gpt-4")
        XCTAssertEqual(config.maxTokens, 4096)
        XCTAssertEqual(config.temperature, 0.7)
        XCTAssertFalse(config.isActive)
    }
    
    func testAPIConfigurationValidation() {
        var config = APIConfiguration.defaultOpenAI()
        XCTAssertFalse(config.isValid) // No API key
        
        config.apiKey = "test-api-key"
        XCTAssertTrue(config.isValid)
        
        config.name = ""
        XCTAssertFalse(config.isValid) // Empty name
        
        config.name = "Test"
        config.baseURL = "invalid-url"
        XCTAssertFalse(config.isValid) // Invalid URL
    }
    
    // MARK: - Configuration Manager Tests
    func testDefaultMode() {
        XCTAssertEqual(configManager.currentMode, .hosted)
        XCTAssertFalse(configManager.isCustomModeEnabled)
    }
    
    func testModeSwitch() {
        configManager.setMode(.custom)
        XCTAssertEqual(configManager.currentMode, .custom)
        XCTAssertTrue(configManager.isCustomModeEnabled)
        
        configManager.setMode(.hosted)
        XCTAssertEqual(configManager.currentMode, .hosted)
        XCTAssertFalse(configManager.isCustomModeEnabled)
    }
    
    func testAddConfiguration() {
        let initialCount = configManager.configurations.count
        
        var config = APIConfiguration.defaultOpenAI()
        config.apiKey = "test-key"
        
        configManager.addConfiguration(config)
        
        XCTAssertEqual(configManager.configurations.count, initialCount + 1)
        XCTAssertTrue(configManager.configurations.contains { $0.id == config.id })
    }
    
    func testSetActiveConfiguration() {
        var config = APIConfiguration.defaultOpenAI()
        config.apiKey = "test-key"
        
        configManager.addConfiguration(config)
        configManager.setActiveConfiguration(config)
        
        XCTAssertEqual(configManager.activeConfiguration?.id, config.id)
        XCTAssertTrue(config.isActive)
    }
    
    func testDeleteConfiguration() {
        var config = APIConfiguration.defaultOpenAI()
        config.apiKey = "test-key"
        
        configManager.addConfiguration(config)
        let initialCount = configManager.configurations.count
        
        configManager.deleteConfiguration(config)
        
        XCTAssertEqual(configManager.configurations.count, initialCount - 1)
        XCTAssertFalse(configManager.configurations.contains { $0.id == config.id })
    }
    
    func testSignatureVerificationDisabled() {
        // In hosted mode with FinPin, signature verification should be enabled
        configManager.setMode(.hosted)
        XCTAssertFalse(configManager.isSignatureVerificationDisabled)
        
        // In custom mode with non-FinPin provider, signature verification should be disabled
        configManager.setMode(.custom)
        var openaiConfig = APIConfiguration.defaultOpenAI()
        openaiConfig.apiKey = "test-key"
        configManager.addConfiguration(openaiConfig)
        configManager.setActiveConfiguration(openaiConfig)
        
        XCTAssertTrue(configManager.isSignatureVerificationDisabled)
    }
    
    func testGetActiveEndpoint() {
        // Default should be FinPin
        let defaultEndpoint = configManager.getActiveEndpoint()
        XCTAssertEqual(defaultEndpoint, APIProvider.finpin.defaultBaseURL)
        
        // Switch to custom OpenAI
        configManager.setMode(.custom)
        var openaiConfig = APIConfiguration.defaultOpenAI()
        openaiConfig.apiKey = "test-key"
        configManager.addConfiguration(openaiConfig)
        configManager.setActiveConfiguration(openaiConfig)
        
        let customEndpoint = configManager.getActiveEndpoint()
        XCTAssertEqual(customEndpoint, APIProvider.openai.defaultBaseURL)
    }
    
    // MARK: - Default Configurations Tests
    func testDefaultConfigurations() {
        let openaiConfig = APIConfiguration.defaultOpenAI()
        XCTAssertEqual(openaiConfig.provider, .openai)
        XCTAssertEqual(openaiConfig.modelName, "gpt-4")
        
        let anthropicConfig = APIConfiguration.defaultAnthropic()
        XCTAssertEqual(anthropicConfig.provider, .anthropic)
        XCTAssertEqual(anthropicConfig.modelName, "claude-3-5-sonnet-20241022")
        
        let finpinConfig = APIConfiguration.defaultFinPin()
        XCTAssertEqual(finpinConfig.provider, .finpin)
        XCTAssertTrue(finpinConfig.isActive)
    }
}
