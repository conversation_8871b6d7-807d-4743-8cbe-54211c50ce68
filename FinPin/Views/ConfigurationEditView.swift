import SwiftUI

// MARK: - Configuration Edit View
struct ConfigurationEditView: View {
    let configuration: APIConfiguration?
    let onSave: (APIConfiguration) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var name: String = ""
    @State private var selectedProvider: APIProvider = .openai
    @State private var baseURL: String = ""
    @State private var apiKey: String = ""
    @State private var modelName: String = ""
    
    @State private var showingTestResult = false
    @State private var testResultMessage = ""
    @State private var isTestingConnection = false
    @State private var showingAPIKeyInfo = false
    
    private var isEditing: Bool {
        configuration != nil
    }
    
    private var isFormValid: Bool {
        !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !baseURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        URL(string: baseURL) != nil &&
        !apiKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !modelName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    VStack(spacing: 8) {
                        Image(systemName: selectedProvider.iconName)
                            .font(.system(size: 40))
                            .foregroundColor(.blue)

                        Text(isEditing ? "Edit Configuration" : "New Configuration")
                            .font(.title2)
                            .fontWeight(.bold)

                        Text("Configure your API settings")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding(.top, 20)

                    // Configuration Form
                    VStack(spacing: 20) {
                        // Configuration Name
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "tag.fill")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Text("Configuration Name")
                                    .font(.headline)
                            }

                            TextField("e.g., My OpenAI Config", text: $name)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .padding(.leading, 28)
                        }

                        // Provider Selection
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "server.rack")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Text("Provider")
                                    .font(.headline)
                            }

                            Picker("Provider", selection: $selectedProvider) {
                                Text("OpenAI").tag(APIProvider.openai)
                                Text("Anthropic").tag(APIProvider.anthropic)
                            }
                            .pickerStyle(SegmentedPickerStyle())
                            .padding(.leading, 28)
                            .onChange(of: selectedProvider) { newProvider in
                                updateDefaultsForProvider(newProvider)
                            }
                        }

                        // Base URL
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "link")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Text("Base URL")
                                    .font(.headline)
                                Spacer()
                                Button("Reset") {
                                    baseURL = selectedProvider.defaultBaseURL
                                }
                                .font(.caption)
                                .foregroundColor(.blue)
                            }

                            TextField("https://api.example.com/v1", text: $baseURL)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .keyboardType(.URL)
                                .padding(.leading, 28)
                        }

                        // API Key
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "key.fill")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Text("API Key")
                                    .font(.headline)
                                Spacer()
                                Button(action: { showingAPIKeyInfo = true }) {
                                    Image(systemName: "info.circle")
                                        .foregroundColor(.blue)
                                }
                            }

                            SecureField("Enter your API key", text: $apiKey)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .padding(.leading, 28)

                            Text("Your API key is stored securely on your device only")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.leading, 28)
                        }

                        // Model Name
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "brain.head.profile")
                                    .foregroundColor(.blue)
                                    .frame(width: 20)
                                Text("Model Name")
                                    .font(.headline)
                            }

                            TextField(getModelPlaceholder(), text: $modelName)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .padding(.leading, 28)
                        }
                    }
                    .padding(.horizontal, 20)
                

                    // Test Connection Button
                    VStack(spacing: 12) {
                        Button(action: testConnection) {
                            HStack {
                                if isTestingConnection {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Image(systemName: "network")
                                        .font(.headline)
                                }

                                Text(isTestingConnection ? "Testing..." : "Test Connection")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(isFormValid ? Color.blue : Color.gray)
                            )
                        }
                        .disabled(!isFormValid || isTestingConnection)
                        .padding(.horizontal, 20)

                        if !isFormValid {
                            Text("Please fill in all required fields to test the connection")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .padding(.horizontal, 20)
                        }
                    }

                    // Provider Information
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(.blue)
                            Text("Provider Information")
                                .font(.headline)
                        }
                        .padding(.horizontal, 20)

                        VStack(alignment: .leading, spacing: 8) {
                            switch selectedProvider {
                            case .openai:
                                InfoRow(icon: "globe", text: "Get your API key from: platform.openai.com")
                                InfoRow(icon: "checkmark.circle", text: "Compatible with OpenAI API format")
                                InfoRow(icon: "brain.head.profile", text: "Supports GPT-4, GPT-3.5-turbo, etc.")

                            case .anthropic:
                                InfoRow(icon: "globe", text: "Get your API key from: console.anthropic.com")
                                InfoRow(icon: "checkmark.circle", text: "Compatible with Anthropic API format")
                                InfoRow(icon: "brain.head.profile", text: "Supports Claude 3.5 Sonnet, Claude 3 Opus, etc.")

                            case .finpin:
                                EmptyView()
                            }
                        }
                        .padding(.horizontal, 20)
                    }
                    .padding(.bottom, 20)
                }
            }
            .navigationTitle(isEditing ? "Edit Configuration" : "New Configuration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveConfiguration()
                    }
                    .fontWeight(.semibold)
                    .disabled(!isFormValid)
                }
            }
        }
        .onAppear {
            loadConfiguration()
        }
        .alert("Connection Test", isPresented: $showingTestResult) {
            Button("OK") { }
        } message: {
            Text(testResultMessage)
        }
        .alert("API Key Information", isPresented: $showingAPIKeyInfo) {
            Button("OK") { }
        } message: {
            Text("Your API key is encrypted and stored securely on your device. It is never transmitted to FinPin servers and is only used to communicate directly with your chosen AI provider.")
        }
    }
    
    // MARK: - Helper Methods
    private func loadConfiguration() {
        if let config = configuration {
            name = config.name
            selectedProvider = config.provider
            baseURL = config.baseURL
            apiKey = config.apiKey
            modelName = config.modelName ?? ""
        } else {
            updateDefaultsForProvider(selectedProvider)
        }
    }

    private func updateDefaultsForProvider(_ provider: APIProvider) {
        baseURL = provider.defaultBaseURL

        switch provider {
        case .openai:
            if modelName.isEmpty { modelName = "gpt-4" }
        case .anthropic:
            if modelName.isEmpty { modelName = "claude-3-5-sonnet-20241022" }
        case .finpin:
            break
        }
    }

    private func getModelPlaceholder() -> String {
        switch selectedProvider {
        case .openai:
            return "gpt-4, gpt-3.5-turbo, etc."
        case .anthropic:
            return "claude-3-5-sonnet-20241022, etc."
        case .finpin:
            return ""
        }
    }

    private func saveConfiguration() {
        let config = APIConfiguration(
            id: configuration?.id ?? UUID(),
            name: name.trimmingCharacters(in: .whitespacesAndNewlines),
            provider: selectedProvider,
            baseURL: baseURL.trimmingCharacters(in: .whitespacesAndNewlines),
            apiKey: apiKey.trimmingCharacters(in: .whitespacesAndNewlines),
            isActive: configuration?.isActive ?? false,
            modelName: modelName.trimmingCharacters(in: .whitespacesAndNewlines),
            maxTokens: 4096,
            temperature: 0.7
        )

        onSave(config)
    }
    
    private func testConnection() {
        guard isFormValid else { return }

        isTestingConnection = true

        Task { @MainActor in
            do {
                // Create a proper test request based on provider
                let testBaseURL = baseURL.trimmingCharacters(in: .whitespacesAndNewlines)
                var testURL: String
                var headers: [String: String] = ["Content-Type": "application/json"]

                switch selectedProvider {
                case .openai:
                    testURL = "\(testBaseURL)/models"
                    headers["Authorization"] = "Bearer \(apiKey)"
                case .anthropic:
                    testURL = "\(testBaseURL)/messages"
                    headers["x-api-key"] = apiKey
                    headers["anthropic-version"] = "2023-06-01"
                case .finpin:
                    testURL = testBaseURL
                }

                guard let url = URL(string: testURL) else {
                    throw URLError(.badURL)
                }

                var request = URLRequest(url: url)
                request.httpMethod = "GET"
                for (key, value) in headers {
                    request.setValue(value, forHTTPHeaderField: key)
                }
                request.timeoutInterval = 10

                let (_, response) = try await URLSession.shared.data(for: request)

                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        testResultMessage = "✅ Connection successful!\n\nYour API configuration is working correctly."
                    } else if httpResponse.statusCode == 401 {
                        testResultMessage = "❌ Authentication failed.\n\nPlease check your API key."
                    } else if httpResponse.statusCode == 403 {
                        testResultMessage = "❌ Access forbidden.\n\nYour API key may not have the required permissions."
                    } else {
                        testResultMessage = "⚠️ Server responded with status code \(httpResponse.statusCode).\n\nThe configuration might be valid, but there may be other issues."
                    }
                } else {
                    testResultMessage = "⚠️ Unexpected response format.\n\nPlease verify your configuration."
                }

            } catch {
                testResultMessage = "❌ Connection failed.\n\nError: \(error.localizedDescription)\n\nPlease check your URL, API key and internet connection."
            }

            isTestingConnection = false
            showingTestResult = true
        }
    }
}

// MARK: - Info Row Component
struct InfoRow: View {
    let icon: String
    let text: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .frame(width: 16)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Preview
struct ConfigurationEditView_Previews: PreviewProvider {
    static var previews: some View {
        ConfigurationEditView(configuration: nil) { _ in }
    }
}
