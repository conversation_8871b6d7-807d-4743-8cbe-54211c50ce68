import SwiftUI
import MapKit
import PhotosUI
import CryptoKit
import Foundation
@preconcurrency import Vision
import UniformTypeIdentifiers
import Intents
import IntentsUI

// MARK: - Environment Detection
extension Bundle {
    var isTestFlight: Bool {
        guard let receiptURL = Bundle.main.appStoreReceiptURL else { return false }
        return receiptURL.lastPathComponent == "sandboxReceipt"
    }
    
    var isDebug: Bool {
        #if DEBUG
        return true
        #else
        return false
        #endif
    }
}



struct ContentView: View {
    @State private var selectedTab = 0
    @EnvironmentObject private var dataManager: DataManager

    var body: some View {
        TabView(selection: $selectedTab) {
            HomeView()
                .tabItem {
                    Image(systemName: "house.fill")
                    Text("Home")
                }
                .tag(0)

            ExpenseLogView()
                .tabItem {
                    Image(systemName: "list.bullet.rectangle")
                    Text("Log")
                }
                .tag(1)

            StatisticsView()
                .tabItem {
                    Image(systemName: "chart.bar.fill")
                    Text("Stats")
                }
                .tag(2)

            SettingsView()
                .environmentObject(dataManager)
                .tabItem {
                    Image(systemName: "gear")
                    Text("Settings")
                }
                .tag(3)


        }
        .accentColor(.blue)
        .onContinueUserActivity("app.finpin.v1.addExpenseVoice") { userActivity in
            handleSiriVoiceInput(userActivity)
        }
        .onContinueUserActivity("app.finpin.v1.addExpenseText") { userActivity in
            handleShortcutsTextInput(userActivity)
        }
      }

    // MARK: - Siri & Shortcuts Handlers
    private func handleSiriVoiceInput(_ userActivity: NSUserActivity) {
        print("🎤 Received Siri voice input")

        // Extract text from user activity
        if let text = userActivity.userInfo?["text"] as? String {
            Task {
                let success = await SiriShortcutsManager.shared.processExpenseFromSiri(text: text)
                DispatchQueue.main.async {
                    if success {
                        // Show success feedback
                        print("✅ Siri expense processed successfully")
                    } else {
                        // Show error feedback
                        print("❌ Failed to process Siri expense")
                    }
                }
            }
        }
    }

    private func handleShortcutsTextInput(_ userActivity: NSUserActivity) {
        print("📝 Received Shortcuts text input")

        // Extract text from user activity
        if let text = userActivity.userInfo?["text"] as? String {
            Task {
                let success = await SiriShortcutsManager.shared.processExpenseFromText(text: text)
                DispatchQueue.main.async {
                    if success {
                        // Show success feedback
                        print("✅ Shortcuts expense processed successfully")
                    } else {
                        // Show error feedback
                        print("❌ Failed to process Shortcuts expense")
                    }
                }
            }
        }
    }
}

// MARK: - Home View
struct HomeView: View {
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var toastManager = ToastManager()
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @State private var showingAddExpense = false
    @State private var showingImagePicker = false
    @State private var showingCamera = false
    
    @State private var isProcessingOCR = false

    var recentExpenses: [ExpenseRecord] {
        Array(dataManager.expenses.prefix(5)) // Show only recent 5 expenses
    }

    // 优化：限制月度费用显示数量，提升性能
    var monthlyExpenses: [ExpenseRecord] {
        let calendar = Calendar.current
        let now = Date()
        let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!

        return dataManager.expenses.lazy
            .filter { expense in
                expense.date >= monthAgo && expense.date <= now
            }
            .sorted { $0.date > $1.date }
            .prefix(6) // 只显示6个，提升性能
            .map { $0 }
    }

    // 缓存的汇率计算结果
    @State private var cachedTodayTotal: Decimal = 0
    @State private var cachedWeeklyTotal: Decimal = 0
    @State private var lastCalculationDate: Date = Date()

    // 优化：使用缓存避免重复计算
    var todayTotal: Decimal {
        return cachedTodayTotal
    }

    var weeklyTotal: Decimal {
        return cachedWeeklyTotal
    }

    // 计算汇率总额的方法
    private func calculateTotals() {
        let calendar = Calendar.current
        let today = Date()

        print("📊 Calculating totals for \(dataManager.expenses.count) expenses")

        // 计算今日总额
        cachedTodayTotal = dataManager.expenses.lazy
            .filter { calendar.isDate($0.date, inSameDayAs: today) }
            .reduce(0) { total, expense in
                let convertedAmount = convertToBaseCurrency(
                    amount: expense.amount,
                    fromCurrency: expense.currency,
                    expense: expense
                )
                return total + convertedAmount
            }

        // 计算周总额
        let weekAgo = Calendar.current.date(byAdding: .day, value: -7, to: Date())!
        cachedWeeklyTotal = dataManager.expenses.lazy
            .filter { $0.date >= weekAgo }
            .reduce(0) { total, expense in
                let convertedAmount = convertToBaseCurrency(
                    amount: expense.amount,
                    fromCurrency: expense.currency,
                    expense: expense
                )
                return total + convertedAmount
            }

        lastCalculationDate = Date()
        print("📊 Totals calculated - Today: \(cachedTodayTotal) USD, Weekly: \(cachedWeeklyTotal) USD")
    }

    // MARK: - Currency Conversion Helper
    // 智能汇率转换：优先使用预存储，然后使用API
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            print("✅ Using pre-stored amount: \(amount) \(fromCurrency) = \(preStoredAmount) \(targetCurrency)")
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            // Only log unique conversions to reduce spam
            if !recentConversions.contains("\(fromCurrency)->\(targetCurrency)") {
                print("✅ API conversion: \(amount) \(fromCurrency) × \(rate) = \(convertedAmount) \(targetCurrency)")
                recentConversions.insert("\(fromCurrency)->\(targetCurrency)")
                if recentConversions.count > 10 {
                    recentConversions.removeFirst()
                }
            }
            return convertedAmount
        }

        // If no rate available, return original amount (fallback)
        print("⚠️ No exchange rate available for \(fromCurrency) to \(targetCurrency), using original amount: \(amount)")
        return amount
    }

    // Track recent conversions to avoid log spam
    @State private var recentConversions: Set<String> = []

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Quick Actions
                    QuickActionsView(
                        onImageImport: { showingImagePicker = true },
                        onCameraCapture: { showingCamera = true },
                        onManualAdd: { showingAddExpense = true }
                    )
                    .padding(.horizontal, 20)
                    .padding(.top, 8)

                    // Quick Stats Cards
                    HStack(spacing: 12) {
                        NavigationLink(destination: ExpenseLogViewWithTimeFilter(selectedTimeRange: .today)) {
                            StatCard(
                                title: "Today",
                                amount: todayTotal,
                                currency: baseCurrencyManager.baseCurrency,
                                color: .green,
                                icon: "calendar"
                            )
                        }

                        NavigationLink(destination: ExpenseLogViewWithTimeFilter(selectedTimeRange: .week)) {
                            StatCard(
                                title: "This Week",
                                amount: weeklyTotal,
                                currency: baseCurrencyManager.baseCurrency,
                                color: .blue,
                                icon: "calendar.badge.clock"
                            )
                        }
                    }
                    .padding(.horizontal, 20)

                    // Monthly Expense Activities Section
                    VStack(alignment: .leading, spacing: 16) {
                        HStack {
                            Text("Expense Activities")
                                .font(.headline)
                                .fontWeight(.semibold)
                            Spacer()
                            Text("Recent")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            NavigationLink(destination: ExpenseLogView()) {
                                Text("View All")
                                    .font(.subheadline)
                                    .foregroundColor(.blue)
                            }
                        }
                        .padding(.horizontal, 20)

                        if monthlyExpenses.isEmpty {
                            EmptyMonthlyView()
                                .padding(.horizontal, 20)
                        } else {
                            MonthlyExpenseGrid(expenses: monthlyExpenses)
                        }
                    }
                }
                .padding(.bottom, 20)
            }
            .navigationTitle("FinPin")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button("Manual Add", systemImage: "plus") {
                            showingAddExpense = true
                        }
                        Button("Take Photo", systemImage: "camera") {
                            showingCamera = true
                        }
                        Button("Photo Library", systemImage: "photo") {
                            showingImagePicker = true
                        }
                    } label: {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                    }
                }
            }
        }
        .onAppear {
            // Load exchange rates on app launch - use smart caching
            Task {
                // Use smart caching to refresh exchange rates only when needed
                let exchangeService = CloudflareExchangeRateService.shared
                await exchangeService.fetchRatesIfNeeded()
                // Calculate totals after rates are loaded
                calculateTotals()
            }

            // Setup toast notification listener for URL scheme
            NotificationCenter.default.addObserver(
                forName: .showToast,
                object: nil,
                queue: .main
            ) { notification in
                if let toast = notification.object as? ToastMessage {
                    toastManager.showToast(toast)
                }
            }
        }
        .onChange(of: dataManager.expenses.count) { _ in
            // Recalculate when expenses change
            calculateTotals()
        }
        .sheet(isPresented: $showingAddExpense) {
            AddExpenseView()
        }
        .sheet(isPresented: $showingImagePicker) {
            ImagePickerView { image in
                processImage(image)
            }
        }
        .fullScreenCover(isPresented: $showingCamera) {
            CameraView { image in
                processImage(image)
            }
        }
        
        .toast(toastManager)
    }

    private func processImage(_ image: UIImage) {
        isProcessingOCR = true
        toastManager.showToast(
            message: "Analyzing receipt image...",
            type: .info
        )

        Task { @MainActor in
            do {
                print("🖼️ Processing image with server API...")

                // First extract text from image using Vision
                let extractedText = try await extractTextFromImage(image)

                guard !extractedText.isEmpty else {
                    throw NSError(domain: "ImageProcessingError", code: 1, userInfo: [NSLocalizedDescriptionKey: "No text found in image"])
                }

                print("📝 Extracted text: \(extractedText.prefix(200))\(extractedText.count > 200 ? "..." : "")")

                // Then parse with server API using CloudflareExpenseParser (which handles custom configurations)
                let parsedExpense = try await CloudflareExpenseParser.shared.parseExpense(from: extractedText)

                // Create expense record with current exchange rate
                let expense = ExpenseRecord.createWithCurrentExchangeRate(
                    amount: parsedExpense.amount,
                    currency: parsedExpense.currency,
                    merchant: parsedExpense.merchant,
                    transactionDate: parsedExpense.transactionDate,
                    originalTimestamp: parsedExpense.originalTimestamp,
                    latitude: 0.0,
                    longitude: 0.0,
                    locationName: parsedExpense.location,
                    tags: parsedExpense.tags,
                    paymentMethod: parsedExpense.paymentMethod,
                    paymentCard: parsedExpense.paymentCard,
                    notes: parsedExpense.notes ?? "Created from image import (Server API)",
                    rawText: extractedText,
                    confidence: .high
                )

                dataManager.addExpense(expense)

                // Show success message
                toastManager.showToast(
                    message: "Receipt imported successfully\n\(expense.formattedAmount) at \(expense.displayMerchant)",
                    type: .success
                )

                print("✅ Image processing completed successfully!")

            } catch {
                print("❌ Image processing failed: \(error)")

                // Show error message and don't fallback to local OCR
                toastManager.showToast(
                    message: "Failed to analyze receipt: \(error.localizedDescription)",
                    type: .error
                )
            }

            isProcessingOCR = false
        }
    }

    private func parseWithServerAPI(_ text: String) async throws -> (amount: String, currency: String, merchant: String?, paymentMethod: String?, paymentCard: String?, location: String?, confidence: Double, category: String?, tags: [String]?, description: String?, timestamp: String?) {
        print("🚀 HomeView: Starting server API call for image import...")

        // Ensure device is registered using unified system
        try await ensureDeviceRegistrationForImageImport()

        // Get device credentials - retry registration if missing
        var deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        var keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId == nil || keySeed == nil {
            print("🔄 Device credentials missing, attempting registration...")
            try await ensureDeviceRegistrationForImageImport()
            deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
            keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")
        }

        guard let finalDeviceId = deviceId, let finalKeySeed = keySeed else {
            throw NSError(domain: "FinPinError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unable to register device. Please check your network connection and try again."])
        }

        // Create request body
        let requestBody: [String: Any] = [
            "text": text,
            "context": [
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "image_metadata": [
                    "format": "text",
                    "source": "image_import"
                ]
            ]
        ]

        // Create JSON matching JavaScript JSON.stringify() format (compact, no extra spaces)
        let requestBodyData = try JSONSerialization.data(withJSONObject: requestBody, options: [])
        let requestBodyString = String(data: requestBodyData, encoding: .utf8) ?? ""

        // Generate timestamp and signature
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        let signature = generateHMACSignature(
            deviceId: finalDeviceId,
            timestamp: timestamp,
            requestBody: requestBodyString,
            keySeed: finalKeySeed
        )

        // Create authenticated request
        let endpointManager = APIEndpointManager.shared
        let url = URL(string: endpointManager.getExpenseParseURL())!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-ImageImport/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue(finalDeviceId, forHTTPHeaderField: "x-device-id")
        request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
        request.setValue(signature, forHTTPHeaderField: "x-signature")
        request.timeoutInterval = 30.0
        request.httpBody = requestBodyData

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "ServerError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid HTTP response"])
        }

        guard httpResponse.statusCode == 200 else {
            let responseString = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server returned status code \(httpResponse.statusCode). Response: \(responseString)"])
        }

        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let success = json["success"] as? Bool, success,
              let responseData = json["data"] as? [String: Any] else {
            let responseString = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw NSError(domain: "ParseError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse server response: \(responseString)"])
        }

        // Extract response data
        let amount = responseData["amount"] as? String ?? "0"
        let currency = responseData["currency"] as? String ?? baseCurrencyManager.baseCurrency
        let merchant = responseData["merchant"] as? String
        let paymentMethod = responseData["payment_method"] as? String
        let paymentCard = responseData["payment_card"] as? String
        let location = responseData["location"] as? String
        let confidence = responseData["confidence"] as? Double ?? 0.5

        let extensions = responseData["extensions"] as? [String: Any]
        let category = extensions?["category"] as? String
        let tags = extensions?["tags"] as? [String]
        let description = extensions?["description"] as? String

        return (
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            confidence: confidence,
            category: category,
            tags: tags,
            description: description,
            timestamp: responseData["timestamp"] as? String
        )
    }

    private func ensureDeviceRegistrationForImageImport() async throws {
        print("🔐 Ensuring device registration for image import...")

        // Check if device is already registered using legacy UserDefaults
        let deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId != nil && keySeed != nil {
            print("✅ Device already registered for image import")
            return
        }

        print("🔄 Device not registered, registering now...")

        let newDeviceId = UUID().uuidString

        let endpointManager = APIEndpointManager.shared
        let url = URL(string: endpointManager.getDeviceRegisterURL())!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-ImageImport/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 30.0

        let requestBody: [String: Any] = [
            "device_id": newDeviceId,
            "device_info": [
                "model": UIDevice.current.model,
                "os_version": UIDevice.current.systemVersion,
                "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                "platform": "ios"
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NSError(domain: "RegistrationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Device registration failed"])
        }

        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]

        if let success = responseData?["success"] as? Bool, success,
           let data = responseData?["data"] as? [String: Any],
           let keySeed = data["key_seed"] as? String {

            UserDefaults.standard.set(newDeviceId, forKey: "debug_device_id")
            UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")

            print("✅ Device registered successfully for image import")
        } else {
            throw NSError(domain: "RegistrationError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid registration response"])
        }
    }

    private func generateHMACSignature(deviceId: String, timestamp: String, requestBody: String, keySeed: String) -> String {
        print("🔒 HMAC Signature Generation Details:")

        // Hash the request body using SHA-256 (matching server implementation)
        let requestBodyData = Data(requestBody.utf8)
        let requestBodyHash = SHA256.hash(data: requestBodyData)
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()

        print("   Request Body Hash: \(requestBodyHashString.prefix(40))...")

        // Create message: timestamp + deviceId + requestBodyHash (matching server format)
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)

        print("   Message to Sign: \(message.prefix(100))\(message.count > 100 ? "..." : "")")

        // Create HMAC key from keySeed
        let keyData = Data(keySeed.utf8)
        let key = SymmetricKey(data: keyData)

        // Generate HMAC-SHA256 signature
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: key)
        let base64Signature = Data(signature).base64EncodedString()

        print("   Final Signature: \(base64Signature.prefix(40))...")
        print("🔒 HMAC signature generation completed")

        return base64Signature
    }

    private func extractTextFromImage(_ image: UIImage) async throws -> String {
        guard let cgImage = image.cgImage else {
            throw NSError(domain: "ImageProcessingError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid image format"])
        }

        return try await withCheckedThrowingContinuation { continuation in
            let request = VNRecognizeTextRequest { request, error in
                if let error = error {
                    continuation.resume(throwing: error)
                    return
                }

                guard let observations = request.results as? [VNRecognizedTextObservation] else {
                    continuation.resume(throwing: NSError(domain: "VisionError", code: 3, userInfo: [NSLocalizedDescriptionKey: "No text observations found"]))
                    return
                }

                let recognizedStrings = observations.compactMap { observation in
                    observation.topCandidates(1).first?.string
                }

                let fullText = recognizedStrings.joined(separator: "\n")
                continuation.resume(returning: fullText)
            }

            request.recognitionLevel = .accurate
            request.recognitionLanguages = ["zh-Hans", "en-US"]
            request.usesLanguageCorrection = true

            let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

            DispatchQueue.global(qos: .userInitiated).async {
                do {
                    try handler.perform([request])
                } catch {
                    continuation.resume(throwing: error)
                }
            }
        }
    }

    private func fallbackToLocalOCR(_ image: UIImage) {
        print("🔄 Using local OCR fallback...")

        OCRService.shared.recognizeText(from: image) { result in
            DispatchQueue.main.async {
                switch result {
                case .success(let ocrResult):
                    self.createExpenseFromOCR(ocrResult)
                    self.toastManager.showToast(
                        message: "Receipt processed with local OCR\n\(ocrResult.amount) \(ocrResult.currency) at \(ocrResult.location ?? "Unknown")",
                        type: .warning
                    )
                case .failure(let error):
                    print("❌ Local OCR also failed: \(error)")
                    self.toastManager.showToast(
                        message: "Unable to process receipt\nPlease try again or add manually",
                        type: .error
                    )
                }
            }
        }
    }

    private func createExpenseFromOCR(_ ocrResult: OCRResult) {
        guard let amount = Decimal(string: ocrResult.amount) else { return }

        // 尝试从原始文本中提取更准确的时间
        let transactionDate = extractDateFromText(ocrResult.rawText)

        // 确保货币代码被添加到可用货币列表
        let currency = dataManager.getOrAddCurrency(ocrResult.currency)

        let expense = ExpenseRecord.createWithCurrentExchangeRate(
            amount: amount,
            currency: currency,
            merchant: ocrResult.location,
            transactionDate: transactionDate,
            locationName: ocrResult.location,
            tags: ["OCR"],
            paymentMethod: "Local Recognition",
            paymentCard: ocrResult.card,
            rawText: ocrResult.rawText,
            confidence: .medium
        )

        dataManager.addExpense(expense)
    }

    // MARK: - Date Extraction Helper
    private func extractDateFromText(_ text: String) -> Date? {
        let dateFormats = [
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd HH:mm",
            "MM/dd/yyyy HH:mm:ss",
            "MM/dd/yyyy HH:mm",
            "dd/MM/yyyy HH:mm:ss",
            "dd/MM/yyyy HH:mm",
            "yyyy-MM-dd",
            "yyyy/MM/dd",
            "MM/dd/yyyy",
            "dd/MM/yyyy"
        ]

        for format in dateFormats {
            let formatter = DateFormatter()
            formatter.dateFormat = format
            formatter.locale = Locale(identifier: "en_US_POSIX")

            if let date = formatter.date(from: text) {
                return date
            }

            // 尝试在文本中查找匹配的日期字符串
            let pattern = format
                .replacingOccurrences(of: "yyyy", with: "\\d{4}")
                .replacingOccurrences(of: "MM", with: "\\d{2}")
                .replacingOccurrences(of: "dd", with: "\\d{2}")
                .replacingOccurrences(of: "HH", with: "\\d{2}")
                .replacingOccurrences(of: "mm", with: "\\d{2}")
                .replacingOccurrences(of: "ss", with: "\\d{2}")

            if let regex = try? NSRegularExpression(pattern: pattern),
               let match = regex.firstMatch(in: text, range: NSRange(text.startIndex..., in: text)),
               let range = Range(match.range, in: text) {
                let dateString = String(text[range])
                if let date = formatter.date(from: dateString) {
                    return date
                }
            }
        }

        return nil
    }
}

// MARK: - Modern UI Components
struct ModernSearchBar: View {
    @Binding var text: String
    @State private var isEditing = false

    var body: some View {
        HStack {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                    .font(.system(size: 16, weight: .medium))

                TextField("Search amount, merchant, location, date...", text: $text)
                    .font(.system(size: 16))
                    .onTapGesture {
                        isEditing = true
                    }

                if !text.isEmpty {
                    Button(action: {
                        text = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                            .font(.system(size: 16))
                    }
                }
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isEditing ? Color.blue : Color.clear, lineWidth: 2)
            )

            if isEditing {
                Button("Cancel") {
                    text = ""
                    isEditing = false
                    hideKeyboard()
                }
                .foregroundColor(.blue)
                .transition(.move(edge: .trailing))
            }
        }
        .animation(.easeInOut(duration: 0.2), value: isEditing)
    }
}

struct QuickActionsView: View {
    let onImageImport: () -> Void
    let onCameraCapture: () -> Void
    let onManualAdd: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            // Main import button
            Button(action: onImageImport) {
                HStack {
                    Image(systemName: "photo.on.rectangle.angled")
                        .font(.title2)
                        .foregroundColor(.white)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("Import Payment Receipt")
                            .font(.headline)
                            .foregroundColor(.white)

                        Text("Auto-recognize amount and merchant info")
                            .font(.caption)
                            .foregroundColor(.white.opacity(0.8))
                    }

                    Spacer()

                    Image(systemName: "arrow.right")
                        .font(.title3)
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(
                    LinearGradient(
                        colors: [.blue, .blue.opacity(0.8)],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                )
                .cornerRadius(16)
            }
            .buttonStyle(PlainButtonStyle())
        .contentShape(Rectangle())

            // Secondary action buttons
            HStack(spacing: 12) {
                Button(action: onCameraCapture) {
                    VStack(spacing: 8) {
                        Image(systemName: "camera.fill")
                            .font(.title2)
                            .foregroundColor(.blue)

                        Text("Camera")
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                }
                .buttonStyle(PlainButtonStyle())

                Button(action: onManualAdd) {
                    VStack(spacing: 8) {
                        Image(systemName: "plus.circle.fill")
                            .font(.title2)
                            .foregroundColor(.green)

                        Text("Manual Add")
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemGray6))
                    )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }
}

struct ModernExpenseCard: View {
    let expense: ExpenseRecord
    let onTap: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Top: merchant and amount
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(expense.merchant ?? "Unknown Merchant")
                        .font(.headline)
                        .foregroundColor(.primary)

                    if !expense.tags.isEmpty {
                        Text(expense.formattedTags)
                            .font(.caption)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                    }
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text(expense.formattedAmount)
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(DateFormatter.displayFormatter.string(from: expense.date))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Bottom: location and payment method
            HStack {
                if let locationName = expense.locationName {
                    Label(locationName, systemImage: "location.fill")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                if let paymentMethod = expense.paymentMethod {
                    Label(paymentMethod, systemImage: "creditcard.fill")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            // Notes
            if let notes = expense.notes, !notes.isEmpty {
                Text(notes)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(Color(.systemGray5), lineWidth: 1)
        )
        .onTapGesture {
            onTap()
        }
    }
}

struct EmptyStateView: View {
    let onAddSample: () -> Void

    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "creditcard.and.123")
                .font(.system(size: 80))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Expense Records")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("Import payment receipts or add manually\nto start tracking your expenses")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Add Sample Data") {
                onAddSample()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(.top, 60)
    }
}



// MARK: - Image Picker and Camera
struct ImagePickerView: UIViewControllerRepresentable {
    let onImageSelected: (UIImage) -> Void
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePickerView

        init(_ parent: ImagePickerView) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageSelected(image)
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

struct CameraView: UIViewControllerRepresentable {
    let onImageCaptured: (UIImage) -> Void
    @Environment(\.dismiss) private var dismiss

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .camera
        picker.allowsEditing = false
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: CameraView

        init(_ parent: CameraView) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.onImageCaptured(image)
            }
            parent.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - Add Expense View
struct AddExpenseView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var toastManager = ToastManager()

    @State private var naturalText: String = ""
    @State private var isProcessing: Bool = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var successfullyCreated: Bool = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header Section
                    VStack(spacing: 16) {
                        // Icon
                        ZStack {
                            Circle()
                                .fill(LinearGradient(
                                    gradient: Gradient(colors: [.blue.opacity(0.1), .purple.opacity(0.1)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ))
                                .frame(width: 80, height: 80)

                            Image(systemName: "plus.circle.fill")
                                .font(.system(size: 40))
                                .foregroundStyle(LinearGradient(
                                    gradient: Gradient(colors: [.blue, .purple]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                ))
                        }

                        VStack(spacing: 8) {
                            Text("Add New Expense")
                                .font(.title2)
                                .fontWeight(.bold)
                                .foregroundColor(.primary)

                            Text("Describe your expense in natural language and let AI parse the details")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                                .lineLimit(2)
                        }
                    }
                    .padding(.top, 20)

                    // Input Section
                    VStack(spacing: 24) {
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Image(systemName: "text.bubble")
                                    .foregroundColor(.blue)
                                    .font(.title3)

                                Text("Describe Your Expense")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Spacer()
                            }

                            VStack(alignment: .leading, spacing: 12) {
                                TextField("", text: $naturalText, axis: .vertical)
                                    .placeholder(when: naturalText.isEmpty) {
                                        Text("e.g., Spent $25 at Starbucks for coffee with Apple Pay")
                                            .foregroundColor(.secondary.opacity(0.6))
                                    }
                                    .font(.body)
                                    .padding(16)
                                    .background(
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(Color(.systemGray6))
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 16)
                                                    .stroke(naturalText.isEmpty ? Color.clear : Color.blue.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                                    .lineLimit(3...8)
                                    .disabled(isProcessing)

                                // Character count
                                HStack {
                                    Spacer()
                                    Text("\(naturalText.count)/500")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }

                        // Process Button
                        Button(action: processNaturalLanguage) {
                            HStack(spacing: 12) {
                                if isProcessing {
                                    ProgressView()
                                        .scaleEffect(0.9)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Image(systemName: "sparkles")
                                        .font(.system(size: 18, weight: .semibold))
                                }

                                Text(isProcessing ? "Processing with AI..." : "Create Expense")
                                    .font(.headline)
                                    .fontWeight(.semibold)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 18)
                            .background(
                                Group {
                                    if naturalText.isEmpty || isProcessing {
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(Color.gray.opacity(0.6))
                                    } else {
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(LinearGradient(
                                                gradient: Gradient(colors: [.blue, .purple]),
                                                startPoint: .leading,
                                                endPoint: .trailing
                                            ))
                                    }
                                }
                            )
                            .scaleEffect(isProcessing ? 0.98 : 1.0)
                            .animation(.easeInOut(duration: 0.1), value: isProcessing)
                        }
                        .disabled(naturalText.isEmpty || isProcessing)

                        // Examples Section
                        VStack(alignment: .leading, spacing: 16) {
                            HStack {
                                Image(systemName: "lightbulb")
                                    .foregroundColor(.orange)
                                    .font(.title3)

                                Text("Try These Examples")
                                    .font(.headline)
                                    .foregroundColor(.primary)

                                Spacer()
                            }

                            VStack(spacing: 12) {
                                ExampleCard("Paid $15.50 at Blue Bottle Coffee", icon: "cup.and.saucer")
                                ExampleCard("Spent ¥2800 at Tokyo Station for lunch", icon: "train.side.front.car")
                                ExampleCard("$89.99 at Target for groceries with Chase card", icon: "cart")
                                ExampleCard("€45 taxi ride from airport to hotel", icon: "car")
                            }
                        }

                    }
                    .padding(.horizontal, 20)

                    Spacer(minLength: 40)
                }
                .padding(.horizontal, 20)
            }
            .background(
                LinearGradient(
                    colors: [Color(.systemBackground), Color.blue.opacity(0.02)],
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: { dismiss() }) {
                        HStack(spacing: 4) {
                            Image(systemName: "xmark")
                                .font(.system(size: 16, weight: .medium))
                            Text("Cancel")
                                .font(.system(size: 17))
                        }
                        .foregroundColor(.primary)
                    }
                }
            }
        }
        .alert("Processing Result", isPresented: $showingAlert) {
            Button("OK") {
                if successfullyCreated {
                    dismiss()
                }
            }
        } message: {
            Text(alertMessage)
        }
    }

    private func processNaturalLanguage() {
        guard !naturalText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            alertMessage = "Please enter some text to process."
            showingAlert = true
            return
        }

        isProcessing = true

        Task { @MainActor in
            do {
                // Use CloudflareExpenseParser which handles custom configurations
                let parsedData = try await CloudflareExpenseParser.shared.parseExpense(from: naturalText)
                
                // Debug: Log payment method value before creating expense
                print("💳 Manual Add: Creating expense with payment method: \(parsedData.paymentMethod ?? "nil")")

                // Create expense record from parsed data
                let expense = ExpenseRecord.createWithCurrentExchangeRate(
                    amount: parsedData.amount,
                    currency: parsedData.currency,
                    merchant: parsedData.merchant,
                    date: Date(), // Record creation time (always local)
                    transactionDate: parsedData.transactionDate, // Server-parsed transaction time or nil
                    latitude: 0.0,
                    longitude: 0.0,
                    locationName: parsedData.location,
                    tags: parsedData.tags + ["manual"],
                    paymentMethod: parsedData.paymentMethod, // Use server-parsed payment method
                    paymentCard: parsedData.paymentCard,
                    notes: parsedData.notes ?? "Created from natural language input: \"\(naturalText)\"",
                    rawText: naturalText,
                    confidence: .high
                )
                
                // Debug: Log final expense payment method
                print("💳 Manual Add: Final expense payment method: \(expense.paymentMethod ?? "nil")")

                dataManager.addExpense(expense)
                successfullyCreated = true
                isProcessing = false

                alertMessage = """
                ✅ Expense created successfully!

                Amount: \(expense.currency) \(expense.amount)
                Merchant: \(expense.merchant ?? "Unknown")
                Payment: \(expense.paymentMethod ?? "N/A")
                """
                showingAlert = true

            } catch {
                isProcessing = false
                alertMessage = """
                ❌ Failed to process expense

                Error: \(error.localizedDescription)

                Please check your internet connection and try again.
                """
                showingAlert = true
            }
        }
    }

    // MARK: - Helper Components
    @ViewBuilder
    private func ExampleCard(_ text: String, icon: String) -> some View {
        Button(action: {
            naturalText = text
        }) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.system(size: 16))
                    .foregroundColor(.blue)
                    .frame(width: 24)

                Text(text)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.leading)

                Spacer()

                Image(systemName: "arrow.up.left")
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemGray6))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.blue.opacity(0.2), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Server API Integration (copied from ContentView)
    private func parseWithServerAPI(_ text: String) async throws -> (amount: String, currency: String, merchant: String?, paymentMethod: String?, paymentCard: String?, location: String?, confidence: Double, category: String?, tags: [String]?, description: String?, timestamp: String?) {
        print("🚀 AddExpenseView: Starting server API call for manual add...")

        // Ensure device is registered using unified system
        try await ensureDeviceRegistrationForManualAdd()

        // Get device credentials - retry registration if missing
        var deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        var keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId == nil || keySeed == nil {
            print("🔄 Device credentials missing, attempting registration...")
            try await ensureDeviceRegistrationForManualAdd()
            deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
            keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")
        }

        guard let finalDeviceId = deviceId, let finalKeySeed = keySeed else {
            throw NSError(domain: "FinPinError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unable to register device. Please check your network connection and try again."])
        }

        // Create request body
        let requestBody: [String: Any] = [
            "text": text,
            "context": [
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "timezone_offset": TimeZone.current.secondsFromGMT() / 3600, // Add current timezone offset in hours
                "image_metadata": [
                    "format": "text",
                    "source": "manual_add"
                ]
            ]
        ]

        // Create JSON matching JavaScript JSON.stringify() format (compact, no extra spaces)
        let requestBodyData = try JSONSerialization.data(withJSONObject: requestBody, options: [])
        let requestBodyString = String(data: requestBodyData, encoding: .utf8) ?? ""

        // Generate timestamp and signature
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        let signature = generateHMACSignature(
            deviceId: finalDeviceId,
            timestamp: timestamp,
            requestBody: requestBodyString,
            keySeed: finalKeySeed
        )

        // Create authenticated request
        let endpointManager = APIEndpointManager.shared
        let url = URL(string: endpointManager.getExpenseParseURL())!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-ManualAdd/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue(finalDeviceId, forHTTPHeaderField: "x-device-id")
        request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
        request.setValue(signature, forHTTPHeaderField: "x-signature")
        request.timeoutInterval = 30.0
        request.httpBody = requestBodyData

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "ServerError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid HTTP response"])
        }

        guard httpResponse.statusCode == 200 else {
            let responseString = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "Server returned status code \(httpResponse.statusCode). Response: \(responseString)"])
        }

        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let success = json["success"] as? Bool, success,
              let responseData = json["data"] as? [String: Any] else {
            let responseString = String(data: data, encoding: .utf8) ?? "Unknown error"
            throw NSError(domain: "ParseError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse server response: \(responseString)"])
        }

        // Extract response data
        let amount = responseData["amount"] as? String ?? "0"
        let currency = responseData["currency"] as? String ?? "USD"
        let merchant = responseData["merchant"] as? String
        let paymentMethod = responseData["payment_method"] as? String
        let paymentCard = responseData["payment_card"] as? String
        let location = responseData["location"] as? String
        let confidence = responseData["confidence"] as? Double ?? 0.5
        
        // Debug: Log manual add payment method extraction
        print("💳 Manual Add: Server returned payment_method: \(paymentMethod ?? "nil")")
        print("💳 Manual Add: All response data keys: \(responseData.keys.sorted())")
        if let paymentMethodValue = responseData["payment_method"] {
            print("💳 Manual Add: Raw payment_method value type: \(type(of: paymentMethodValue)), value: \(paymentMethodValue)")
        }

        let extensions = responseData["extensions"] as? [String: Any]
        let category = extensions?["category"] as? String
        let tags = extensions?["tags"] as? [String]
        let description = extensions?["description"] as? String
        
        // Parse timestamp from server response if available
        var transactionTimestamp: String? = nil
        if let timestamp = responseData["timestamp"] as? String {
            transactionTimestamp = timestamp
            print("🕐 Manual Add: Server returned timestamp: \(timestamp)")
        }

        return (
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            confidence: confidence,
            category: category,
            tags: tags,
            description: description,
            timestamp: transactionTimestamp
        )
    }

    // MARK: - Device Registration for Manual Add
    private func ensureDeviceRegistrationForManualAdd() async throws {
        print("🔐 Ensuring device registration for manual add...")

        // Check if device is already registered using legacy UserDefaults
        let deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId != nil && keySeed != nil {
            print("✅ Device already registered for manual add")
            return
        }

        print("🔄 Device not registered, registering now...")

        let newDeviceId = UUID().uuidString

        let endpointManager = APIEndpointManager.shared
        let url = URL(string: endpointManager.getDeviceRegisterURL())!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-ManualAdd/1.0", forHTTPHeaderField: "User-Agent")
        request.timeoutInterval = 30.0

        let requestBody: [String: Any] = [
            "device_id": newDeviceId,
            "device_info": [
                "model": UIDevice.current.model,
                "os_version": UIDevice.current.systemVersion,
                "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                "platform": "ios"
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw NSError(domain: "RegistrationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Device registration failed"])
        }

        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]

        if let success = responseData?["success"] as? Bool, success,
           let data = responseData?["data"] as? [String: Any],
           let keySeed = data["key_seed"] as? String {

            UserDefaults.standard.set(newDeviceId, forKey: "debug_device_id")
            UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")

            print("✅ Device registered successfully for manual add")
        } else {
            throw NSError(domain: "RegistrationError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Invalid registration response"])
        }
    }

    // MARK: - HMAC Signature Generation
    private func generateHMACSignature(deviceId: String, timestamp: String, requestBody: String, keySeed: String) -> String {
        print("🔒 HMAC Signature Generation Details:")

        // Hash the request body using SHA-256 (matching server implementation)
        let requestBodyData = Data(requestBody.utf8)
        let requestBodyHash = SHA256.hash(data: requestBodyData)
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()

        print("   Request Body Hash: \(requestBodyHashString.prefix(40))...")

        // Create message: timestamp + deviceId + requestBodyHash (matching server format)
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)

        print("   Message to Sign: \(message.prefix(100))\(message.count > 100 ? "..." : "")")

        // Create HMAC key from keySeed
        let keyData = Data(keySeed.utf8)
        let key = SymmetricKey(data: keyData)

        // Generate HMAC-SHA256 signature
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: key)
        let base64Signature = Data(signature).base64EncodedString()

        print("   Final Signature: \(base64Signature.prefix(40))...")
        print("🔒 HMAC signature generation completed")

        return base64Signature
    }


}

// MARK: - TextField Placeholder Extension
extension View {
    func placeholder<Content: View>(
        when shouldShow: Bool,
        alignment: Alignment = .leading,
        @ViewBuilder placeholder: () -> Content) -> some View {

        ZStack(alignment: alignment) {
            placeholder().opacity(shouldShow ? 1 : 0)
            self
        }
    }
}

// MARK: - Toast Notification System
struct ToastMessage: Identifiable, Equatable {
    let id = UUID()
    let message: String
    let type: ToastType
    let duration: TimeInterval

    enum ToastType {
        case success
        case warning
        case error
        case info

        var color: Color {
            switch self {
            case .success: return .green
            case .warning: return .orange
            case .error: return .red
            case .info: return .blue
            }
        }

        var icon: String {
            switch self {
            case .success: return "checkmark.circle.fill"
            case .warning: return "exclamationmark.triangle.fill"
            case .error: return "xmark.circle.fill"
            case .info: return "info.circle.fill"
            }
        }

        var title: String {
            switch self {
            case .success: return "Success"
            case .warning: return "Warning"
            case .error: return "Error"
            case .info: return "Processing"
            }
        }
    }

    init(message: String, type: ToastType = .info, duration: TimeInterval = 5.0) {
        self.message = message
        self.type = type
        self.duration = duration
    }
}

class ToastManager: ObservableObject {
    @Published var currentToast: ToastMessage?
    private var toastTimer: Timer?

    func showToast(_ toast: ToastMessage) {
        // Cancel existing timer
        toastTimer?.invalidate()

        // Show new toast
        withAnimation(.easeInOut(duration: 0.3)) {
            currentToast = toast
        }

        // Auto-dismiss after duration
        toastTimer = Timer.scheduledTimer(withTimeInterval: toast.duration, repeats: false) { _ in
            self.dismissToast()
        }
    }

    func showToast(message: String, type: ToastMessage.ToastType = .info, duration: TimeInterval = 5.0) {
        let toast = ToastMessage(message: message, type: type, duration: duration)
        showToast(toast)
    }

    func dismissToast() {
        withAnimation(.easeInOut(duration: 0.3)) {
            currentToast = nil
        }
        toastTimer?.invalidate()
        toastTimer = nil
    }
}

struct ToastView: View {
    let toast: ToastMessage
    let onDismiss: () -> Void

    var body: some View {
        HStack(spacing: 12) {
            // 图标背景圆圈
            Circle()
                .fill(toast.type.color.opacity(0.15))
                .frame(width: 32, height: 32)
                .overlay(
                    Image(systemName: toast.type.icon)
                        .foregroundColor(toast.type.color)
                        .font(.system(size: 14, weight: .medium))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(toast.type.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text(toast.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.leading)
                    .lineLimit(2)
            }

            Spacer()

            Button(action: onDismiss) {
                Image(systemName: "xmark")
                    .foregroundColor(.secondary)
                    .font(.system(size: 12, weight: .medium))
                    .frame(width: 20, height: 20)
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(toast.type.color.opacity(0.2), lineWidth: 1)
        )
        .padding(.horizontal, 16)
        .padding(.bottom, 16)
    }
}

struct ToastModifier: ViewModifier {
    @ObservedObject var toastManager: ToastManager

    func body(content: Content) -> some View {
        content
            .overlay(alignment: .bottom) {
                if let toast = toastManager.currentToast {
                    ToastView(toast: toast) {
                        toastManager.dismissToast()
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .zIndex(1000)
                }
            }
    }
}

extension View {
    func toast(_ toastManager: ToastManager) -> some View {
        modifier(ToastModifier(toastManager: toastManager))
    }
}

// MARK: - Helper Extensions
extension View {
    func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
}

// MARK: - Expense Detail View
struct IdentifiableTag: Identifiable {
    var id: String { value }
    let value: String
}

struct ExpenseDetailView: View {
    let expenseId: UUID
    @Environment(\ .dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @State private var showingEditView = false
    @State private var selectedTag: IdentifiableTag? = nil
    @State private var isAddingTag = false
    @State private var newTagText = ""
    @State private var pressedTag: String? = nil
    @State private var showingDeleteConfirmation = false
    @State private var showingExpenseDeleteConfirmation = false
    @State private var toastMessage: String? = nil
    @State private var showingActionMenu = false
    @State private var showingRecordTimeInfo = false
    @FocusState private var isTextFieldFocused: Bool
    
    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.timeZone = TimeZone.current // Use local timezone for user-friendly display
        return formatter
    }()

    private var expense: ExpenseRecord? {
        dataManager.expenses.first { $0.id == expenseId }
    }

    var body: some View {
        NavigationView {
            if let expense = expense {
                VStack(spacing: 0) {
                    // Custom Header with buttons
                    HStack {
                        // Back Button
                        Button(action: { dismiss() }) {
                            Image(systemName: "chevron.left")
                                .font(.system(size: 17, weight: .medium))
                                .foregroundColor(.blue)
                        }

                        Spacer()

                        Text("Expense Details")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Spacer()

                        // Menu Button
                        Button(action: {
                            showingActionMenu = true
                        }) {
                            Image(systemName: "ellipsis")
                                .font(.system(size: 17))
                                .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 12)
                    .background(Color(.systemBackground))
                    .overlay(
                        Rectangle()
                            .frame(height: 0.5)
                            .foregroundColor(Color(.separator)),
                        alignment: .bottom
                    )

                    List {
                        // Amount Section
                        Section {
                            VStack(spacing: 8) {
                                Text("\(expense.amount, format: .currency(code: expense.currency))")
                                    .font(.system(size: 32, weight: .bold, design: .rounded))
                                    .foregroundColor(.primary)

                                if expense.currency != baseCurrencyManager.baseCurrency {
                                    let convertedAmount = convertToBaseCurrency(
                                        amount: expense.amount,
                                        fromCurrency: expense.currency,
                                        expense: expense
                                    )
                                    // Only show converted amount if we have valid exchange rate data
                                    if hasValidExchangeRate(from: expense.currency, to: baseCurrencyManager.baseCurrency, expense: expense) {
                                        Text("≈ \(formatCurrency(amount: convertedAmount, currency: baseCurrencyManager.baseCurrency))")
                                            .font(.subheadline)
                                            .foregroundColor(.secondary)
                                    }
                                }

                                HStack(spacing: 4) {
                                    Text(expense.date, style: .date)
                                        .font(.subheadline)
                                        .foregroundColor(.secondary)

                                    Button(action: {
                                        showingRecordTimeInfo = true
                                    }) {
                                        Image(systemName: "info.circle")
                                            .font(.caption)
                                            .foregroundColor(.secondary)
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                        }
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)

                    // Details Section
                    Section {
                        // Time - 显示完整的交易日期时间
                        DetailRow(icon: "clock", title: "Local Time", value: {
                            let timeString = dateFormatter.string(from: expense.effectiveDate)
                            print("🕐 ExpenseDetailView: Displaying time for expense \(expense.id):")
                            print("   - effectiveDate: \(expense.effectiveDate)")
                            print("   - transactionDate: \(expense.transactionDate?.description ?? "nil")")
                            print("   - date: \(expense.date)")
                            print("   - formatted display: \(timeString)")
                            return timeString
                        }())
                        
                        if let merchant = expense.merchant, !merchant.isEmpty {
                            DetailRow(icon: "building.2", title: "Merchant", value: merchant)
                        }

                        if let paymentMethod = expense.paymentMethod, !paymentMethod.isEmpty {
                            DetailRow(icon: "creditcard", title: "Payment", value: paymentMethod)
                        }

                        DetailRow(icon: "dollarsign.circle", title: "Currency", value: expense.currency)

                        if let locationName = expense.locationName, !locationName.isEmpty {
                            DetailRow(icon: "location", title: "Location", value: locationName)
                        }

                        if let notes = expense.notes, !notes.isEmpty {
                            DetailRow(icon: "note.text", title: "Notes", value: notes)
                        }
                    }

                    // Tags Section
                    Section("Tags") {
                        VStack(alignment: .leading, spacing: 8) {
                            FlowLayout(spacing: 8) {
                                ForEach(expense.tags, id: \.self) { tag in
                                    Text("#\(tag)")
                                        .font(.caption)
                                        .fontWeight(.medium)
                                        .padding(.horizontal, 12)
                                        .padding(.vertical, 6)
                                        .background(
                                            Capsule()
                                                .fill(pressedTag == tag ? Color.red.opacity(0.3) : Color.blue.opacity(0.1))
                                        )
                                        .foregroundColor(pressedTag == tag ? .red : .blue)
                                        .onLongPressGesture(minimumDuration: 0.5) {
                                            pressedTag = tag
                                            showingDeleteConfirmation = true
                                        }
                                        .onTapGesture {
                                            selectedTag = IdentifiableTag(value: tag)
                                        }
                                }
                                
                                // Add tag button
                                Button(action: {
                                    isAddingTag = true
                                }) {
                                    Image(systemName: "plus")
                                        .font(.system(size: 11, weight: .medium))
                                        .foregroundColor(.blue)
                                        .frame(width: 22, height: 22)
                                        .background(
                                            Circle()
                                                .fill(.blue.opacity(0.1))
                                        )
                                        .foregroundColor(.blue)
                                }
                                .buttonStyle(PlainButtonStyle())
                                .opacity(isAddingTag ? 0 : 1)
                                .animation(.easeInOut(duration: 0.2), value: isAddingTag)
                            }
                            
                            // Add tag input field
                            if isAddingTag {
                                HStack {
                                    TextField("Enter tag", text: $newTagText)
                                        .textFieldStyle(RoundedBorderTextFieldStyle())
                                        .focused($isTextFieldFocused)
                                        .onSubmit {
                                            addTag(to: expense)
                                        }
                                        .onAppear {
                                            DispatchQueue.main.async {
                                                isTextFieldFocused = true
                                            }
                                        }
                                    
                                    Button("Add") {
                                        addTag(to: expense)
                                    }
                                    .disabled(newTagText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                                }
                                .padding(.top, 8)
                            }
                        }
                        .padding(.vertical, 4)
                    }

                    // Exchange Rate Section
                    if expense.currency != baseCurrencyManager.baseCurrency {
                        Section("Exchange Rate") {
                            // 显示消费币种到基准币种的汇率
                            if let currentRate = cloudflareExchangeService.getRate(from: expense.currency, to: baseCurrencyManager.baseCurrency) {
                                let rateString = String(format: "%.2f", currentRate)
                                DetailRow(
                                    icon: "arrow.2.squarepath",
                                    title: "Current Rate",
                                    value: "1 \(expense.currency) = \(rateString) \(baseCurrencyManager.baseCurrency)"
                                )
                            } else {
                                DetailRow(
                                    icon: "arrow.2.squarepath",
                                    title: "Rate",
                                    value: "API rate not available"
                                )
                            }
                        }
                    }
                    }
                    .listStyle(.insetGrouped)
                    .clipped()
                }
            } else {
                VStack(spacing: 16) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.system(size: 48))
                        .foregroundColor(.orange)

                    Text("Expense Not Found")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("This expense may have been deleted")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding()
            }
        }
        .navigationBarHidden(true)
        .sheet(isPresented: $showingEditView) {
            if let expense = expense {
                EditExpenseView(expense: expense)
            }
        }
        .sheet(item: $selectedTag) { identifiable in
            let tag = identifiable.value
            NavigationStack {
                ExpenseLogViewWithTagFilter(selectedTag: tag)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Done") {
                                selectedTag = nil
                            }
                        }
                    }
            }
        }
        .overlay(
            Group {
                if let message = toastMessage {
                    VStack {
                        Spacer()
                        Text(message)
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(Color.black.opacity(0.8))
                            )
                            .padding(.horizontal, 24)
                            .padding(.bottom, 60)
                            .transition(.move(edge: .bottom).combined(with: .opacity))
                    }
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                            withAnimation {
                                toastMessage = nil
                            }
                        }
                    }
                }
            }
        )
        .confirmationDialog("Delete Tag", isPresented: $showingDeleteConfirmation, titleVisibility: .visible) {
            Button("Delete \"\(pressedTag ?? "")\"", role: .destructive) {
                confirmDeleteTag()
            }
            Button("Cancel", role: .cancel) {
                pressedTag = nil
            }
        } message: {
            Text("Are you sure you want to delete the tag '\(pressedTag ?? "")'?")
        }
        .confirmationDialog("Actions", isPresented: $showingActionMenu, titleVisibility: .visible) {
            Button("Edit") {
                showingEditView = true
            }
            Button("Delete", role: .destructive) {
                showingExpenseDeleteConfirmation = true
            }
            Button("Cancel", role: .cancel) {
                // Do nothing, just dismiss
            }
        } message: {
            Text("Choose an action for this expense")
        }
        .confirmationDialog("Delete Expense", isPresented: $showingExpenseDeleteConfirmation, titleVisibility: .visible) {
            Button("Delete", role: .destructive) {
                if let expense = expense {
                    dataManager.deleteExpense(expense)
                    dismiss()
                }
            }
            Button("Cancel", role: .cancel) {
                // Do nothing, just dismiss
            }
        } message: {
            Text("Are you sure you want to delete this expense? This action cannot be undone.")
        }
        .alert("Record Time Information", isPresented: $showingRecordTimeInfo) {
            Button("OK") { }
        } message: {
            Text("This is the time when the expense record was created in the app, not necessarily the actual transaction time. The actual transaction time is shown in the 'Local Time' field below if available.")
        }
    }

    private func hasAdditionalDetails(_ expense: ExpenseRecord) -> Bool {
        expense.notes?.isEmpty == false ||
        expense.locationName?.isEmpty == false ||
        expense.paymentMethod?.isEmpty == false ||
        expense.paymentCard?.isEmpty == false
    }

    // 智能汇率转换：优先使用预存储，然后使用API
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            print("✅ ExpenseDetailView using pre-stored amount: \(amount) \(fromCurrency) = \(preStoredAmount) \(targetCurrency)")
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            print("✅ ExpenseDetailView API conversion: \(amount) \(fromCurrency) × \(rate) = \(convertedAmount) \(targetCurrency)")
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        print("⚠️ ExpenseDetailView: No API exchange rate available for \(fromCurrency) to \(targetCurrency), showing original amount: \(amount)")
        return amount
    }

    private func formatCurrency(amount: Decimal, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        } else {
            // Fallback formatting
            return "\(amount.formatted(.number.precision(.fractionLength(2)))) \(currency)"
        }
    }

    // Check if we have valid exchange rate data (not fallback 1:1)
    private func hasValidExchangeRate(from: String, to: String, expense: ExpenseRecord? = nil) -> Bool {
        // If same currency, no conversion needed
        if from == to { return false }

        // Strategy 1: Check if we have pre-stored exchange rate data
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           majorAmounts[to] != nil {
            return true
        }

        // Strategy 2: Check if we have valid API exchange rate data
        if cloudflareExchangeService.getRate(from: from, to: to) != nil {
            return true
        }

        return false
    }

    // MARK: - Tag Management
    private func addTag(to expense: ExpenseRecord) {
        // Check if we're still in adding mode
        guard isAddingTag else { return }
        
        // Trim only leading and trailing whitespace, preserve internal spaces
        let trimmedTag = newTagText
            .trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Validate tag - allow alphanumeric characters, spaces, and some punctuation
        guard !trimmedTag.isEmpty else { return }
        
        // Remove any control characters but keep spaces and most punctuation
        let cleanedTag = String(trimmedTag.unicodeScalars.filter { 
            CharacterSet.alphanumerics.contains($0) || 
            CharacterSet.punctuationCharacters.contains($0) || 
            $0.properties.isWhitespace
        })
        
        guard !cleanedTag.isEmpty else { return }
        
        // Limit tag length
        let finalTag = String(cleanedTag.prefix(20))
        
        // Check if tag already exists
        guard !expense.tags.contains(finalTag) else {
            // Tag already exists, just close the input
            isAddingTag = false
            newTagText = ""
            return
        }
        
        // Create updated expense with new tag
        var updatedExpense = expense
        updatedExpense.tags.append(finalTag)
        
        // Update in data manager
        dataManager.updateExpense(updatedExpense)
        
        // Close input and reset
        isAddingTag = false
        newTagText = ""
        
        // Add to available tags if it's a new tag
        dataManager.addTag(finalTag)
        
        // Show toast notification
        toastMessage = "Tag added! Long press any tag to delete it."
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            toastMessage = nil
        }
    }
    
    private func deleteTag(_ tag: String, from expense: ExpenseRecord) {
        // Create updated expense without the deleted tag
        var updatedExpense = expense
        updatedExpense.tags.removeAll { $0 == tag }
        
        // Update in data manager
        dataManager.updateExpense(updatedExpense)
    }
    
    // MARK: - Delete Confirmation
    private func confirmDeleteTag() {
        guard let tagToDelete = pressedTag else { return }
        
        // Find the current expense
        guard let currentExpense = expense else { return }
        
        deleteTag(tagToDelete, from: currentExpense)
        pressedTag = nil
    }
}

// MARK: - Main Amount Card
struct MainAmountCard: View {
    let expense: ExpenseRecord
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared

    var body: some View {
        VStack(spacing: 16) {
            // Amount Section
            VStack(spacing: 6) {
                Text(expense.formattedAmount)
                    .font(.system(size: 32, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                // Show converted amount only if we have valid exchange rate data
                if expense.currency != "USD" && hasValidExchangeRate(from: expense.currency, to: "USD", expense: expense) {
                    let convertedAmount = convertToBaseCurrency(
                        amount: expense.amount,
                        fromCurrency: expense.currency,
                        toBaseCurrency: "USD"
                    )
                    Text("≈ \(formatCurrency(amount: convertedAmount, currency: "USD"))")
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.secondary)

                    // Show exchange rate information
                    if let exchangeRateInfo = getExchangeRateInfo() {
                        Text(exchangeRateInfo)
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 4)
                    }
                }
            }

            // Merchant and Date
            VStack(spacing: 8) {
                Text(expense.displayMerchant)
                    .font(.title3)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)
                    .multilineTextAlignment(.center)

                Text(DateFormatter.displayFormatter.string(from: expense.effectiveDate))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // Confidence Badge (only if not high confidence)
            if expense.confidence != .high {
                ConfidenceBadge(confidence: expense.confidence)
            }
        }
        .padding(.horizontal, 20)
        .padding(.vertical, 24)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.06), radius: 8, x: 0, y: 2)
        )
    }

    // 只使用CloudflareExchangeRateService，禁止使用任何硬编码汇率
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, toBaseCurrency: String) -> Decimal {
        // If same currency, no conversion needed
        if fromCurrency == toBaseCurrency {
            return amount
        }

        // 只使用Cloudflare service，不使用任何硬编码汇率
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: toBaseCurrency) {
            let convertedAmount = amount * Decimal(rate)
            print("✅ MainAmountCard Cloudflare conversion: \(amount) \(fromCurrency) × \(rate) = \(convertedAmount) \(toBaseCurrency)")
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        print("⚠️ MainAmountCard: No API exchange rate available for \(fromCurrency) to \(toBaseCurrency), showing original amount: \(amount)")
        return amount
    }

    private func formatCurrency(amount: Decimal, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        } else {
            // Fallback formatting
            return "\(amount.formatted(.number.precision(.fractionLength(2)))) \(currency)"
        }
    }

    private func getExchangeRateInfo() -> String? {
        // 优先使用记录时保存的汇率信息
        if let exchangeRate = expense.exchangeRate,
           let baseCurrency = expense.baseCurrency {
            // exchangeRate存储的是 1 baseCurrency = X expense.currency 的格式
            // 所以显示应该是 1 baseCurrency = exchangeRate expense.currency
            let rateString = String(format: "%.4f", exchangeRate)
            return "1 \(baseCurrency) = \(rateString) \(expense.currency)"
        }

        // 只使用Cloudflare API汇率，不使用任何硬编码汇率
        if let currentRate = cloudflareExchangeService.getRate(from: "USD", to: expense.currency) {
            // currentRate的格式是 1 USD = currentRate expense.currency
            let rateString = String(format: "%.4f", currentRate)
            return "1 USD = \(rateString) \(expense.currency) (API rate)"
        }

        return nil
    }

    // Check if we have valid exchange rate data (not fallback 1:1)
    private func hasValidExchangeRate(from: String, to: String, expense: ExpenseRecord? = nil) -> Bool {
        // If same currency, no conversion needed
        if from == to { return false }

        // Strategy 1: Check if we have pre-stored exchange rate data
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           majorAmounts[to] != nil {
            return true
        }

        // Strategy 2: Check if we have valid API exchange rate data
        if cloudflareExchangeService.getRate(from: from, to: to) != nil {
            return true
        }

        return false
    }
}

// MARK: - Compact Info Grid
struct CompactInfoGrid: View {
    let expense: ExpenseRecord

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            // Currency Info
            CompactInfoTile(
                title: "Currency",
                value: expense.currency,
                icon: "dollarsign.circle.fill",
                color: .green
            )

            // Location or Payment Method
            if let locationName = expense.locationName, !locationName.isEmpty {
                CompactInfoTile(
                    title: "Location",
                    value: locationName,
                    icon: "location.fill",
                    color: .blue
                )
            } else if let paymentMethod = expense.paymentMethod, !paymentMethod.isEmpty {
                CompactInfoTile(
                    title: "Payment",
                    value: paymentMethod,
                    icon: "creditcard.fill",
                    color: .purple
                )
            } else {
                CompactInfoTile(
                    title: "Confidence",
                    value: expense.confidence.rawValue.capitalized,
                    icon: "doc.text.fill",
                    color: .orange
                )
            }
        }
    }
}

struct CompactInfoTile: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(color)

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Spacer()
            }

            HStack {
                Text(value)
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.primary)
                    .lineLimit(1)

                Spacer()
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemGray6))
        )
    }
}

// MARK: - Quick Info Grid
struct QuickInfoGrid: View {
    let expense: ExpenseRecord

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 12) {
            InfoTile(
                title: "Currency",
                value: expense.currency,
                icon: "dollarsign.circle.fill",
                color: .green
            )

            if expense.hasLocationData {
                InfoTile(
                    title: "Location",
                    value: "Available",
                    icon: "location.circle.fill",
                    color: .blue
                )
            } else {
                InfoTile(
                    title: "Payment",
                    value: expense.paymentMethod ?? "Unknown",
                    icon: "creditcard.circle.fill",
                    color: .purple
                )
            }
        }
    }
}

// MARK: - Compact Tags Section
struct CompactTagsSection: View {
    let tags: [String]
    let onTagTap: (String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                Image(systemName: "tag.fill")
                    .foregroundColor(.blue)
                    .font(.system(size: 14, weight: .medium))

                Text("Tags")
                    .font(.system(size: 15, weight: .semibold))
                    .foregroundColor(.primary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 70))
            ], spacing: 8) {
                ForEach(tags, id: \.self) { tag in
                    Button(action: { onTagTap(tag) }) {
                        Text("#\(tag)")
                            .font(.system(size: 13, weight: .medium))
                            .padding(.horizontal, 10)
                            .padding(.vertical, 6)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(8)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.04), radius: 4, x: 0, y: 1)
        )
    }
}



struct CompactDetailRow: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(color)
                .frame(width: 20)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.primary)
                    .fixedSize(horizontal: false, vertical: true)
            }

            Spacer()
        }
    }
}

// MARK: - Tags Section
struct TagsSection: View {
    let tags: [String]
    let onTagTap: (String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "tag.fill")
                    .foregroundColor(.orange)
                    .font(.caption)

                Text("Tags")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()
            }

            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 80))
            ], spacing: 8) {
                ForEach(tags, id: \.self) { tag in
                    Button(action: { onTagTap(tag) }) {
                        Text("#\(tag)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(16)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
        )
    }
}

// MARK: - Additional Details Card
struct AdditionalDetailsCard: View {
    let expense: ExpenseRecord

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "info.circle.fill")
                    .foregroundColor(.blue)
                    .font(.caption)

                Text("Additional Details")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()
            }

            VStack(spacing: 12) {
                if let location = expense.locationName, !location.isEmpty {
                    CompactDetailRow(
                        icon: "location.fill",
                        title: "Location",
                        value: location,
                        color: .blue
                    )
                }

                if let method = expense.paymentMethod, !method.isEmpty {
                    CompactDetailRow(
                        icon: "creditcard.fill",
                        title: "Payment Method",
                        value: method,
                        color: .purple
                    )
                }

                if let card = expense.paymentCard, !card.isEmpty {
                    CompactDetailRow(
                        icon: "creditcard",
                        title: "Payment Card",
                        value: card,
                        color: .green
                    )
                }

                if let notes = expense.notes, !notes.isEmpty {
                    CompactDetailRow(
                        icon: "note.text",
                        title: "Notes",
                        value: notes,
                        color: .orange
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
        )
    }
}

// MARK: - Supporting Components
struct ConfidenceBadge: View {
    let confidence: ExpenseRecord.DataConfidence

    var body: some View {
        HStack(spacing: 4) {
            Circle()
                .fill(confidence.color)
                .frame(width: 6, height: 6)

            Text(confidence.displayName)
                .font(.caption)
                .fontWeight(.medium)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(confidence.color.opacity(0.1))
        .foregroundColor(confidence.color)
        .cornerRadius(8)
    }
}

struct TimeSourceIndicator: View {
    let expense: ExpenseRecord

    var body: some View {
        HStack(spacing: 6) {
            Image(systemName: expense.transactionDate != nil ? "clock.fill" : "plus.circle.fill")
                .font(.caption2)

            Text(expense.transactionDate != nil ? "Transaction time" : "Record time")
                .font(.caption2)
                .fontWeight(.medium)
        }
        .foregroundColor(expense.transactionDate != nil ? .green : .orange)
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill((expense.transactionDate != nil ? Color.green : Color.orange).opacity(0.1))
        )
    }
}

struct InfoTile: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(color)
                .font(.title2)

            VStack(spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(1)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }
}

// MARK: - Currency Picker View
struct CurrencyPickerView: View {
    let selectedCurrency: String
    let onCurrencySelected: (String) -> Void
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @State private var searchText = ""
    
    // Use hardcoded currency list (from exchange-rates.finpin.app)
    private let majorCurrencies = CurrencyHelper.majorCurrencies
    private let allCurrencies = CurrencyHelper.allSupportedCurrencies
    
    // Filtered search results
    private var filteredCurrencies: [String] {
        if searchText.isEmpty {
            return []
        }
        
        let searchUpper = searchText.uppercased()
        return allCurrencies.filter { currency in
            // Exclude currencies already in major currencies list
            !majorCurrencies.contains(currency) &&
            (currency.uppercased().contains(searchUpper) ||
             CurrencyHelper.name(for: currency).uppercased().contains(searchUpper))
        }.sorted()
    }

    var body: some View {
        NavigationView {
            List {
                Section("Popular Currencies") {
                    ForEach(majorCurrencies, id: \.self) { currency in
                        CurrencyRow(
                            currency: currency,
                            isSelected: currency == selectedCurrency,
                            onTap: {
                                onCurrencySelected(currency)
                                dismiss()
                            }
                        )
                    }
                }
                
                Section {
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        TextField("Search currencies...", text: $searchText)
                    }
                    .padding(.vertical, 4)
                } header: {
                    Text("Search All Currencies")
                } footer: {
                    if searchText.isEmpty {
                        Text("Type to search from \(allCurrencies.count - majorCurrencies.count) additional currencies")
                    } else if filteredCurrencies.isEmpty {
                        Text("No currencies found")
                    } else {
                        Text("\(filteredCurrencies.count) currencies found")
                    }
                }
                
                if !searchText.isEmpty {
                    Section("Search Results") {
                        ForEach(filteredCurrencies, id: \.self) { currency in
                            CurrencyRow(
                                currency: currency,
                                isSelected: currency == selectedCurrency,
                                onTap: {
                                    onCurrencySelected(currency)
                                    dismiss()
                                }
                            )
                        }
                    }
                }
            }
            .navigationTitle("Select Currency")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                leading: Button("Cancel") {
                    dismiss()
                }
            )
        }
    }
}

struct CurrencyRow: View {
    let currency: String
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(currency)
                        .font(.headline)
                        .fontWeight(isSelected ? .semibold : .regular)
                        .foregroundColor(.primary)

                    Text(CurrencyHelper.name(for: currency))
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                        .multilineTextAlignment(.leading)
                }

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark")
                        .foregroundColor(.blue)
                        .fontWeight(.semibold)
                }
            }
            .padding(.vertical, 2)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Subscription View
struct SubscriptionView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @Environment(\.dismiss) private var dismiss
    @State private var isLoading = false
    @State private var showingTermsOfService = false
    @State private var showingPrivacyPolicy = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    VStack(spacing: 16) {
                        Image(systemName: "crown.fill")
                            .font(.system(size: 60))
                            .foregroundColor(.orange)

                        Text("FinPin Premium")
                            .font(.largeTitle)
                            .fontWeight(.bold)
                    }
                    .padding(.top, 20)

                    // Features
                    VStack(spacing: 20) {
                        FeatureRow(
                            icon: "infinity",
                            title: "Unlimited Expenses",
                            description: "No limit on recording expenses, go beyond \(subscriptionManager.expenseLimit) limit"
                        )
                    }
                    .padding(.horizontal, 20)
                    .frame(maxWidth: .infinity, alignment: .center)

                    // Pricing
                    VStack(spacing: 16) {
                        VStack(spacing: 8) {
                            Text("Premium Subscription")
                                .font(.title2)
                                .fontWeight(.semibold)

                            Text("\(subscriptionManager.subscriptionPrice) every 6 months")
                                .font(.title3)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                        }

                        Text("Cancel anytime. Auto-renewable subscription.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding(20)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(.systemGray6))
                    )
                    .padding(.horizontal, 20)

                    // Buttons
                    VStack(spacing: 12) {
                        if subscriptionManager.hasActiveSubscription {
                            VStack(spacing: 12) {
                                Image(systemName: "checkmark.circle.fill")
                                    .font(.title)
                                    .foregroundColor(.green)

                                Text(subscriptionManager.subscriptionStatusText)
                                    .font(.headline)
                                    .foregroundColor(.green)

                                if let expiryText = subscriptionManager.subscriptionExpiryText {
                                    Text(expiryText)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }

                                Text("Thank you for supporting FinPin!")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    .multilineTextAlignment(.center)
                            }
                            .padding()
                        } else {
                            VStack(spacing: 12) {
                                Button(action: {
                                    Task {
                                        await subscriptionManager.purchase()
                                    }
                                }) {
                                    HStack {
                                        if subscriptionManager.isLoading {
                                            ProgressView()
                                                .scaleEffect(0.8)
                                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                        } else {
                                            Text("Subscribe Now - \(subscriptionManager.subscriptionPrice)")
                                                .fontWeight(.semibold)
                                        }
                                    }
                                    .frame(maxWidth: .infinity)
                                    .frame(height: 50)
                                    .background(subscriptionManager.isLoading ? Color.gray : Color.blue)
                                    .foregroundColor(.white)
                                    .cornerRadius(12)
                                }
                                .disabled(subscriptionManager.isLoading)

                                Button(action: {
                                    Task {
                                        await subscriptionManager.restorePurchases()
                                    }
                                }) {
                                    Text("Restore Purchases")
                                        .font(.subheadline)
                                        .foregroundColor(.blue)
                                }
                                .disabled(subscriptionManager.isLoading)
                            }
                            .disabled(isLoading)
                            .padding(.horizontal, 20)
                        }
                    }

                    // Terms
                    VStack(spacing: 8) {
                        Text("By subscribing, you agree to our Terms of Service and Privacy Policy.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)

                        HStack(spacing: 20) {
                            Button("Terms of Service") {
                                showingTermsOfService = true
                            }
                            .font(.caption)
                            .foregroundColor(.blue)

                            Button("Privacy Policy") {
                                showingPrivacyPolicy = true
                            }
                            .font(.caption)
                            .foregroundColor(.blue)
                        }
                    }
                    .padding(.horizontal, 20)
                    .padding(.bottom, 20)
                }
            }
            .navigationTitle("Premium")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Done") {
                    dismiss()
                }
            )
        }
        .onAppear {
            print("🔄 SubscriptionView appeared, loading products...")
            Task {
                await subscriptionManager.loadProducts()
                await subscriptionManager.checkSubscriptionStatus()
            }
        }
        .alert("Purchase Error", isPresented: .constant(subscriptionManager.purchaseError != nil)) {
            Button("OK") {
                subscriptionManager.purchaseError = nil
            }
            // Add Restore Purchases button for certain errors
            if shouldShowRestoreButton(for: subscriptionManager) {
                Button("Restore Purchases") {
                    Task {
                        await subscriptionManager.restorePurchases()
                    }
                    subscriptionManager.purchaseError = nil
                }
            }
        } message: {
            Text(subscriptionManager.purchaseError ?? "")
        }
        .sheet(isPresented: $showingTermsOfService) {
            LegalDocumentView(documentType: .termsOfService)
        }
        .sheet(isPresented: $showingPrivacyPolicy) {
            LegalDocumentView(documentType: .privacyPolicy)
        }
    }
}

// Helper function to determine if we should show the Restore Purchases button
@MainActor private func shouldShowRestoreButton(for subscriptionManager: SubscriptionManager) -> Bool {
    guard let error = subscriptionManager.purchaseError else { return false }
    
    // Show restore button for timeout or network errors
    let errorLower = error.lowercased()
    return errorLower.contains("timeout") || 
           errorLower.contains("network") || 
           errorLower.contains("connection") ||
           errorLower.contains("restore") ||
           errorLower.contains("purchase") ||
           errorLower.contains("product") ||
           errorLower.contains("subscription")
}

struct FeatureRow: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 16) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.blue)
                .frame(width: 30)

            VStack(alignment: .leading, spacing: 4) {
                Text(title)
                    .font(.headline)
                    .foregroundColor(.primary)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// MARK: - Detail Card Component
struct DetailCard<Content: View>: View {
    let title: String
    let icon: String
    let content: Content

    init(title: String, icon: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: icon)
                    .foregroundColor(.blue)
                    .font(.caption)
                    .frame(width: 16)

                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Spacer()
            }

            content
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }
}



struct ModernDetailRow: View {
    let title: String
    let value: String
    let icon: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .foregroundColor(.blue)
                .font(.system(size: 16, weight: .medium))
                .frame(width: 24, height: 24)
                .background(
                    Circle()
                        .fill(Color.blue.opacity(0.1))
                )

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)

                Text(value)
                    .font(.subheadline)
                    .foregroundColor(.primary)
                    .lineLimit(nil)
            }

            Spacer()
        }
    }
}

struct EditExpenseView: View {
    let expense: ExpenseRecord
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager

    @State private var amount: String
    @State private var currency: String
    @State private var merchant: String
    @State private var locationName: String
    @State private var paymentMethod: String
    @State private var paymentCard: String
    @State private var notes: String
    @State private var date: Date
    @State private var showingCurrencyPicker = false

    init(expense: ExpenseRecord) {
        self.expense = expense
        _amount = State(initialValue: String(describing: expense.amount))
        _currency = State(initialValue: expense.currency)
        _merchant = State(initialValue: expense.merchant ?? "")
        _locationName = State(initialValue: expense.locationName ?? "")
        _paymentMethod = State(initialValue: expense.paymentMethod ?? "Apple Pay")
        _paymentCard = State(initialValue: expense.paymentCard ?? "")
        _notes = State(initialValue: expense.notes ?? "")
        // Initialize with transaction time (effectiveDate) to show actual transaction time by default
        _date = State(initialValue: expense.effectiveDate)
    }

    var body: some View {
        NavigationStack {
            Form {
                Section("Basic Information") {
                    HStack {
                        Text("Amount")
                            .frame(width: 140, alignment: .leading)
                        TextField("0.00", text: $amount)
                            .keyboardType(.decimalPad)
                            .multilineTextAlignment(.trailing)
                    }

                    HStack {
                        Text("Currency")
                            .frame(width: 140, alignment: .leading)
                        Button(action: {
                            showingCurrencyPicker = true
                        }) {
                            HStack {
                                Text("\(currency) - \(CurrencyHelper.name(for: currency))")
                                    .foregroundColor(.primary)
                                    .lineLimit(1)
                                Spacer()
                                Image(systemName: "chevron.down")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    HStack {
                        Text("Merchant")
                            .frame(width: 140, alignment: .leading)
                        TextField("Merchant name", text: $merchant)
                    }
                }

                Section("Details") {
                    HStack {
                        Text("Location")
                            .frame(width: 140, alignment: .leading)
                        TextField("Expense Location", text: $locationName)
                    }

                    HStack {
                        Text("Payment Method")
                            .frame(width: 150, alignment: .leading)
                        TextField("Payment method", text: $paymentMethod)
                    }

                    HStack {
                        Text("Payment Card")
                            .frame(width: 150, alignment: .leading)
                        TextField("Payment card", text: $paymentCard)
                    }

                    DatePicker("Transaction Time", selection: $date, displayedComponents: [.date, .hourAndMinute])
                        .environment(\.timeZone, TimeZone.current) // Use local timezone for user-friendly editing
                }

                Section("Notes") {
                    TextField("AddNotes...", text: $notes, axis: .vertical)
                        .lineLimit(3...6)
                }
            }
            .navigationTitle("Edit Expense")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        saveChanges()
                    }
                    .disabled(amount.isEmpty || merchant.isEmpty)
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCurrencyPicker) {
            CurrencyPickerView(
                selectedCurrency: currency,
                onCurrencySelected: { newCurrency in
                    currency = newCurrency
                }
            )
        }
    }

    private func saveChanges() {
        guard let amountDecimal = Decimal(string: amount) else { return }

        let updatedExpense = ExpenseRecord(
            id: expense.id,
            amount: amountDecimal,
            currency: currency,
            merchant: merchant.isEmpty ? nil : merchant,
            date: expense.date, // Keep original record creation date unchanged
            transactionDate: date, // Save edited time as transaction time (no timezone conversion)
            latitude: expense.latitude,
            longitude: expense.longitude,
            locationName: locationName.isEmpty ? nil : locationName,
            tags: expense.tags,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard.isEmpty ? nil : paymentCard,
            notes: notes.isEmpty ? nil : notes,
            rawText: expense.rawText,
            confidence: expense.confidence,
            exchangeRate: expense.exchangeRate,
            baseCurrency: expense.baseCurrency,
            convertedAmount: expense.convertedAmount
        )

        dataManager.updateExpense(updatedExpense)
        dismiss()
    }
}

// MARK: - Expense Log View
struct ExpenseLogView: View {
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @State private var searchText = ""
    @State private var selectedTimeRange: TimeRange = .all
    @State private var selectedExpense: ExpenseRecord?
    @State private var showingFilters = false
    @State private var displayedItemsCount = 50 // 初始显示50条记录
    private let itemsPerPage = 50 // 每次加载50条
    
    // Multi-select state
    @State private var isMultiSelectMode = false
    @State private var selectedExpenses: Set<UUID> = []
    @State private var showingBatchTagSheet = false
    @State private var showingRemoveTagSheet = false

    enum FilterType: String, CaseIterable {
        case all = "All"

        var icon: String {
            return "list.bullet"
        }
    }

    enum TimeRange: String, CaseIterable {
        case all = "All Time"
        case today = "Today"
        case week = "This Week"
        case month = "This Month"
        case year = "This Year"

        var icon: String {
            switch self {
            case .all: return "calendar"
            case .today: return "calendar.badge.clock"
            case .week: return "calendar.badge.plus"
            case .month: return "calendar.circle"
            case .year: return "calendar.badge.exclamationmark"
            }
        }
    }



    // 所有筛选后的费用（用于统计）
    var allFilteredExpenses: [ExpenseRecord] {
        var expenses = dataManager.expenses

        // Apply search filter with enhanced tag search
        if !searchText.isEmpty {
            expenses = expenses.filter { expense in
                // Check if search text starts with # for tag search
                if searchText.hasPrefix("#") {
                    let tagQuery = String(searchText.dropFirst()).lowercased()
                    return expense.tags.contains { $0.lowercased().contains(tagQuery) }
                } else {
                    // Regular full-text search
                    return expense.matches(searchText: searchText)
                }
            }
        }

        // Apply time range filter
        let calendar = Calendar.current
        let now = Date()

        switch selectedTimeRange {
case .all:
    break
case .today:
    expenses = expenses.filter { calendar.isDate($0.date, inSameDayAs: now) }
case .week:
    let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
    expenses = expenses.filter { $0.effectiveDate >= weekAgo }
case .month:
    let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!
    expenses = expenses.filter { $0.date >= monthAgo }
case .year:
    let yearAgo = calendar.date(byAdding: .year, value: -1, to: now)!
    expenses = expenses.filter { $0.date >= yearAgo }
}

        return expenses.sorted { $0.effectiveDate > $1.effectiveDate }
    }

    // 分页显示的费用（性能优化）
    var filteredExpenses: [ExpenseRecord] {
        let allExpenses = allFilteredExpenses
        return Array(allExpenses.prefix(displayedItemsCount))
    }

    // 是否还有更多数据可以加载
    var hasMoreData: Bool {
        allFilteredExpenses.count > displayedItemsCount
    }

    var totalAmount: Decimal {
        calculateTotalInBaseCurrency(allFilteredExpenses)
    }

    private func calculateTotalInBaseCurrency(_ expenses: [ExpenseRecord]) -> Decimal {
        expenses.reduce(Decimal(0)) { total, expense in
            let convertedAmount = convertToBaseCurrency(
                amount: expense.amount,
                fromCurrency: expense.currency,
                expense: expense
            )
            return total + convertedAmount
        }
    }

    // 智能汇率转换：优先使用预存储，然后使用API
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search and Filter Bar
                VStack(spacing: 12) {
                    // Search Bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)

                        TextField("Search expenses or #tag...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                            .submitLabel(.done)
                            .onSubmit {
                                hideKeyboard()
                            }

                        if !searchText.isEmpty {
                            Button(action: { searchText = "" }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)

                    // Search hint for tag search
                    if searchText.hasPrefix("#") && searchText.count > 1 {
                        HStack {
                            Image(systemName: "tag.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text("Searching tags...")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        .padding(.horizontal, 4)
                    }

                    // Time Range Filter
                    HStack {
                        Menu {
                            ForEach(TimeRange.allCases, id: \.self) { range in
                                Button(action: { selectedTimeRange = range }) {
                                    Label(range.rawValue, systemImage: range.icon)
                                }
                            }
                        } label: {
                            HStack(spacing: 6) {
                                Image(systemName: selectedTimeRange.icon)
                                Text(selectedTimeRange.rawValue)
                                Image(systemName: "chevron.down")
                                    .font(.caption)
                            }
                            .font(.subheadline)
                            .foregroundColor(selectedTimeRange == .all ? .primary : .white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: 20)
                                    .fill(selectedTimeRange == .all ? Color(.systemGray5) : Color.green)
                            )
                        }

                        Spacer()
                    }
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 16)
                .background(Color(.systemBackground))

                // Summary Bar
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        HStack {
                            Text("\(allFilteredExpenses.count) expenses")
                                .font(.subheadline)
                                .foregroundColor(.secondary)

                            if hasMoreData {
                                Text("(showing \(displayedItemsCount))")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .opacity(0.7)
                            }
                        }
                        Text("\(totalAmount, format: .currency(code: baseCurrencyManager.baseCurrency))")
                            .font(.title2)
                            .fontWeight(.bold)
                    }
                    Spacer()
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color(.systemGray6))

                // Expense List
                if allFilteredExpenses.isEmpty {
                    EmptyLogView()
                } else {
                    List {
                        ForEach(filteredExpenses) { expense in
                            if isMultiSelectMode {
                                Button(action: {
                                    toggleExpenseSelection(expense)
                                }) {
                                    SelectableLogExpenseRow(
                                        expense: expense,
                                        isSelected: selectedExpenses.contains(expense.id)
                                    )
                                }
                                .buttonStyle(PlainButtonStyle())
                            } else {
                                NavigationLink(destination: ExpenseDetailView(expenseId: expense.id)) {
                                    LogExpenseRow(expense: expense)
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .listRowInsets(EdgeInsets(top: 8, leading: 20, bottom: 8, trailing: 20))
                        .listRowSeparator(.hidden)

                        // 加载更多按钮
                        if hasMoreData {
                            LoadMoreButton {
                                loadMoreItems()
                            }
                            .listRowInsets(EdgeInsets(top: 16, leading: 20, bottom: 16, trailing: 20))
                            .listRowSeparator(.hidden)
                            .listRowBackground(Color.clear)
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle(isMultiSelectMode ? "\(selectedExpenses.count) Selected" : "Expense Log")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        toggleMultiSelectMode()
                    }) {
                        Image(systemName: isMultiSelectMode ? "xmark" : "checkmark.circle")
                            .font(.system(size: 17, weight: .medium))
                    }
                }
            }
            .safeAreaInset(edge: .bottom) {
                if isMultiSelectMode && !selectedExpenses.isEmpty {
                    BatchActionsToolbar(
                        selectedCount: selectedExpenses.count,
                        onAddTag: {
                            showingBatchTagSheet = true
                        },
                        onRemoveTag: {
                            showingRemoveTagSheet = true
                        }
                    )
                }
            }
            
            .onChange(of: searchText) { _ in
                resetPagination()
            }
            .onChange(of: selectedTimeRange) { _ in
                resetPagination()
            }
            .sheet(isPresented: $showingBatchTagSheet) {
                BatchTagView(
                    selectedExpenses: selectedExpenses.compactMap { id in
                        dataManager.expenses.first { $0.id == id }
                    },
                    onTagAdded: { tag in
                        addTagToSelectedExpenses(tag)
                    }
                )
            }
            .sheet(isPresented: $showingRemoveTagSheet) {
                BatchRemoveTagView(
                    selectedExpenses: selectedExpenses.compactMap { id in
                        dataManager.expenses.first { $0.id == id }
                    },
                    onTagRemoved: { tag in
                        removeTagFromSelectedExpenses(tag)
                    }
                )
            }
        }
    }

    private func loadMoreItems() {
        displayedItemsCount = min(displayedItemsCount + itemsPerPage, allFilteredExpenses.count)
    }

    private func resetPagination() {
        displayedItemsCount = itemsPerPage
    }
    
    private func toggleMultiSelectMode() {
        isMultiSelectMode.toggle()
        if !isMultiSelectMode {
            selectedExpenses.removeAll()
        }
    }
    
    private func toggleExpenseSelection(_ expense: ExpenseRecord) {
        if selectedExpenses.contains(expense.id) {
            selectedExpenses.remove(expense.id)
        } else {
            selectedExpenses.insert(expense.id)
        }
    }
    
    private func addTagToSelectedExpenses(_ tag: String) {
        for expenseId in selectedExpenses {
            if let index = dataManager.expenses.firstIndex(where: { $0.id == expenseId }) {
                var updatedExpense = dataManager.expenses[index]
                if !updatedExpense.tags.contains(tag) {
                    updatedExpense.tags.append(tag)
                    dataManager.updateExpense(updatedExpense)
                }
            }
        }
        showingBatchTagSheet = false
        selectedExpenses.removeAll()
        isMultiSelectMode = false
    }
    
    private func removeTagFromSelectedExpenses(_ tag: String) {
        for expenseId in selectedExpenses {
            if let index = dataManager.expenses.firstIndex(where: { $0.id == expenseId }) {
                var updatedExpense = dataManager.expenses[index]
                if let tagIndex = updatedExpense.tags.firstIndex(of: tag) {
                    updatedExpense.tags.remove(at: tagIndex)
                    dataManager.updateExpense(updatedExpense)
                }
            }
        }
        showingRemoveTagSheet = false
        selectedExpenses.removeAll()
        isMultiSelectMode = false
    }
}

// MARK: - Selectable Log Expense Row for Multi-Select Mode
struct SelectableLogExpenseRow: View {
    let expense: ExpenseRecord
    let isSelected: Bool

    var body: some View {
        HStack(spacing: 16) {
            // Selection indicator
            Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                .font(.system(size: 20))
                .foregroundColor(isSelected ? .blue : .gray)
                .animation(.easeInOut(duration: 0.2), value: isSelected)
            
            // Original expense content
            HStack(spacing: 16) {
                // Date Column
                VStack(alignment: .leading, spacing: 2) {
                    Text(expense.date, format: .dateTime.month(.abbreviated).day())
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(expense.date, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(width: 50, alignment: .leading)

                // Merchant and Details
                VStack(alignment: .leading, spacing: 4) {
                    Text(expense.merchant ?? "Unknown")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    HStack {
                        if let location = expense.locationName {
                            Text(location)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }

                        if !expense.tags.isEmpty {
                            Text("• \(expense.tags.joined(separator: ", "))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }
                }

                Spacer()

                // Amount
                VStack(alignment: .trailing, spacing: 2) {
                    Text(expense.formattedAmount)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    if let paymentMethod = expense.paymentMethod {
                        Text(paymentMethod)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 8)
        }
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
        )
        .animation(.easeInOut(duration: 0.2), value: isSelected)
    }
}

// MARK: - Batch Actions Toolbar
struct BatchActionsToolbar: View {
    let selectedCount: Int
    let onAddTag: () -> Void
    let onRemoveTag: () -> Void
    
    var body: some View {
        VStack(spacing: 0) {
            Divider()
            
            HStack(spacing: 8) {
                Button(action: onAddTag) {
                    HStack(spacing: 4) {
                        Image(systemName: "tag.fill")
                            .font(.system(size: 12))
                        Text("Add Tag")
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.blue)
                    )
                }
                .fixedSize()
                
                Button(action: onRemoveTag) {
                    HStack(spacing: 4) {
                        Image(systemName: "tag")
                            .font(.system(size: 12))
                        Text("Remove")
                    }
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .stroke(Color.blue, lineWidth: 1.5)
                    )
                }
                .fixedSize()
                
                Spacer()
                
                Text("\(selectedCount) selected")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 10)
            .background(Color(.systemBackground))
        }
    }
}

// MARK: - Batch Tag View
struct BatchTagView: View {
    let selectedExpenses: [ExpenseRecord]
    let onTagAdded: (String) -> Void
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    
    @State private var newTagText = ""
    @State private var isAddingNewTag = false
    @FocusState private var isTextFieldFocused: Bool
    
    // Get all existing tags from the data manager
    private var availableTags: [String] {
        Array(Set(dataManager.expenses.flatMap { $0.tags })).sorted()
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // Header info
                    VStack(spacing: 8) {
                        Text("Add Tag to \(selectedExpenses.count) Expenses")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("Select an existing tag or create a new one")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.top)
                    
                    // Create New Tag section (moved to top for better accessibility)
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Create New Tag")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        if isAddingNewTag {
                            HStack {
                                TextField("Enter tag name", text: $newTagText)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                                    .focused($isTextFieldFocused)
                                    .onSubmit {
                                        addNewTag()
                                    }
                                
                                Button("Add") {
                                    addNewTag()
                                }
                                .disabled(newTagText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                                .fontWeight(.semibold)
                                
                                Button("Cancel") {
                                    cancelNewTag()
                                }
                                .foregroundColor(.secondary)
                            }
                        } else {
                            Button(action: {
                                isAddingNewTag = true
                                // Delay focus to prevent jumping to top
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                    isTextFieldFocused = true
                                }
                            }) {
                                HStack {
                                    Image(systemName: "plus.circle.fill")
                                    Text("Create New Tag")
                                }
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.green)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 10)
                                .background(
                                    RoundedRectangle(cornerRadius: 20)
                                        .fill(Color.green.opacity(0.1))
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 20)
                                                .stroke(Color.green.opacity(0.3), lineWidth: 1)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    .padding(.horizontal)
                
                // Existing tags grid
                if !availableTags.isEmpty {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Text("Existing Tags")
                                .font(.subheadline)
                                .fontWeight(.medium)
                                .foregroundColor(.secondary)
                            Spacer()
                        }
                        
                        LazyVGrid(columns: [
                            GridItem(.adaptive(minimum: 100))
                        ], spacing: 12) {
                            ForEach(availableTags, id: \.self) { tag in
                                Button(action: {
                                    onTagAdded(tag)
                                }) {
                                    Text("#\(tag)")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.blue)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(Color.blue.opacity(0.1))
                                                .overlay(
                                                    RoundedRectangle(cornerRadius: 20)
                                                        .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                                                )
                                        )
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                
                // Bottom padding to ensure content is not cut off
                Color.clear
                    .frame(height: 20)
            }
            .padding(.bottom, 20)
            .onTapGesture {
                // Dismiss keyboard when tapping outside
                hideKeyboard()
            }
        }
        .navigationTitle("Add Tag")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("Cancel") {
                    dismiss()
                }
            }
        }
        }
    }
    
    private func addNewTag() {
        let tagName = newTagText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !tagName.isEmpty else { return }
        
        onTagAdded(tagName)
    }
    
    private func cancelNewTag() {
        isAddingNewTag = false
        newTagText = ""
        isTextFieldFocused = false
    }
}

// MARK: - Batch Remove Tag View
struct BatchRemoveTagView: View {
    let selectedExpenses: [ExpenseRecord]
    let onTagRemoved: (String) -> Void
    @Environment(\.dismiss) private var dismiss
    
    // Get all common tags from selected expenses
    private var commonTags: [String] {
        guard !selectedExpenses.isEmpty else { return [] }
        
        // Start with the first expense's tags
        var result = Set(selectedExpenses.first?.tags ?? [])
        
        // Find intersection with all other expenses
        for expense in selectedExpenses.dropFirst() {
            result = result.intersection(Set(expense.tags))
        }
        
        return Array(result).sorted()
    }
    
    // Get all tags from selected expenses (any tag that appears in at least one expense)
    private var allTagsInSelection: [String] {
        let allTags = selectedExpenses.flatMap { $0.tags }
        return Array(Set(allTags)).sorted()
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // Header info
                VStack(spacing: 8) {
                    Text("Remove Tags from \(selectedExpenses.count) Expenses")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    Text("Select tags to remove from the selected expenses")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top)
                
                if commonTags.isEmpty && allTagsInSelection.isEmpty {
                    // No tags found
                    VStack(spacing: 16) {
                        Image(systemName: "tag.slash")
                            .font(.system(size: 48))
                            .foregroundColor(.gray)
                        
                        Text("No Tags Found")
                            .font(.title2)
                            .fontWeight(.medium)
                            .foregroundColor(.secondary)
                        
                        Text("The selected expenses don't have any tags to remove")
                            .font(.body)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.vertical, 40)
                } else {
                    // Common tags section (appears in ALL selected expenses)
                    if !commonTags.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Common Tags (in all selected expenses)")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                            
                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 120))
                            ], spacing: 12) {
                                ForEach(commonTags, id: \.self) { tag in
                                    Button(action: {
                                        onTagRemoved(tag)
                                    }) {
                                        HStack(spacing: 8) {
                                            Image(systemName: "minus.circle.fill")
                                                .foregroundColor(.red)
                                            Text("#\(tag)")
                                        }
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.red)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(Color.red.opacity(0.1))
                                                .overlay(
                                                    RoundedRectangle(cornerRadius: 20)
                                                        .stroke(Color.red.opacity(0.3), lineWidth: 1)
                                                )
                                        )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                    
                    // All tags section (appears in at least one expense)
                    let otherTags = allTagsInSelection.filter { !commonTags.contains($0) }
                    if !otherTags.isEmpty {
                        VStack(alignment: .leading, spacing: 12) {
                            HStack {
                                Text("Other Tags (in some selected expenses)")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)
                                Spacer()
                            }
                            
                            LazyVGrid(columns: [
                                GridItem(.adaptive(minimum: 120))
                            ], spacing: 12) {
                                ForEach(otherTags, id: \.self) { tag in
                                    Button(action: {
                                        onTagRemoved(tag)
                                    }) {
                                        HStack(spacing: 8) {
                                            Image(systemName: "minus.circle")
                                                .foregroundColor(.orange)
                                            Text("#\(tag)")
                                        }
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.orange)
                                        .padding(.horizontal, 16)
                                        .padding(.vertical, 8)
                                        .background(
                                            RoundedRectangle(cornerRadius: 20)
                                                .fill(Color.orange.opacity(0.1))
                                                .overlay(
                                                    RoundedRectangle(cornerRadius: 20)
                                                        .stroke(Color.orange.opacity(0.3), lineWidth: 1)
                                                )
                                        )
                                    }
                                    .buttonStyle(PlainButtonStyle())
                                }
                            }
                        }
                        .padding(.horizontal)
                    }
                }
                
                Spacer()
            }
            .navigationTitle("Remove Tags")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Load More Button Component
struct LoadMoreButton: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Spacer()
                HStack(spacing: 8) {
                    Image(systemName: "arrow.down.circle")
                        .font(.subheadline)
                    Text("Load More")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
                Spacer()
            }
            .padding(.vertical, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.blue.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - Expense Log View with Pre-selected Time Filter
struct ExpenseLogViewWithTimeFilter: View {
    let selectedTimeRange: ExpenseLogView.TimeRange
    @EnvironmentObject private var dataManager: DataManager
    @State private var searchText = ""
    @State private var showingFilters = false

    var filteredExpenses: [ExpenseRecord] {
        var expenses = dataManager.expenses

        // Apply search filter with enhanced tag search
        if !searchText.isEmpty {
            expenses = expenses.filter { expense in
                // Check if search text starts with # for tag search
                if searchText.hasPrefix("#") {
                    let tagQuery = String(searchText.dropFirst()).lowercased()
                    return expense.tags.contains { $0.lowercased().contains(tagQuery) }
                } else {
                    // Regular full-text search
                    return expense.matches(searchText: searchText)
                }
            }
        }

        // Apply time range filter
        let calendar = Calendar.current
        let now = Date()

        switch selectedTimeRange {
        case .all:
            break
        case .today:
            expenses = expenses.filter { calendar.isDate($0.date, inSameDayAs: now) }
        case .week:
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
            expenses = expenses.filter { $0.date >= weekAgo }
        case .month:
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!
            expenses = expenses.filter { $0.date >= monthAgo }
        case .year:
            let yearAgo = calendar.date(byAdding: .year, value: -1, to: now)!
            expenses = expenses.filter { $0.date >= yearAgo }
        }

        return expenses.sorted { $0.date > $1.date }
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 0) {
                // Search Bar
                ModernSearchBar(text: $searchText)
                    .padding(.horizontal, 16)
                    .padding(.top, 8)

                // Filter Controls
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        // Time Range Filter (pre-selected)
                        HStack(spacing: 8) {
                            Image(systemName: "calendar")
                                .foregroundColor(.blue)
                                .font(.caption)
                            Text(selectedTimeRange.rawValue)
                                .font(.caption)
                                .fontWeight(.medium)
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(16)


                    }
                    .padding(.horizontal, 16)
                }
                .padding(.vertical, 8)

                // Expense List
                if filteredExpenses.isEmpty {
                    EmptyLogView()
                } else {
                    List {
                        ForEach(filteredExpenses) { expense in
                            NavigationLink(destination: ExpenseDetailView(expenseId: expense.id)) {
                                CompactExpenseCard(expense: expense)
                            }
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("Expense Log")
            .navigationBarTitleDisplayMode(.large)
            
        }
    }
}



// MARK: - Stat Card for Home View
struct StatCard: View {
    let title: String
    let amount: Decimal
    let currency: String
    let color: Color
    let icon: String

    private func formatCurrency(amount: Decimal, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        } else {
            // Fallback formatting
            return "\(amount.formatted(.number.precision(.fractionLength(2)))) \(currency)"
        }
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.subheadline)
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
            }

            Text("\(formatCurrency(amount: amount, currency: currency))")
                .font(.headline)
                .fontWeight(.bold)
                .foregroundColor(.primary)
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
    }
}

// MARK: - Compact Expense Card for Home View
struct CompactExpenseCard: View {
    let expense: ExpenseRecord
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared

    var body: some View {
        HStack(spacing: 12) {
                // Amount
                VStack(alignment: .trailing, spacing: 2) {
                    Text(expense.formattedAmount)
                        .font(.system(size: 16, weight: .semibold, design: .rounded))
                        .foregroundColor(.primary)

                    // Converted amount
                    if expense.currency != baseCurrencyManager.baseCurrency {
                        let convertedAmount = convertToBaseCurrency(
                            amount: expense.amount,
                            fromCurrency: expense.currency,
                            expense: expense
                        )
                        // Only show converted amount if we have valid exchange rate data
                        if hasValidExchangeRate(from: expense.currency, to: baseCurrencyManager.baseCurrency, expense: expense) {
                            Text("≈ \(formatCurrency(amount: convertedAmount, currency: baseCurrencyManager.baseCurrency))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                    }
                }

                VStack(alignment: .leading, spacing: 4) {
                    // Merchant
                    Text(expense.displayMerchant)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    // Date and location
                    HStack(spacing: 8) {
                        Text(DateFormatter.abbreviatedMonthFormatter.string(from: expense.effectiveDate))
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if let location = expense.locationName, !location.isEmpty {
                            Text("•")
                                .font(.caption)
                                .foregroundColor(.secondary)

                            Text(location)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }

                    // Tags
                    if !expense.tags.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 4) {
                                ForEach(expense.tags.prefix(3), id: \.self) { tag in
                                    Text("#\(tag)")
                                        .font(.caption2)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.blue.opacity(0.1))
                                        .foregroundColor(.blue)
                                        .cornerRadius(4)
                                }
                                if expense.tags.count > 3 {
                                    Text("+\(expense.tags.count - 3)")
                                        .font(.caption2)
                                        .foregroundColor(.secondary)
                                }
                            }
                        }
                    }
                }

                Spacer()

                // Confidence indicator
                if expense.confidence != .high {
                    Image(systemName: "exclamationmark.triangle.fill")
                        .font(.caption)
                        .foregroundColor(.orange)
                }

                // 移除重复的箭头图标，NavigationLink会自动显示
            }
            .padding(16)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
    }

    // 智能汇率转换：优先使用预存储，然后使用API
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    private func formatCurrency(amount: Decimal, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        } else {
            // Fallback formatting
            return "\(amount.formatted(.number.precision(.fractionLength(2)))) \(currency)"
        }
    }

    // Check if we have valid exchange rate data (not fallback 1:1)
    private func hasValidExchangeRate(from: String, to: String, expense: ExpenseRecord? = nil) -> Bool {
        // If same currency, no conversion needed
        if from == to { return false }

        // Strategy 1: Check if we have pre-stored exchange rate data
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           majorAmounts[to] != nil {
            return true
        }

        // Strategy 2: Check if we have valid API exchange rate data
        if cloudflareExchangeService.getRate(from: from, to: to) != nil {
            return true
        }

        return false
    }
}

// MARK: - Monthly Expense Grid
struct MonthlyExpenseGrid: View {
    let expenses: [ExpenseRecord]

    private let columns = [
        GridItem(.flexible()),
        GridItem(.flexible())
    ]

    var body: some View {
        LazyVGrid(columns: columns, spacing: 12) {
            ForEach(expenses.prefix(6), id: \.id) { expense in
                NavigationLink(destination: ExpenseDetailView(expenseId: expense.id)) {
                    MonthlyExpenseCard(expense: expense)
                }
            }
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - Monthly Expense Card
struct MonthlyExpenseCard: View {
    let expense: ExpenseRecord
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
                // Header with amount and date
                HStack {
                    VStack(alignment: .leading, spacing: 2) {
                        Text(expense.formattedAmount)
                            .font(.system(size: 16, weight: .bold, design: .rounded))
                            .foregroundColor(.primary)

                        // Converted amount
                        if expense.currency != baseCurrencyManager.baseCurrency {
                            let convertedAmount = convertToBaseCurrency(
                                amount: expense.amount,
                                fromCurrency: expense.currency,
                                expense: expense
                            )
                            // Only show converted amount if we have valid exchange rate data
                            if hasValidExchangeRate(from: expense.currency, to: baseCurrencyManager.baseCurrency, expense: expense) {
                                Text("≈ \(formatCurrency(amount: convertedAmount, currency: baseCurrencyManager.baseCurrency))")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }

                    Spacer()

                    Text(DateFormatter.abbreviatedMonthFormatter.string(from: expense.effectiveDate))
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                // Merchant name
                Text(expense.displayMerchant)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)

                // Tags or location
                HStack {
                    if !expense.tags.isEmpty {
                        Text("#\(expense.tags.first!)")
                            .font(.caption2)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.blue.opacity(0.1))
                            .foregroundColor(.blue)
                            .cornerRadius(4)
                    }

                    if let location = expense.locationName, !location.isEmpty {
                        Image(systemName: "location.fill")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                        Text(location)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                            .lineLimit(1)
                    }

                    Spacer()

                    // Confidence indicator
                    if expense.confidence != .high {
                        Circle()
                            .fill(expense.confidence.color)
                            .frame(width: 6, height: 6)
                    }
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.04), radius: 3, x: 0, y: 1)
            )
    }

    // 智能汇率转换：优先使用预存储，然后使用API
    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    private func formatCurrency(amount: Decimal, currency: String) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        } else {
            // Fallback formatting
            return "\(amount.formatted(.number.precision(.fractionLength(2)))) \(currency)"
        }
    }

    // Check if we have valid exchange rate data (not fallback 1:1)
    private func hasValidExchangeRate(from: String, to: String, expense: ExpenseRecord? = nil) -> Bool {
        // If same currency, no conversion needed
        if from == to { return false }

        // Strategy 1: Check if we have pre-stored exchange rate data
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           majorAmounts[to] != nil {
            return true
        }

        // Strategy 2: Check if we have valid API exchange rate data
        if cloudflareExchangeService.getRate(from: from, to: to) != nil {
            return true
        }

        return false
    }
}

// MARK: - Empty Monthly View
struct EmptyMonthlyView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "calendar.badge.exclamationmark")
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Expenses This Month")
                    .font(.headline)
                    .fontWeight(.semibold)

                Text("Start tracking your expenses to see monthly activity")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(32)
    }
}

// MARK: - Empty Recent View for Home
struct EmptyRecentView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "tray")
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Recent Expenses")
                    .font(.headline)
                    .fontWeight(.semibold)

                Text("Start tracking your expenses to see them here")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
        }
        .padding(32)
    }
}



// MARK: - Log Expense Row for ExpenseLogView
struct LogExpenseRow: View {
    let expense: ExpenseRecord

    var body: some View {
        HStack(spacing: 16) {
            HStack(spacing: 16) {
                // Date Column
                VStack(alignment: .leading, spacing: 2) {
                    Text(expense.date, format: .dateTime.month(.abbreviated).day())
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(expense.date, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
                .frame(width: 50, alignment: .leading)

                // Merchant and Details
                VStack(alignment: .leading, spacing: 4) {
                    Text(expense.merchant ?? "Unknown")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                        .lineLimit(1)

                    HStack {
                        if let location = expense.locationName {
                            Text(location)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }

                        if !expense.tags.isEmpty {
                            Text("• \(expense.tags.joined(separator: ", "))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .lineLimit(1)
                        }
                    }
                }

                Spacer()

                // Amount
                VStack(alignment: .trailing, spacing: 2) {
                    Text(expense.formattedAmount)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    if let paymentMethod = expense.paymentMethod {
                        Text(paymentMethod)
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding(.vertical, 8)
        }
    }
}

// MARK: - Empty Log View
struct EmptyLogView: View {
    var body: some View {
        VStack(spacing: 24) {
            Spacer()

            Image(systemName: "list.bullet.clipboard")
                .font(.system(size: 64))
                .foregroundColor(.secondary)

            VStack(spacing: 12) {
                Text("No Expenses Found")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("Try adjusting your filters or add some expenses to get started")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, 32)
            }

            Spacer()
        }
    }
}

// MARK: - Expense Card Share View
struct ExpenseCardShareView: View {
    let expenses: [ExpenseRecord]
    let onShare: (UIImage, String) -> Void
    @State private var selectedExpense: ExpenseRecord?

    var recentExpenses: [ExpenseRecord] {
        Array(expenses.sorted { $0.date > $1.date }.prefix(5))
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Share an expense")
                    .font(.headline)
                    .foregroundColor(.primary)

                Spacer()

                Text("(\(expenses.count) total)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.horizontal, 20)

            if recentExpenses.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "creditcard.trianglebadge.exclamationmark")
                        .font(.system(size: 48))
                        .foregroundColor(.gray)

                    Text("No expenses to share")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    Text("Add some expenses first to create shareable cards")
                        .font(.body)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    // Debug info
                    Text("Debug: Total expenses = \(expenses.count)")
                        .font(.caption)
                        .foregroundColor(.red)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 40)
            } else {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(recentExpenses) { expense in
                            ShareableExpenseCard(expense: expense) {
                                print("Generating card for: \(expense.merchant ?? "Unknown") - \(expense.formattedAmount)")
                                generateExpenseCard(expense)
                            }
                        }
                    }
                    .padding(.horizontal, 20)
                }

                // Debug info
                Text("Debug: Showing \(recentExpenses.count) recent expenses")
                    .font(.caption)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 20)
            }
        }
    }

    private func generateExpenseCard(_ expense: ExpenseRecord) {
        // Create a simple but effective expense card
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 400, height: 300))
        let image = renderer.image { context in
            let cgContext = context.cgContext

            // Background
            cgContext.setFillColor(UIColor.systemBlue.cgColor)
            cgContext.fill(CGRect(x: 0, y: 0, width: 400, height: 300))

            // White card background
            cgContext.setFillColor(UIColor.white.cgColor)
            let cardRect = CGRect(x: 20, y: 40, width: 360, height: 220)
            cgContext.fill(cardRect)

            // Add shadow effect
            cgContext.setShadow(offset: CGSize(width: 0, height: 4), blur: 8, color: UIColor.black.withAlphaComponent(0.2).cgColor)
            cgContext.fill(cardRect)
            cgContext.setShadow(offset: .zero, blur: 0, color: nil)

            // Amount text
            let amountText = "\(expense.formattedAmount)"
            let amountAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 32),
                .foregroundColor: UIColor.systemBlue
            ]
            _ = amountText.size(withAttributes: amountAttributes)
            amountText.draw(at: CGPoint(x: 40, y: 80), withAttributes: amountAttributes)

            // Merchant text
            if let merchant = expense.merchant {
                let merchantAttributes: [NSAttributedString.Key: Any] = [
                    .font: UIFont.systemFont(ofSize: 20),
                    .foregroundColor: UIColor.darkGray
                ]
                merchant.draw(at: CGPoint(x: 40, y: 130), withAttributes: merchantAttributes)
            }

            // Date
            let dateFormatter = DateFormatter()
            dateFormatter.dateStyle = .medium
            let dateText = dateFormatter.string(from: expense.date)
            let dateAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.gray
            ]
            dateText.draw(at: CGPoint(x: 40, y: 170), withAttributes: dateAttributes)

            // Branding
            let brandText = "FinPin"
            let brandAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 18),
                .foregroundColor: UIColor.systemBlue
            ]
            brandText.draw(at: CGPoint(x: 40, y: 210), withAttributes: brandAttributes)
        }

        let shareText = "💳 Just spent \(expense.formattedAmount)"
        + (expense.merchant != nil ? " at \(expense.merchant!)" : "")
        + " #FinPin #ExpenseTracking"

        onShare(image, shareText)
    }
}

// MARK: - Shareable Expense Card
struct ShareableExpenseCard: View {
    let expense: ExpenseRecord
    let onShare: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(expense.formattedAmount)
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)

                    if let merchant = expense.merchant {
                        Text(merchant)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }

                    Text(expense.date, style: .date)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: onShare) {
                    Image(systemName: "square.and.arrow.up")
                        .font(.title2)
                        .foregroundColor(.blue)
                }
            }

            if !expense.tags.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 8) {
                        ForEach(expense.tags, id: \.self) { tag in
                            Text(tag)
                                .font(.caption)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(Color.blue.opacity(0.1))
                                .foregroundColor(.blue)
                                .cornerRadius(8)
                        }
                    }
                    .padding(.horizontal, 1)
                }
            }
        }
        .padding(16)
        .frame(width: 280)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Monthly Report Share View
struct MonthlyReportShareView: View {
    let expenses: [ExpenseRecord]
    let onShare: (UIImage, String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Monthly spending summary")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal, 20)

            // Monthly summary card
            MonthlyReportCard(expenses: expenses) {
                generateMonthlyReport()
            }
            .padding(.horizontal, 20)
        }
    }

    private func generateMonthlyReport() {
        // Generate monthly report image and text
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        let monthlyExpenses = expenses.filter { expense in
            let expenseMonth = Calendar.current.component(.month, from: expense.date)
            let expenseYear = Calendar.current.component(.year, from: expense.date)
            return expenseMonth == currentMonth && expenseYear == currentYear
        }

        let totalAmount = monthlyExpenses.reduce(Decimal.zero) { $0 + $1.amount }
        let monthName = DateFormatter().monthSymbols[currentMonth - 1]

        // Create a simple report image
        let renderer = UIGraphicsImageRenderer(size: CGSize(width: 400, height: 400))
        let image = renderer.image { context in
            let cgContext = context.cgContext

            // Background gradient
            cgContext.setFillColor(UIColor.systemBlue.cgColor)
            cgContext.fill(CGRect(x: 0, y: 0, width: 400, height: 400))

            // White card
            cgContext.setFillColor(UIColor.white.cgColor)
            let cardRect = CGRect(x: 20, y: 40, width: 360, height: 320)
            cgContext.fill(cardRect)

            // Title
            let titleText = "\(monthName) \(currentYear)"
            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 24),
                .foregroundColor: UIColor.darkGray
            ]
            titleText.draw(at: CGPoint(x: 40, y: 80), withAttributes: titleAttributes)

            // Total amount
            let totalText = "$\(totalAmount)"
            let totalAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.boldSystemFont(ofSize: 36),
                .foregroundColor: UIColor.systemBlue
            ]
            totalText.draw(at: CGPoint(x: 40, y: 140), withAttributes: totalAttributes)

            // Transaction count
            let countText = "\(monthlyExpenses.count) transactions"
            let countAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 18),
                .foregroundColor: UIColor.gray
            ]
            countText.draw(at: CGPoint(x: 40, y: 200), withAttributes: countAttributes)

            // Branding
            let brandText = "FinPin Monthly Report"
            let brandAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16),
                .foregroundColor: UIColor.systemBlue
            ]
            brandText.draw(at: CGPoint(x: 40, y: 280), withAttributes: brandAttributes)
        }

        let shareText = "📊 My \(monthName) spending: $\(totalAmount) across \(monthlyExpenses.count) transactions #FinPin #MonthlyReport"
        onShare(image, shareText)
    }
}

// MARK: - Yearly Report Share View
struct YearlyReportShareView: View {
    let expenses: [ExpenseRecord]
    let onShare: (UIImage, String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Yearly spending summary")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal, 20)

            Text("Coming soon...")
                .font(.body)
                .foregroundColor(.secondary)
                .padding(.horizontal, 20)
        }
    }
}

// MARK: - Personal Insights Share View
struct PersonalInsightsShareView: View {
    let expenses: [ExpenseRecord]
    let onShare: (UIImage, String) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Personal spending insights")
                .font(.headline)
                .foregroundColor(.primary)
                .padding(.horizontal, 20)

            Text("Coming soon...")
                .font(.body)
                .foregroundColor(.secondary)
                .padding(.horizontal, 20)
        }
    }
}

// MARK: - Monthly Report Card
struct MonthlyReportCard: View {
    let expenses: [ExpenseRecord]
    let onShare: () -> Void
    @StateObject private var exchangeRateService = ExchangeRateService.shared

    var monthlyTotal: Decimal {
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        let currentMonthExpenses = expenses.filter { expense in
            let expenseMonth = Calendar.current.component(.month, from: expense.date)
            let expenseYear = Calendar.current.component(.year, from: expense.date)
            return expenseMonth == currentMonth && expenseYear == currentYear
        }

        return calculateTotalInBaseCurrency(currentMonthExpenses)
    }

    private func calculateTotalInBaseCurrency(_ expenses: [ExpenseRecord]) -> Decimal {
        expenses.reduce(Decimal(0)) { total, expense in
            // 优先使用记录时保存的转换金额
            if let convertedAmount = expense.convertedAmount,
               let expenseBaseCurrency = expense.baseCurrency,
               expenseBaseCurrency == exchangeRateService.baseCurrency {
                return total + convertedAmount
            }

            // 如果没有保存的转换金额，使用当前汇率转换
            if expense.currency == exchangeRateService.baseCurrency {
                return total + expense.amount
            } else if let convertedAmount = exchangeRateService.convertAmount(
                expense.amount,
                from: expense.currency,
                to: exchangeRateService.baseCurrency
            ) {
                return total + convertedAmount
            } else {
                // 如果无法转换，记录错误但不加入总额
                print("Warning: Unable to convert \(expense.amount) \(expense.currency) to \(exchangeRateService.baseCurrency)")
                return total
            }
        }
    }

    var monthlyCount: Int {
        let currentMonth = Calendar.current.component(.month, from: Date())
        let currentYear = Calendar.current.component(.year, from: Date())

        return expenses.filter { expense in
            let expenseMonth = Calendar.current.component(.month, from: expense.date)
            let expenseYear = Calendar.current.component(.year, from: expense.date)
            return expenseMonth == currentMonth && expenseYear == currentYear
        }.count
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text("This Month")
                        .font(.headline)
                        .foregroundColor(.primary)

                    Text("\(monthlyTotal, format: .currency(code: exchangeRateService.baseCurrency))")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.blue)

                    Text("\(monthlyCount) transactions")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Button(action: onShare) {
                    VStack(spacing: 8) {
                        Image(systemName: "square.and.arrow.up.fill")
                            .font(.title2)
                            .foregroundColor(.white)

                        Text("Share")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.white)
                    }
                    .padding(16)
                    .background(Color.blue)
                    .cornerRadius(12)
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Share Sheet
struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - CGContext Extension
extension CGContext {
    func fillRoundedRect(_ rect: CGRect, cornerRadius: CGFloat) {
        let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
        addPath(path.cgPath)
        fillPath()
    }
}

// MARK: - Supporting Types
struct ExpenseLocation: Identifiable {
    let id = UUID()
    let expense: ExpenseRecord

    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(latitude: expense.latitude, longitude: expense.longitude)
    }
}

struct CompactMapLocation: Identifiable {
    let id = UUID()
    let coordinate: CLLocationCoordinate2D
}



// MARK: - Statistics View
struct StatisticsView: View {
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared

    var totalAmount: Decimal {
        calculateTotalInBaseCurrency(dataManager.expenses)
    }

    var thisMonthAmount: Decimal {
        let calendar = Calendar.current
        let now = Date()
        let currentMonth = calendar.component(.month, from: now)
        let currentYear = calendar.component(.year, from: now)

        let thisMonthExpenses = dataManager.expenses
            .filter { expense in
                let expenseMonth = calendar.component(.month, from: expense.effectiveDate)
                let expenseYear = calendar.component(.year, from: expense.effectiveDate)
                return expenseMonth == currentMonth && expenseYear == currentYear
            }

        return calculateTotalInBaseCurrency(thisMonthExpenses)
    }

    var expensesByTag: [String: [ExpenseRecord]] {
        dataManager.getExpensesByTag()
    }

    var expensesByMonth: [String: [ExpenseRecord]] {
        dataManager.getExpensesByMonth()
    }

    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 24) {
                    // Overview card
                    OverviewCard(
                        totalAmount: totalAmount,
                        totalCount: dataManager.expenses.count,
                        thisMonthAmount: thisMonthAmount
                    )

                    // TagsStatistics
                    if !expensesByTag.isEmpty {
                        TagStatsView(expensesByTag: expensesByTag)
                    }

                    // Monthly Statistics
                    if !expensesByMonth.isEmpty {
                        MonthlyStatsView(expensesByMonth: expensesByMonth)
                    }

                    // Empty state
                    if dataManager.expenses.isEmpty {
                        EmptyStatsView {
                            dataManager.createSampleData()
                        }
                    }
                }
                .padding(20)
            }
            .navigationTitle("Statistics")
            .navigationBarTitleDisplayMode(.large)
        }
    }

    // 使用与Home视图相同的汇率转换逻辑
    private func calculateTotalInBaseCurrency(_ expenses: [ExpenseRecord]) -> Decimal {
        expenses.reduce(Decimal(0)) { total, expense in
            let convertedAmount = convertToBaseCurrency(
                amount: expense.amount,
                fromCurrency: expense.currency,
                expense: expense
            )
            return total + convertedAmount
        }
    }

    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }
}

struct OverviewCard: View {
    let totalAmount: Decimal
    let totalCount: Int
    let thisMonthAmount: Decimal
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("Total Expenses")
                    .font(.headline)
                    .foregroundColor(.secondary)

                VStack(spacing: 2) {
                    Text("\(totalAmount, format: .currency(code: baseCurrencyManager.baseCurrency))")
                        .font(.system(size: 36, weight: .bold, design: .rounded))
                        .foregroundColor(.primary)

                    Text("in \(baseCurrencyManager.baseCurrency)")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .opacity(0.8)
                }
            }

            HStack(spacing: 40) {
                VStack(spacing: 4) {
                    Text("\(totalCount)")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)

                    Text("expenses")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                VStack(spacing: 4) {
                    Text("\(thisMonthAmount, format: .currency(code: baseCurrencyManager.baseCurrency))")
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)

                    Text("This Month")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(24)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemGray6))
        )
    }
}

struct TagStatsView: View {
    let expensesByTag: [String: [ExpenseRecord]]
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @State private var showingAllTags = false

    // 限制显示的标签数量
    private let maxDisplayedTags = 10

    private func calculateTotalInBaseCurrency(_ expenses: [ExpenseRecord]) -> Decimal {
        expenses.reduce(Decimal(0)) { total, expense in
            let convertedAmount = convertToBaseCurrency(
                amount: expense.amount,
                fromCurrency: expense.currency,
                expense: expense
            )
            return total + convertedAmount
        }
    }

    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    var sortedTags: [(String, [ExpenseRecord])] {
        expensesByTag.sorted { first, second in
            let firstTotal = calculateTotalInBaseCurrency(first.value)
            let secondTotal = calculateTotalInBaseCurrency(second.value)
            return firstTotal > secondTotal
        }
    }

    var displayedTags: [(String, [ExpenseRecord])] {
        if showingAllTags || sortedTags.count <= maxDisplayedTags {
            return sortedTags
        } else {
            return Array(sortedTags.prefix(maxDisplayedTags))
        }
    }

    var hasMoreTags: Bool {
        return sortedTags.count > maxDisplayedTags
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Tags Statistics")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                if hasMoreTags && !showingAllTags {
                    Text("\(sortedTags.count) total")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            VStack(spacing: 12) {
                ForEach(displayedTags, id: \.0) { tag, expenses in
                    let total = calculateTotalInBaseCurrency(expenses)

                    NavigationLink(destination: ExpenseLogViewWithTagFilter(selectedTag: tag)) {
                        HStack {
                            VStack(alignment: .leading, spacing: 4) {
                                Text("#\(tag)")
                                    .font(.headline)
                                    .foregroundColor(.blue)

                                Text("\(expenses.count) expenses")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }

                            Spacer()

                            Text("\(total, format: .currency(code: baseCurrencyManager.baseCurrency))")
                                .font(.headline)
                                .fontWeight(.semibold)
                                .foregroundColor(.primary)
                        }
                        .padding(16)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(.systemBackground))
                                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                        )
                    }
                }

                // 查看更多/收起按钮
                if hasMoreTags {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingAllTags.toggle()
                        }
                    }) {
                        HStack {
                            Image(systemName: showingAllTags ? "chevron.up" : "chevron.down")
                                .font(.caption)

                            Text(showingAllTags ? "Show Less" : "Show More (\(sortedTags.count - maxDisplayedTags) more)")
                                .font(.subheadline)
                        }
                        .foregroundColor(.blue)
                        .padding(.vertical, 8)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }
}

struct MonthlyStatsView: View {
    let expensesByMonth: [String: [ExpenseRecord]]
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @State private var showingAllMonths = false

    // 限制显示的月份数量为最近6个月
    private let maxDisplayedMonths = 6

    private func calculateTotalInBaseCurrency(_ expenses: [ExpenseRecord]) -> Decimal {
        expenses.reduce(Decimal(0)) { total, expense in
            let convertedAmount = convertToBaseCurrency(
                amount: expense.amount,
                fromCurrency: expense.currency,
                expense: expense
            )
            return total + convertedAmount
        }
    }

    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, expense: ExpenseRecord? = nil) -> Decimal {
        let targetCurrency = baseCurrencyManager.baseCurrency

        // If same currency, no conversion needed
        if fromCurrency == targetCurrency {
            return amount
        }

        // Strategy 1: 使用预存储的多币种金额（最准确）
        if let expense = expense,
           let majorAmounts = expense.majorCurrencyAmounts,
           let preStoredAmount = majorAmounts[targetCurrency] {
            return preStoredAmount
        }

        // Strategy 2: 使用Cloudflare API实时转换
        if let rate = cloudflareExchangeService.getRate(from: fromCurrency, to: targetCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    var sortedMonths: [(String, [ExpenseRecord])] {
        expensesByMonth.sorted { $0.0 > $1.0 }
    }

    var displayedMonths: [(String, [ExpenseRecord])] {
        if showingAllMonths || sortedMonths.count <= maxDisplayedMonths {
            return sortedMonths
        } else {
            return Array(sortedMonths.prefix(maxDisplayedMonths))
        }
    }

    var hasMoreMonths: Bool {
        return sortedMonths.count > maxDisplayedMonths
    }

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Monthly Statistics")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.primary)

                Spacer()

                if hasMoreMonths && !showingAllMonths {
                    Text("Recent \(maxDisplayedMonths) months")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }

            VStack(spacing: 12) {
                ForEach(displayedMonths, id: \.0) { month, expenses in
                    let total = calculateTotalInBaseCurrency(expenses)

                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text(formatMonth(month))
                                .font(.headline)
                                .foregroundColor(.primary)

                            Text("\(expenses.count) expenses")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Text("\(total, format: .currency(code: baseCurrencyManager.baseCurrency))")
                            .font(.headline)
                            .fontWeight(.semibold)
                            .foregroundColor(.primary)
                    }
                    .padding(16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color(.systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 2)
                    )
                }

                // 查看更多/收起按钮
                if hasMoreMonths {
                    Button(action: {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            showingAllMonths.toggle()
                        }
                    }) {
                        HStack {
                            Image(systemName: showingAllMonths ? "chevron.up" : "chevron.down")
                                .font(.caption)

                            Text(showingAllMonths ? "Show Recent 6 Months" : "Show All Months (\(sortedMonths.count) total)")
                                .font(.subheadline)
                        }
                        .foregroundColor(.blue)
                        .padding(.vertical, 8)
                    }
                    .frame(maxWidth: .infinity)
                }
            }
        }
    }

    private func formatMonth(_ month: String) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM"

        if let date = formatter.date(from: month) {
            formatter.dateFormat = "MMM yyyy"
            return formatter.string(from: date)
        }

        return month
    }
}

struct EmptyStatsView: View {
    let onAddSample: () -> Void

    var body: some View {
        VStack(spacing: 24) {
            Image(systemName: "chart.bar.xaxis")
                .font(.system(size: 80))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text("No Statistics Data")
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)

                Text("Add expense records to view\ndetailed statistics")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button("Add Sample Data") {
                onAddSample()
            }
            .buttonStyle(.borderedProminent)
            .controlSize(.large)
        }
        .padding(.top, 60)
    }
}

// MARK: - Settings View
struct SettingsView: View {
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var exchangeRateService = ExchangeRateService.shared
    @StateObject private var cloudflareExchangeService = CloudflareExchangeRateService.shared
    @StateObject private var baseCurrencyManager = BaseCurrencyManager.shared
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @StateObject private var siriShortcutsManager = SiriShortcutsManager.shared
    @StateObject private var cloudKitManager = CloudKitManager.shared
    @StateObject private var apiConfigurationManager = APIConfigurationManager.shared

    @State private var showingDeleteAlert = false
    @State private var showingSyncAlert = false
    @State private var syncAlertMessage = ""
    @State private var showingExportSheet = false
    @State private var csvContent = ""
    @State private var showingDebugView = false
    @State private var showingTagManager = false
    @State private var serverHealthStatus: ServerHealthStatus = .unknown
    @State private var isCheckingHealth = false
    @State private var showingCSVImportInSettings = false
    @State private var csvImportTextInSettings = ""
    @State private var showingCSVFilePicker = false
    @State private var showingCSVExporter = false
    @State private var csvExportContent = ""
    @State private var csvExportFilename = ""
    @State private var showingCSVImportAlert = false
    @State private var csvImportAlertMessage = ""
    @State private var showingCurrencyPicker = false
    @State private var showingSubscriptionView = false
    @State private var showingAPIConfigurationSheet = false
    @State private var showingTermsOfService = false
    @State private var showingPrivacyPolicy = false
    @State private var showingPushConfirmAlert = false
    @State private var showingPullConfirmAlert = false
    @State private var exchangeRateStatusText = "Update currency conversion rates"
    @State private var lastExchangeRateUpdate: Date?

    enum ServerHealthStatus {
        case healthy, unhealthy, unknown

        var color: Color {
            switch self {
            case .healthy: return .green
            case .unhealthy: return .red
            case .unknown: return .gray
            }
        }

        var text: String {
            switch self {
            case .healthy: return "Healthy"
            case .unhealthy: return "Unhealthy"
            case .unknown: return "Unknown"
            }
        }
    }

    var body: some View {
        NavigationView {
            Form {
                // Premium Bar
                Section {
                    ZStack {
                        // Background gradient
                        RoundedRectangle(cornerRadius: 16)
                            .fill(
                                LinearGradient(
                                    gradient: Gradient(colors: [Color.blue.opacity(0.1), Color.purple.opacity(0.1)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                            )
                            .blur(radius: 10)
                        
                        // Main content
                        VStack(spacing: 12) {
                            HStack {
                                VStack(alignment: .leading, spacing: 4) {
                                    Text(subscriptionManager.hasActiveSubscription ? "Premium Plan" : "Free Plan")
                                        .font(.headline)
                                        .fontWeight(.semibold)
                                    
                                    Text(subscriptionManager.hasActiveSubscription ? 
                                        "\(dataManager.expenses.count) / unlimited expenses" : 
                                        "\(subscriptionManager.expenseCount)/\(subscriptionManager.expenseLimit) expenses used")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                    
                                    // Show subscription expiry date for Premium users
                                    if subscriptionManager.hasActiveSubscription, 
                                       let expiryText = subscriptionManager.subscriptionExpiryText {
                                        Text("\(expiryText)")
                                            .font(.caption2)
                                            .foregroundColor(.secondary)
                                    }
                                }
                                
                                Spacer()
                            }
                            
                            // Progress bar (only show for free plan)
                            if !subscriptionManager.hasActiveSubscription {
                                GeometryReader { geometry in
                                    ZStack(alignment: .leading) {
                                        Rectangle()
                                            .fill(Color.gray.opacity(0.3))
                                            .frame(height: 6)
                                            .cornerRadius(3)
                                        
                                        Rectangle()
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.blue, Color.purple]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                            .frame(
                                                width: min(
                                                    CGFloat(subscriptionManager.expenseCount) / CGFloat(subscriptionManager.expenseLimit) * geometry.size.width,
                                                    geometry.size.width
                                                ),
                                                height: 6
                                            )
                                            .cornerRadius(3)
                                    }
                                }
                                .frame(height: 6)
                            }
                            
                            // Upgrade button (only show for free plan)
                            if !subscriptionManager.hasActiveSubscription {
                                Button(action: { 
                                    showingSubscriptionView = true
                                }) {
                                    HStack {
                                        Text("Upgrade to Premium")
                                            .font(.subheadline)
                                            .fontWeight(.medium)
                                        
                                        Image(systemName: "arrow.right")
                                            .font(.caption)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 8)
                                    .background(
                                        RoundedRectangle(cornerRadius: 8)
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: [Color.blue, Color.purple]),
                                                    startPoint: .leading,
                                                    endPoint: .trailing
                                                )
                                            )
                                    )
                                    .foregroundColor(.white)
                                }
                            }
                        }
                        .padding()
                    }
                    .padding(.horizontal)
                }
                .listRowInsets(EdgeInsets())
                
                // Server Status Section
                Section("Server Status") {
                    HStack {
                        Circle()
                            .fill(serverHealthStatus.color)
                            .frame(width: 12, height: 12)

                        Text("API Server")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Spacer()

                        Text(serverHealthStatus.text)
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Button(action: checkServerHealth) {
                            if isCheckingHealth {
                                ProgressView()
                                    .scaleEffect(0.8)
                            } else {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.blue)
                            }
                        }
                        .disabled(isCheckingHealth)
                    }
                }

                // Currency Settings Section
                Section("Currency Settings") {
                    HStack {
                        Image(systemName: "dollarsign.circle.fill")
                            .foregroundColor(.green)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Base Currency")
                                .font(.headline)
                            Text("Currency for conversions and statistics")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }

                        Spacer()

                        Button(action: { showingCurrencyPicker = true }) {
                            HStack(spacing: 4) {
                                Text(baseCurrencyManager.baseCurrency)
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                Image(systemName: "chevron.right")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .foregroundColor(.blue)
                    }

                    // Exchange Rate Service
                    HStack {
                        Image(systemName: "arrow.2.squarepath")
                            .foregroundColor(.blue)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("Exchange Rates")
                                .font(.headline)
                            Text(exchangeRateStatusText)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .animation(.easeInOut(duration: 0.3), value: exchangeRateStatusText)
                        }

                        Spacer()

                        if cloudflareExchangeService.isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Button(action: {
                                updateExchangeRates()
                            }) {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.blue)
                            }
                        }
                    }
                }



                // Data Management Section
                Section("Data Management") {
                    // CSV Export
                    HStack {
                        Image(systemName: "square.and.arrow.up")
                            .foregroundColor(.blue)
                        VStack(alignment: .leading) {
                            Text("Export CSV")
                                .font(.headline)
                                .foregroundColor(.primary)
                        }
                        Spacer()

                        Button(action: {
                            exportCSVFromSettings()
                        }) {
                            Text("Export")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }

                    // CSV Import
                    HStack {
                        Image(systemName: "square.and.arrow.down")
                            .foregroundColor(.green)
                        VStack(alignment: .leading) {
                            Text("Import CSV")
                                .font(.headline)
                                .foregroundColor(.primary)
                        }
                        Spacer()

                        Button(action: {
                            showingCSVFilePicker = true
                        }) {
                            Text("Import")
                                .font(.caption)
                                .foregroundColor(.green)
                        }
                    }

                    Button(action: {
                        showingTagManager = true
                    }) {
                        HStack {
                            Image(systemName: "tag")
                                .foregroundColor(.orange)
                            Text("View Tags")
                                .font(.headline)
                                .foregroundColor(.primary)
                        }
                    }
                }

  
                // App Features Section
                Section("Experimental Features") {
                    // Siri Voice Commands
                    HStack {
                        Image(systemName: "mic.fill")
                            .foregroundColor(.blue)
                        VStack(alignment: .leading) {
                            Text("Siri Voice Commands")
                                .font(.headline)
                            Text("Say \"Hey Siri, add expense to FinPin\" to record transactions")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()

                        Toggle("", isOn: $siriShortcutsManager.isEnabled)
                            .labelsHidden()
                            .onChange(of: siriShortcutsManager.isEnabled) { enabled in
                                siriShortcutsManager.toggleShortcuts(enabled)
                            }
                    }

                    // Shortcuts Integration
                    HStack {
                        Image(systemName: "wand.and.rays")
                            .foregroundColor(.orange)
                        VStack(alignment: .leading) {
                            Text("Shortcuts Integration")
                                .font(.headline)
                            Text("Process text from screenshots and other sources")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        Spacer()

                        Button(action: openShortcutsApp) {
                            Text("Setup")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }





                    HStack {
                        Image(systemName: "icloud.fill")
                            .foregroundColor(.blue)
                        VStack(alignment: .leading) {
                            Text("iCloud Sync")
                                .font(.headline)
                                .foregroundColor(.primary)

                            if cloudKitManager.isCloudKitAvailable {
                                if let lastSync = cloudKitManager.lastSyncDate {
                                    Text("Last sync: \(lastSync, formatter: DateFormatter.shortDateTime)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                } else {
                                    Text("Ready to sync")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            } else {
                                Text("iCloud not available")
                                    .font(.caption)
                                    .foregroundColor(.orange)
                            }
                        }
                        Spacer()

                        if cloudKitManager.isSyncing {
                            HStack(spacing: 4) {
                                ProgressView()
                                    .scaleEffect(0.7)
                                Text("Syncing...")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                            }
                        } else {
                            Text("Status")
                                .font(.caption)
                                .foregroundColor(.blue)
                        }
                    }

                    // Push to iCloud - Independent Row
                    HStack {
                        Image(systemName: "icloud.and.arrow.up")
                            .foregroundColor(cloudKitManager.isCloudKitAvailable ? .green : .gray)
                        Text("Push to iCloud")
                            .font(.headline)
                            .foregroundColor(cloudKitManager.isCloudKitAvailable ? .primary : .secondary)
                        Spacer()
                        Button(action: {
                            showingPushConfirmAlert = true
                        }) {
                            Text("Push")
                                .font(.subheadline)
                                .foregroundColor(cloudKitManager.isCloudKitAvailable ? .green : .gray)
                        }
                        .disabled(!cloudKitManager.isCloudKitAvailable)
                    }

                    // Pull from iCloud - Independent Row  
                    HStack {
                        Image(systemName: "icloud.and.arrow.down")
                            .foregroundColor(cloudKitManager.isCloudKitAvailable ? .blue : .gray)
                        Text("Pull from iCloud")
                            .font(.headline)
                            .foregroundColor(cloudKitManager.isCloudKitAvailable ? .primary : .secondary)
                        Spacer()
                        Button(action: {
                            showingPullConfirmAlert = true
                        }) {
                            Text("Pull")
                                .font(.subheadline)
                                .foregroundColor(cloudKitManager.isCloudKitAvailable ? .blue : .gray)
                        }
                        .disabled(!cloudKitManager.isCloudKitAvailable)
                    }

                    // API Configuration
                    HStack {
                        Image(systemName: "gear")
                            .foregroundColor(.blue)

                        VStack(alignment: .leading, spacing: 2) {
                            Text("API Configuration")
                                .font(.headline)
                                .foregroundColor(.primary)

                            if apiConfigurationManager.isCustomModeEnabled {
                                if let activeConfig = apiConfigurationManager.activeConfiguration {
                                    Text("\(activeConfig.name)")
                                        .font(.caption)
                                        .foregroundColor(.blue)
                                } else {
                                    Text("Custom mode - No active configuration")
                                        .font(.caption)
                                        .foregroundColor(.orange)
                                }
                            } else {
                                Text("FinPin Hosted (Secure & Managed)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Spacer()

                        Button(action: { showingAPIConfigurationSheet = true }) {
                            Text("Setup")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.white)
                                .padding(.horizontal, 12)
                                .padding(.vertical, 6)
                                .background(
                                    Capsule()
                                        .fill(.blue)
                                )
                        }
                    }
                    .padding(.vertical, 4)
                }

 
                // Legal Information Section
                Section("Legal Information") {
                    Button(action: {
                        showingTermsOfService = true
                    }) {
                        HStack {
                            Image(systemName: "doc.text")
                                .foregroundColor(.blue)
                                .frame(width: 20)
                            Text("Terms of Service")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())

                    Button(action: {
                        showingPrivacyPolicy = true
                    }) {
                        HStack {
                            Image(systemName: "hand.raised")
                                .foregroundColor(.blue)
                                .frame(width: 20)
                            Text("Privacy Policy")
                                .font(.headline)
                                .foregroundColor(.primary)
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }

                               // Danger Zone
                Section("Danger Zone") {
                    Button(action: {
                        showingDeleteAlert = true
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("Clear All Data")
                                .foregroundColor(.red)
                        }
                    }
                }

                // App Info Section
                Section("App Information") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.1.0")
                            .foregroundColor(.secondary)
                    }

                    HStack {
                        Text("Build Version")
                        Spacer()
                        Text("9")
                            .foregroundColor(.secondary)
                    }

                    VStack(alignment: .leading, spacing: 8) {
                        Text("About FinPin")
                            .font(.headline)
                        Text("FinPin is an intelligent expense tracking app, automatically recording expense information through smart recognition of payment receipts.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }


                // Developer Tools Section (Hidden in production)
                #if DEBUG || targetEnvironment(simulator)
                Section("Developer Tools") {
                    Button(action: {
                        showingDebugView = true
                    }) {
                        HStack {
                            Image(systemName: "hammer")
                                .foregroundColor(.purple)
                            VStack(alignment: .leading) {
                                Text("Debug Tools")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                            }
                            Spacer()
                            Image(systemName: "chevron.right")
                                .foregroundColor(.secondary)
                                .font(.caption)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                    
                    // NEW: Clear Subscription Caches Button
                    Button(action: {
                        let subscriptionManager = SubscriptionManager.shared
                        subscriptionManager.clearAllSubscriptionCaches()
                        
                        // Show confirmation
                        NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                            message: "All subscription caches cleared successfully!",
                            type: .success
                        ))
                    }) {
                        HStack {
                            Image(systemName: "arrow.clockwise")
                                .foregroundColor(.red)
                            VStack(alignment: .leading) {
                                Text("Clear Subscription Caches")
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text("Reset subscription status for testing")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                            Spacer()
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                #elseif !DEBUG
                // Show Developer Tools in TestFlight builds
                if Bundle.main.isTestFlight {
                    Section("Developer Tools") {
                        Button(action: {
                            showingDebugView = true
                        }) {
                            HStack {
                                Image(systemName: "hammer")
                                    .foregroundColor(.purple)
                                VStack(alignment: .leading) {
                                    Text("Debug Tools")
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                }
                                Spacer()
                                Image(systemName: "chevron.right")
                                    .foregroundColor(.secondary)
                                    .font(.caption)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                        
                        // NEW: Clear Subscription Caches Button
                        Button(action: {
                            let subscriptionManager = SubscriptionManager.shared
                            subscriptionManager.clearAllSubscriptionCaches()
                            
                            // Show confirmation
                            NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                                message: "All subscription caches cleared successfully!",
                                type: .success
                            ))
                        }) {
                            HStack {
                                Image(systemName: "arrow.clockwise")
                                    .foregroundColor(.red)
                                VStack(alignment: .leading) {
                                    Text("Clear Subscription Caches")
                                        .font(.headline)
                                        .foregroundColor(.primary)
                                    Text("Reset subscription status for testing")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                Spacer()
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                #endif
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.large)
            .onAppear {
                checkServerHealth()
            }
            .onReceive(NotificationCenter.default.publisher(for: .subscriptionStatusChanged)) { _ in
                // Force refresh the view when subscription status changes
                print("🔔 Settings view received subscription status change notification")
            }
        }
        .alert("Clear All Data", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Confirm Delete", role: .destructive) {
                deleteAllData()
            }
        } message: {
            Text("This operation will permanently delete all expense records (include iCloud data!!!) and cannot be undone. Are you sure you want to continue?")
        }
        .alert("CSV Import Result", isPresented: $showingCSVImportAlert) {
            Button("OK") { }
        } message: {
            Text(csvImportAlertMessage)
        }
        .sheet(isPresented: $showingExportSheet) {
            ShareSheet(activityItems: [csvContent])
        }
        .sheet(isPresented: $showingDebugView) {
            DebugView()
        }
        .sheet(isPresented: $showingTagManager) {
            TagManagerView()
        }
        .sheet(isPresented: $showingCSVImportInSettings) {
            CSVImportView(csvText: $csvImportTextInSettings, onImport: { csvContent in
                let importedExpenses = CSVManager.importExpensesFromCSV(csvContent)
                for expense in importedExpenses {
                    dataManager.addExpense(expense)
                }
            })
        }
        .sheet(isPresented: $showingCurrencyPicker) {
            BaseCurrencyPickerView(
                selectedCurrency: baseCurrencyManager.baseCurrency,
                onCurrencySelected: { currency in
                    baseCurrencyManager.baseCurrency = currency
                    showingCurrencyPicker = false
                }
            )
        }
        .sheet(isPresented: $showingSubscriptionView) {
            SubscriptionView()
        }
        .onReceive(NotificationCenter.default.publisher(for: .showPurchaseView)) { _ in
            showingSubscriptionView = true
        }
        .onReceive(NotificationCenter.default.publisher(for: .subscriptionStatusChanged)) { _ in
            // The @Published properties in SubscriptionManager will automatically update the UI
            // No need to manually call checkSubscriptionStatus() here
            print("🔔 Subscription status changed, UI will update automatically")
        }
        .sheet(isPresented: $showingAPIConfigurationSheet) {
            APIConfigurationView(
                onSave: {
                    // Configuration is automatically saved by APIConfigurationManager
                    print("API configuration updated")
                }
            )
        }
        .fileImporter(
            isPresented: $showingCSVFilePicker,
            allowedContentTypes: [.commaSeparatedText, .plainText],
            allowsMultipleSelection: false
        ) { result in
            handleCSVFileImport(result: result)
        }
        .fileExporter(
            isPresented: $showingCSVExporter,
            document: CSVDocument(content: csvExportContent),
            contentType: .commaSeparatedText,
            defaultFilename: csvExportFilename
        ) { result in
            handleCSVFileExport(result: result)
        }
        .sheet(isPresented: $showingTermsOfService) {
            LegalDocumentView(documentType: .termsOfService)
        }
        .sheet(isPresented: $showingPrivacyPolicy) {
            LegalDocumentView(documentType: .privacyPolicy)
        }
        .alert("iCloud Sync", isPresented: $showingSyncAlert) {
            Button("OK") { }
        } message: {
            Text(syncAlertMessage)
        }
        .alert("Push to iCloud", isPresented: $showingPushConfirmAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Push", role: .destructive) {
                Task {
                    do {
                        try await dataManager.syncToiCloud()
                        syncAlertMessage = "Local data pushed to iCloud successfully!"
                        showingSyncAlert = true
                    } catch {
                        syncAlertMessage = "Push failed: \(error.localizedDescription)"
                        showingSyncAlert = true
                    }
                }
            }
        } message: {
            Text("This will overwrite all iCloud data with your local expenses. Are you sure?")
        }
        .alert("Pull from iCloud", isPresented: $showingPullConfirmAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Pull", role: .destructive) {
                Task {
                    do {
                        try await dataManager.syncFromiCloud()
                        syncAlertMessage = "iCloud data pulled to local successfully!"
                        showingSyncAlert = true
                    } catch {
                        syncAlertMessage = "Pull failed: \(error.localizedDescription)"
                        showingSyncAlert = true
                    }
                }
            }
        } message: {
            Text("This will overwrite all local data with iCloud expenses. Are you sure?")
        }
    }

    private func deleteAllData() {
        dataManager.deleteAllExpenses()
    }

    private func openShortcutsApp() {
        // Open the FinPin shortcuts download page
        let shortcutURL = "https://finpin.app/shortcuts"

        if let url = URL(string: shortcutURL) {
            UIApplication.shared.open(url) { success in
                if !success {
                    // Fallback to showing instructions
                    showShortcutInstructions()
                }
            }
        } else {
            showShortcutInstructions()
        }
    }

    private func showShortcutInstructions() {
        // This could show an alert with instructions
        // For now, we'll just print instructions
        print("""
        To set up FinPin Shortcuts:

        1. Open the Shortcuts app
        2. Tap the + to create a new shortcut
        3. Add "Get Text from Input" action
        4. Add "Open URL" action with URL: finpin://add-expense?text=[Text from previous action]
        5. Name your shortcut "Add Expense"
        6. Add to Siri for voice activation
        """)
    }



    private func exportCSVFromSettings() {
        let expenses = dataManager.expenses
        csvExportContent = CSVManager.exportExpensesToCSV(expenses)

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        csvExportFilename = "FinPin_Export_\(dateFormatter.string(from: Date())).csv"

        showingCSVExporter = true
    }

    private func handleCSVFileImport(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            do {
                // Start accessing the security-scoped resource
                guard url.startAccessingSecurityScopedResource() else {
                    print("❌ Failed to access security-scoped resource")
                    return
                }

                defer {
                    url.stopAccessingSecurityScopedResource()
                }

                // Read the CSV content
                let csvContent = try String(contentsOf: url, encoding: .utf8)

                // Import the expenses
                let importedExpenses = CSVManager.importExpensesFromCSV(csvContent)

                if importedExpenses.isEmpty {
                    csvImportAlertMessage = """
                    ❌ Import Failed

                    No valid expense records found in CSV file.

                    Please check:
                    • CSV format is correct
                    • Header row is included
                    • Date format is yyyy-MM-dd HH:mm:ss
                    • Amount field contains valid numbers
                    """
                    showingCSVImportAlert = true
                    return
                }

                // Add to data manager
                for expense in importedExpenses {
                    dataManager.addExpense(expense)
                }

                // Show success message
                let dateRange = importedExpenses.compactMap { $0.date }
                let minDate = dateRange.min()?.formatted(date: .abbreviated, time: .omitted) ?? "N/A"
                let maxDate = dateRange.max()?.formatted(date: .abbreviated, time: .omitted) ?? "N/A"
                let totalAmount = importedExpenses.reduce(0) { $0 + NSDecimalNumber(decimal: $1.amount).doubleValue }

                csvImportAlertMessage = """
                ✅ Import Successful!

                Imported \(importedExpenses.count) expense records

                Data Overview:
                • Date Range: \(minDate) to \(maxDate)
                • Total Amount: $\(String(format: "%.2f", totalAmount))
                • Merchants: \(Set(importedExpenses.compactMap { $0.merchant }).count)
                • Categories: \(Set(importedExpenses.flatMap { $0.tags }).count)

                All records have been added to your expense history.
                """
                showingCSVImportAlert = true

            } catch {
                csvImportAlertMessage = """
                ❌ File Read Failed

                Error: \(error.localizedDescription)

                Please check:
                • File is in valid CSV format
                • File encoding is UTF-8
                • File is not corrupted
                """
                showingCSVImportAlert = true
            }

        case .failure(let error):
            csvImportAlertMessage = """
            ❌ File Selection Failed

            Error: \(error.localizedDescription)

            Please try selecting the CSV file again.
            """
            showingCSVImportAlert = true
        }
    }

    private func handleCSVFileExport(result: Result<URL, Error>) {
        switch result {
        case .success(let url):
            print("✅ CSV exported successfully to: \(url.path)")
            // Could show a success alert here if needed

        case .failure(let error):
            print("❌ Error exporting CSV file: \(error)")
            // Could show an error alert here if needed
        }
    }

    private func checkServerHealth() {
        isCheckingHealth = true

        Task { @MainActor in
            do {
                // Make a simple health check request
                let url = URL(string: "https://api.finpin.app/api/v1/health")!
                let (_, response) = try await URLSession.shared.data(from: url)

                if let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200 {
                    serverHealthStatus = .healthy
                } else {
                    serverHealthStatus = .unhealthy
                }
            } catch {
                serverHealthStatus = .unhealthy
            }

            isCheckingHealth = false
        }
    }


    
    private func updateExchangeRates() {
        Task {
            // Update status text to show we're fetching
            exchangeRateStatusText = "Fetching latest rates..."
            
            // Fetch rates for the base currency
            await cloudflareExchangeService.fetchRatesForCurrency(baseCurrencyManager.baseCurrency)
            
            // Update last fetch time
            lastExchangeRateUpdate = Date()
            
            // Update status text with success message
            exchangeRateStatusText = "Rates updated successfully"
            
            // Reset status text after 2 seconds
            try? await Task.sleep(nanoseconds: 2_000_000_000)
            if lastExchangeRateUpdate != nil && 
               Date().timeIntervalSince(lastExchangeRateUpdate!) >= 2 {
                exchangeRateStatusText = "Last updated: Just now"
            }
        }
    }
}

// MARK: - Compact Map View
struct CompactMapView: View {
    let latitude: Double
    let longitude: Double
    let locationName: String
    let amount: String

    @State private var region: MKCoordinateRegion

    init(latitude: Double, longitude: Double, locationName: String, amount: String) {
        self.latitude = latitude
        self.longitude = longitude
        self.locationName = locationName
        self.amount = amount

        self._region = State(initialValue: MKCoordinateRegion(
            center: CLLocationCoordinate2D(latitude: latitude, longitude: longitude),
            span: MKCoordinateSpan(latitudeDelta: 0.01, longitudeDelta: 0.01)
        ))
    }

    var body: some View {
        Map(coordinateRegion: .constant(region), annotationItems: [CompactMapLocation(coordinate: CLLocationCoordinate2D(latitude: latitude, longitude: longitude))]) { location in
            MapAnnotation(coordinate: location.coordinate) {
                VStack(spacing: 2) {
                    Image(systemName: "mappin.circle.fill")
                        .foregroundColor(.red)
                        .font(.system(size: 20))

                    Text(amount)
                        .font(.caption2)
                        .fontWeight(.bold)
                        .foregroundColor(.primary)
                        .padding(.horizontal, 4)
                        .padding(.vertical, 2)
                        .background(
                            RoundedRectangle(cornerRadius: 4)
                                .fill(Color.white)
                                .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
                        )
                }
            }
        }
        .disabled(true) // Disable interaction
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
        )
    }
}

// MARK: - Debug View
struct DebugView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var debugText = ""
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var isLoading = false
    @State private var serverResponse = ""
    @State private var showingServerResponse = false
    @State private var registrationStatus = ""
    @State private var showingRegistrationStatus = false
    @State private var isRegistering = false
    @State private var showingCSVImport = false
    @State private var csvImportText = ""
    @State private var showingCSVFilePickerInDebug = false
    @State private var showingCSVExporterInDebug = false
    @State private var csvExportContentInDebug = ""
    @State private var csvExportFilenameInDebug = ""

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                Text("Debug Tools")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)

                Text("Paste payment text, the system will try to parse and create expense records")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                // Device Registration Section
                VStack(alignment: .leading, spacing: 12) {
                    HStack {
                        Text("Device Registration")
                            .font(.headline)
                            .foregroundColor(.primary)

                        Spacer()

                        Button(action: registerDevice) {
                            HStack {
                                if isRegistering {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .foregroundColor(.white)
                                } else {
                                    Image(systemName: "key.fill")
                                        .foregroundColor(.white)
                                }
                                Text(isRegistering ? "Registering..." : "Register Device")
                                    .font(.subheadline)
                                    .foregroundColor(.white)
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(Color.orange)
                            .cornerRadius(8)
                        }
                        .disabled(isRegistering || isLoading)
                    }

                    // Registration Status
                    if !registrationStatus.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Button(action: {
                                withAnimation(.easeInOut(duration: 0.3)) {
                                    showingRegistrationStatus.toggle()
                                }
                            }) {
                                HStack {
                                    Text("Registration Status")
                                        .font(.subheadline)
                                        .fontWeight(.medium)
                                        .foregroundColor(.secondary)

                                    Spacer()

                                    Image(systemName: showingRegistrationStatus ? "chevron.up" : "chevron.down")
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .buttonStyle(PlainButtonStyle())

                            if showingRegistrationStatus {
                                ScrollView {
                                    Text(registrationStatus)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                }
                                .frame(maxHeight: 150)
                                .padding(8)
                                .background(Color(.systemGray6))
                                .cornerRadius(6)
                                .transition(.opacity.combined(with: .scale(scale: 0.95)))
                                .overlay(
                                    HStack {
                                        Spacer()
                                        Button(action: {
                                            UIPasteboard.general.string = registrationStatus
                                        }) {
                                            Image(systemName: "doc.on.doc")
                                                .font(.caption)
                                                .foregroundColor(.blue)
                                                .padding(4)
                                                .background(Color.white.opacity(0.8))
                                                .cornerRadius(4)
                                        }
                                        .padding(.trailing, 8)
                                        .padding(.top, 8)
                                    },
                                    alignment: .topTrailing
                                )
                            }
                        }
                    }
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray6).opacity(0.5))
                )

                TextEditor(text: $debugText)
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
                    .frame(minHeight: 200)

                Button(action: parseAndCreateExpense) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .foregroundColor(.white)
                        }
                        Text(isLoading ? "Parsing..." : "Parse with Server API")
                            .font(.headline)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .cornerRadius(12)
                }
                .disabled(debugText.isEmpty || isLoading)

                // Test Server Connection Button
                Button(action: testServerConnection) {
                    HStack {
                        Image(systemName: "network")
                            .foregroundColor(.white)
                        Text("Test Server Connection")
                            .font(.subheadline)
                            .foregroundColor(.white)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 12)
                    .background(Color.green)
                    .cornerRadius(8)
                }
                .disabled(isLoading)

                // CSV Import/Export Section (Hidden)
                #if false
                VStack(spacing: 12) {
                    Text("CSV Import/Export")
                        .font(.headline)
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    Text(subscriptionManager.hasActiveSubscription ?
                         "Import/export expense data in CSV format for data management and backup" :
                         "Premium feature: Import/export expense data in CSV format for data management and backup")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    HStack(spacing: 12) {
                        Button(action: {
                            if subscriptionManager.hasActiveSubscription {
                                exportToCSV()
                            } else {
                                alertMessage = "CSV Export is a Premium feature. Please subscribe to unlock this functionality."
                                showingAlert = true
                            }
                        }) {
                            VStack(spacing: 4) {
                                Image(systemName: "square.and.arrow.up")
                                    .font(.title2)
                                Text("Export")
                                    .font(.caption)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(subscriptionManager.hasActiveSubscription ? Color.blue : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!subscriptionManager.hasActiveSubscription)

                        Button(action: {
                            if subscriptionManager.hasActiveSubscription {
                                showingCSVFilePickerInDebug = true
                            } else {
                                alertMessage = "CSV Import is a Premium feature. Please subscribe to unlock this functionality."
                                showingAlert = true
                            }
                        }) {
                            VStack(spacing: 4) {
                                Image(systemName: "square.and.arrow.down")
                                    .font(.title2)
                                Text("Import")
                                    .font(.caption)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(subscriptionManager.hasActiveSubscription ? Color.green : Color.gray)
                            .cornerRadius(8)
                        }
                        .disabled(!subscriptionManager.hasActiveSubscription)

                        Button(action: generateSampleCSV) {
                            VStack(spacing: 4) {
                                Image(systemName: "doc.text")
                                    .font(.title2)
                                Text("Sample")
                                    .font(.caption)
                            }
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 12)
                            .background(Color.orange)
                            .cornerRadius(8)
                        }
                    }

                    Button(action: clearAllData) {
                        HStack {
                            Image(systemName: "trash.fill")
                                .foregroundColor(.white)
                            Text("Clear All Data")
                                .font(.subheadline)
                                .foregroundColor(.white)
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 12)
                        .background(Color.red.opacity(0.8))
                        .cornerRadius(8)
                    }
                }
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Color(.systemGray6).opacity(0.5))
                )
                #endif

                // Server Response Section
                if !serverResponse.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Button(action: {
                            withAnimation(.easeInOut(duration: 0.3)) {
                                showingServerResponse.toggle()
                            }
                        }) {
                            HStack {
                                Text("Server Response")
                                    .font(.subheadline)
                                    .fontWeight(.medium)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Image(systemName: showingServerResponse ? "chevron.up" : "chevron.down")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())

                        if showingServerResponse {
                            ScrollView {
                                Text(serverResponse)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(maxWidth: .infinity, alignment: .leading)
                            }
                            .frame(maxHeight: 200)
                            .padding(8)
                            .background(Color(.systemGray6))
                            .cornerRadius(6)
                            .transition(.opacity.combined(with: .scale(scale: 0.95)))
                            .overlay(
                                HStack {
                                    Spacer()
                                    Button(action: {
                                        UIPasteboard.general.string = serverResponse
                                    }) {
                                        Image(systemName: "doc.on.doc")
                                            .font(.caption)
                                            .foregroundColor(.blue)
                                            .padding(4)
                                            .background(Color.white.opacity(0.8))
                                            .cornerRadius(4)
                                    }
                                    .padding(.trailing, 8)
                                    .padding(.top, 8)
                                },
                                alignment: .topTrailing
                            )
                        }
                    }
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemGray6).opacity(0.5))
                    )
                }

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
        .alert("Parse Result", isPresented: $showingAlert) {
            Button("OK") { }
        } message: {
            Text(alertMessage)
        }
        .sheet(isPresented: $showingCSVImport) {
            CSVImportView(csvText: $csvImportText, onImport: importFromCSV)
        }
        .fileImporter(
            isPresented: $showingCSVFilePickerInDebug,
            allowedContentTypes: [.commaSeparatedText, .plainText],
            allowsMultipleSelection: false
        ) { result in
            handleCSVFileImportInDebug(result: result)
        }
        .fileExporter(
            isPresented: $showingCSVExporterInDebug,
            document: CSVDocument(content: csvExportContentInDebug),
            contentType: .commaSeparatedText,
            defaultFilename: csvExportFilenameInDebug
        ) { result in
            handleCSVFileExportInDebug(result: result)
        }
    }

    private func parseAndCreateExpense() {
        isLoading = true
        serverResponse = ""

        Task { @MainActor in
            do {
                // Try to use server API for parsing
                let parsedExpense = try await parseWithServerAPI(debugText)

                // Display real server response
                // Get authentication info from response
                let authInfo = parsedExpense.authInfo
                let deviceId = authInfo?.deviceId ?? "N/A"
                let timestamp = authInfo?.timestamp ?? "N/A"
                let signature = authInfo?.signature ?? "N/A"
                let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

                serverResponse = """
                ✅ HMAC Authenticated Server API Response (Success):

                Authentication:
                - Device ID: \(deviceId)
                - Key Seed: \(keySeed != nil ? "✅ Available (\(keySeed!.prefix(20))...)" : "❌ Missing")
                - Signature: ✅ HMAC-SHA256 verified

                Request:
                POST https://api.finpin.app/api/v1/parse/expense
                Headers:
                - Content-Type: application/json
                - x-device-id: \(deviceId)
                - x-timestamp: \(timestamp)
                - x-signature: \(signature.prefix(20))...
                - User-Agent: FinPin-Debug/1.0

                Body:
                {
                  "text": "\(debugText.prefix(100))\(debugText.count > 100 ? "..." : "")",
                  "context": {
                    "timestamp": "\(ISO8601DateFormatter().string(from: Date()))",
                    "image_metadata": {
                      "format": "text",
                      "source": "debug_tools"
                    }
                  }
                }

                Response:
                HTTP/1.1 200 OK
                Content-Type: application/json

                {
                  "success": true,
                  "data": {
                    "amount": "\(parsedExpense.amount)",
                    "currency": "\(parsedExpense.currency)",
                    "merchant": "\(parsedExpense.merchant ?? "null")",
                    "payment_method": "\(parsedExpense.paymentMethod ?? "null")",
                    "payment_card": "\(parsedExpense.paymentCard ?? "null")",
                    "location": "\(parsedExpense.location ?? "null")",
                    "confidence": \(parsedExpense.confidence),
                    "extensions": {
                      "category": "\(parsedExpense.category ?? "null")",
                      "tags": \(parsedExpense.tags?.description ?? "null"),
                      "description": "\(parsedExpense.description ?? "null")"
                    }
                  }
                }

                🎉 Parsing completed with authenticated API!
                """

                // Create expense record from real server response with current exchange rate
                let expense = ExpenseRecord.createWithCurrentExchangeRate(
                    amount: Decimal(string: parsedExpense.amount) ?? 0,
                    currency: parsedExpense.currency,
                    merchant: parsedExpense.merchant,
                    date: Date(),
                    latitude: 0.0,
                    longitude: 0.0,
                    locationName: parsedExpense.location,
                    tags: parsedExpense.tags ?? ["debug", "server-parsed"],
                    paymentMethod: parsedExpense.paymentMethod,
                    paymentCard: parsedExpense.paymentCard,
                    notes: parsedExpense.description ?? "Created via Debug Tools (Real Server API)",
                    rawText: debugText,
                    confidence: .high
                )

                dataManager.addExpense(expense)
                alertMessage = "✅ Successfully created expense record using Real Server API!\n\nAmount: \(expense.currency) \(expense.amount)\nMerchant: \(expense.merchant ?? "Unknown")\nPayment: \(expense.paymentMethod ?? "N/A")\nCard: \(expense.paymentCard ?? "N/A")\nConfidence: \(parsedExpense.confidence)"
                showingAlert = true
                debugText = ""

            } catch {
                // No fallback - show server error and require server API
                serverResponse = """
                ❌ Server API Error:

                Error: \(error.localizedDescription)

                Debug Information:
                - URL: https://api.finpin.app/api/v1/parse/expense
                - Method: POST
                - Content-Type: application/json
                - Text Length: \(debugText.count) characters

                Please check:
                1. Internet connection
                2. Server availability
                3. Request format

                ⚠️ Debug Tools only uses Server API - no local fallback.
                """

                alertMessage = "❌ Server API Error: \(error.localizedDescription)\n\nDebug Tools requires server connection. Please check your internet connection and try again."
                showingAlert = true
            }

            isLoading = false
        }
    }

    private func registerDevice() {
        isRegistering = true
        registrationStatus = ""

        Task { @MainActor in
            do {
                print("🔐 Starting device registration...")

                // Get device information
                let device = UIDevice.current
                print("📱 Device Info: \(device.model), iOS \(device.systemVersion)")

                // Generate a device ID for this session
                let deviceId = UUID().uuidString

                // Create registration request
                let url = URL(string: "https://api.finpin.app/api/v1/device/register")!
                var request = URLRequest(url: url)
                request.httpMethod = "POST"
                request.setValue("application/json", forHTTPHeaderField: "Content-Type")
                request.setValue("FinPin-Debug/1.0", forHTTPHeaderField: "User-Agent")
                request.timeoutInterval = 30.0

                // Create proper request body matching server schema
                let requestBody: [String: Any] = [
                    "device_id": deviceId,
                    "device_info": [
                        "model": UIDevice.current.model,
                        "os_version": UIDevice.current.systemVersion,
                        "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                        "platform": "ios"
                    ]
                ]

                request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

                // Log request details
                let requestBodyString = String(data: request.httpBody!, encoding: .utf8) ?? "Unable to encode"
                print("📤 Registration Request Body: \(requestBodyString)")

                let (data, response) = try await URLSession.shared.data(for: request)

                guard let httpResponse = response as? HTTPURLResponse,
                      httpResponse.statusCode == 200 else {
                    throw NSError(domain: "RegistrationError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Registration failed with status code \((response as? HTTPURLResponse)?.statusCode ?? 0)"])
                }

                let responseString = String(data: data, encoding: .utf8) ?? "Unable to decode response"
                print("📥 Registration Response: \(responseString)")

                let json = try JSONSerialization.jsonObject(with: data) as? [String: Any]
                guard let success = json?["success"] as? Bool, success else {
                    let errorMessage = json?["error"] as? String ?? "Registration failed"
                    let details = json?["details"] as? [[String: Any]]
                    if let details = details {
                        let detailsString = details.map { detail in
                            let path = (detail["path"] as? [String])?.joined(separator: ".") ?? "unknown"
                            let message = detail["message"] as? String ?? "unknown error"
                            return "- \(path): \(message)"
                        }.joined(separator: "\n")
                        throw NSError(domain: "RegistrationError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Registration validation failed:\n\(detailsString)"])
                    }
                    throw NSError(domain: "RegistrationError", code: 2, userInfo: [NSLocalizedDescriptionKey: errorMessage])
                }

                guard let responseData = json?["data"] as? [String: Any] else {
                    throw NSError(domain: "RegistrationError", code: 3, userInfo: [NSLocalizedDescriptionKey: "Missing data in registration response"])
                }

                // Extract auth token - try different possible field names
                let authToken = responseData["device_token"] as? String ??
                               responseData["auth_token"] as? String ??
                               responseData["key_seed"] as? String

                guard let token = authToken else {
                    throw NSError(domain: "RegistrationError", code: 4, userInfo: [NSLocalizedDescriptionKey: "Missing auth token in registration response. Available fields: \(responseData.keys.joined(separator: ", "))"])
                }

                // Get additional info from response
                let expiresAt = responseData["expires_at"] as? String ?? "Not specified"
                let keySeed = responseData["key_seed"] as? String

                // Store credentials for this session
                UserDefaults.standard.set(deviceId, forKey: "debug_device_id")
                UserDefaults.standard.set(token, forKey: "debug_auth_token")

                // Also store key_seed if available for HMAC signing
                if let keySeed = keySeed {
                    UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")
                }

                registrationStatus = """
                ✅ Device Registration SUCCESSFUL!

                Device Information:
                - Device ID: \(deviceId)
                - Auth Token: ✅ Available (\(token.prefix(20))...)
                - Key Seed: \(keySeed != nil ? "✅ Available (\(keySeed!.prefix(20))...)" : "❌ Not provided")
                - Expires At: \(expiresAt)
                - Registration Time: \(ISO8601DateFormatter().string(from: Date()))

                Security Headers Ready:
                - X-Device-ID: \(deviceId)
                - Authorization: Bearer [TOKEN]
                - Content-Type: application/json
                - User-Agent: FinPin-Debug/1.0

                Server Response Fields:
                \(responseData.keys.sorted().map { "- \($0): \(responseData[$0] ?? "nil")" }.joined(separator: "\n"))

                🎉 Device is now authenticated for API calls!
                You can now use "Parse with Server API" with proper authentication.

                Note: This registration is valid for this debug session.
                """

                showingRegistrationStatus = true
                alertMessage = "✅ Device Registration Successful!\n\nYour device is now authenticated. You can proceed with server API calls."
                showingAlert = true

            } catch {
                registrationStatus = """
                ❌ Device Registration FAILED!

                Error Details:
                \(error.localizedDescription)

                Troubleshooting Steps:
                1. Check your internet connection
                2. Verify the registration server is accessible
                3. Ensure the app has proper permissions
                4. Try again in a few moments

                Registration URL: https://api.finpin.app/api/v1/device/register

                Without device registration, API calls will fail with 401 errors.
                """

                showingRegistrationStatus = true
                alertMessage = "❌ Device Registration Failed!\n\n\(error.localizedDescription)\n\nPlease register your device before using server API."
                showingAlert = true
            }

            isRegistering = false
        }
    }



    private func testServerConnection() {
        isLoading = true
        serverResponse = ""

        Task { @MainActor in
            do {
                print("🧪 Testing server connection...")

                // Test with a simple known text
                let testText = "Costa Coffee £9.65 HSBC Card"
                let result = try await parseWithServerAPI(testText)

                serverResponse = """
                ✅ HMAC Authentication Test SUCCESSFUL!

                Test Input: "\(testText)"

                Authentication Details:
                - Device ID: \(result.authInfo?.deviceId ?? "N/A")
                - Timestamp: \(result.authInfo?.timestamp ?? "N/A")
                - Signature: \(result.authInfo?.signature.prefix(20) ?? "N/A")...

                Server Response:
                - Amount: \(result.amount) \(result.currency)
                - Merchant: \(result.merchant ?? "N/A")
                - Payment Method: \(result.paymentMethod ?? "N/A")
                - Payment Card: \(result.paymentCard ?? "N/A")
                - Location: \(result.location ?? "N/A")
                - Confidence: \(result.confidence)
                - Category: \(result.category ?? "N/A")
                - Tags: \(result.tags?.joined(separator: ", ") ?? "N/A")

                🎉 HMAC Authentication & Server API working correctly!
                You can now use "Parse with Server API" with confidence.
                """

                alertMessage = "✅ Server Connection Test Successful!\n\nThe server API is working correctly. You can now parse your expense text."
                showingAlert = true

            } catch {
                serverResponse = """
                ❌ Server Connection Test FAILED!

                Error Details:
                \(error.localizedDescription)

                Troubleshooting Steps:
                1. Check your internet connection
                2. Verify the server is running
                3. Check if the API endpoint is accessible
                4. Try again in a few moments

                Server URL: https://api.finpin.app/api/v1/parse/expense
                """

                alertMessage = "❌ Server Connection Failed!\n\n\(error.localizedDescription)\n\nPlease check your internet connection and try again."
                showingAlert = true
            }

            isLoading = false
        }
    }

    private func generateHMACSignature(deviceId: String, timestamp: String, requestBody: String, keySeed: String) -> String {
        print("🔒 DebugView HMAC Signature Generation:")

        // Hash the request body using SHA-256 (matching server implementation)
        let requestBodyData = Data(requestBody.utf8)
        let requestBodyHash = SHA256.hash(data: requestBodyData)
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()

        print("   Request Body Hash: \(requestBodyHashString.prefix(40))...")

        // Create message: timestamp + deviceId + requestBodyHash (matching server format)
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)

        print("   Message to Sign: \(message.prefix(100))\(message.count > 100 ? "..." : "")")
        print("   Message Length: \(message.count) characters")

        // Create HMAC key from keySeed (server uses Base64 string directly as UTF-8)
        let keyData = Data(keySeed.utf8)
        let key = SymmetricKey(data: keyData)

        print("   Key Seed (Base64): \(keySeed)")
        print("   Key Data Length: \(keyData.count) bytes")

        // Generate HMAC-SHA256 signature
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: key)
        let base64Signature = Data(signature).base64EncodedString()

        print("   Final Signature: \(base64Signature.prefix(40))...")
        print("   Signature Length: \(base64Signature.count) characters")
        print("🔒 DebugView HMAC signature generation completed")

        return base64Signature
    }

    private func parseWithServerAPI(_ text: String) async throws -> (amount: String, currency: String, merchant: String?, paymentMethod: String?, paymentCard: String?, location: String?, confidence: Double, category: String?, tags: [String]?, description: String?, authInfo: (deviceId: String, timestamp: String, signature: String)?) {
        print("🚀 Debug Tools: Starting authenticated server API call...")
        print("📝 Text to parse: \(text.prefix(100))\(text.count > 100 ? "..." : "")")

        // Get stored credentials
        guard let deviceId = UserDefaults.standard.string(forKey: "debug_device_id"),
              let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed") else {

            // Debug: Show what credentials we have
            let storedDeviceId = UserDefaults.standard.string(forKey: "debug_device_id")
            let storedKeySeed = UserDefaults.standard.string(forKey: "debug_key_seed")
            let storedAuthToken = UserDefaults.standard.string(forKey: "debug_auth_token")

            print("❌ Missing credentials for debug tools:")
            print("   Device ID: \(storedDeviceId ?? "nil")")
            print("   Key Seed: \(storedKeySeed != nil ? "Available (\(storedKeySeed!.prefix(20))...)" : "nil")")
            print("   Auth Token: \(storedAuthToken != nil ? "Available (\(storedAuthToken!.prefix(20))...)" : "nil")")

            throw NSError(domain: "AuthError", code: 401, userInfo: [NSLocalizedDescriptionKey: "Device not registered or missing key seed. Please register device first using the 'Register Device' button.\n\nDebug info:\n- Device ID: \(storedDeviceId ?? "missing")\n- Key Seed: \(storedKeySeed != nil ? "available" : "missing")\n- Auth Token: \(storedAuthToken != nil ? "available" : "missing")"])
        }

        print("🔐 Debug Tools Credentials:")
        print("   Device ID: \(deviceId)")
        print("   Key Seed: Available (\(keySeed.prefix(20))...)")
        print("   Key Seed Length: \(keySeed.count) characters")

        // Create request body using Codable struct (matching APIClient.swift approach)
        struct DebugParseRequest: Codable {
            let text: String
            let context: ParseContext

            struct ParseContext: Codable {
                let timestamp: String
                let image_metadata: ImageMetadata

                struct ImageMetadata: Codable {
                    let format: String
                    let source: String
                }
            }
        }

        let requestBody = DebugParseRequest(
            text: text,
            context: DebugParseRequest.ParseContext(
                timestamp: ISO8601DateFormatter().string(from: Date()),
                image_metadata: DebugParseRequest.ParseContext.ImageMetadata(
                    format: "text",
                    source: "debug_tools"
                )
            )
        )

        // Use JSONEncoder (matching APIClient.swift approach)
        let requestBodyData = try JSONEncoder().encode(requestBody)
        let requestBodyString = String(data: requestBodyData, encoding: .utf8) ?? ""

        // Generate timestamp and signature
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))

        print("🔐 Debug Tools - Signature Generation:")
        print("   Device ID: \(deviceId)")
        print("   Timestamp: \(timestamp)")
        print("   Key Seed: \(keySeed.prefix(20))...")
        print("   Request Body Length: \(requestBodyString.count) characters")
        print("   Request Body JSON: \(requestBodyString)")

        // Debug: Show the exact bytes of the request body for comparison
        let requestBodyBytes = requestBodyData.map { String(format: "%02x", $0) }.joined()
        print("   Request Body Bytes: \(requestBodyBytes.prefix(100))\(requestBodyBytes.count > 100 ? "..." : "")")

        let signature = generateHMACSignature(
            deviceId: deviceId,
            timestamp: timestamp,
            requestBody: requestBodyString,
            keySeed: keySeed
        )

        print("   Generated Signature: \(signature.prefix(40))...")
        print("🔐 Signature generation completed")

        // Test: Generate signature using APIClient method for comparison
        print("🧪 Testing APIClient signature generation method:")
        let testRequestBodyHash = SHA256.hash(data: Data(requestBodyString.utf8))
        let testRequestBodyHashString = testRequestBodyHash.compactMap { String(format: "%02x", $0) }.joined()
        let testMessage = "\(timestamp)\(deviceId)\(testRequestBodyHashString)"
        let testMessageData = Data(testMessage.utf8)
        let testKeyData = Data(keySeed.utf8)
        let testSignature = HMAC<SHA256>.authenticationCode(for: testMessageData, using: SymmetricKey(data: testKeyData))
        let testBase64Signature = Data(testSignature).base64EncodedString()
        print("   APIClient Method Signature: \(testBase64Signature.prefix(40))...")
        print("   Matches Our Method: \(testBase64Signature == signature)")

        // Create authenticated request with HMAC headers
        let url = URL(string: "https://api.finpin.app/api/v1/parse/expense")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-Debug/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue(deviceId, forHTTPHeaderField: "x-device-id")
        request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
        request.setValue(signature, forHTTPHeaderField: "x-signature")
        request.timeoutInterval = 30.0
        request.httpBody = requestBodyData

        print("📤 Making authenticated API call...")
        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "ServerError", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid HTTP response"])
        }

        print("📊 HTTP Status Code: \(httpResponse.statusCode)")

        let responseString = String(data: data, encoding: .utf8) ?? "Unable to decode response"
        print("📥 Raw Response: \(responseString)")

        guard httpResponse.statusCode == 200 else {
            let errorMessage = "Server returned status code \(httpResponse.statusCode). Response: \(responseString)"
            throw NSError(domain: "ServerError", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
        }

        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any] else {
            throw NSError(domain: "ParseError", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse JSON response: \(responseString)"])
        }

        guard let success = json["success"] as? Bool, success else {
            let errorMessage = json["error"] as? String ?? "Unknown server error"
            throw NSError(domain: "ServerError", code: 3, userInfo: [NSLocalizedDescriptionKey: "Server error: \(errorMessage)"])
        }

        guard let responseData = json["data"] as? [String: Any] else {
            throw NSError(domain: "ParseError", code: 4, userInfo: [NSLocalizedDescriptionKey: "Missing or invalid data field in server response"])
        }

        // Extract response data
        let amount = responseData["amount"] as? String ?? "0"
        let currency = responseData["currency"] as? String ?? "USD"
        let merchant = responseData["merchant"] as? String
        let paymentMethod = responseData["payment_method"] as? String
        let paymentCard = responseData["payment_card"] as? String
        let location = responseData["location"] as? String
        let confidence = responseData["confidence"] as? Double ?? 0.5

        let extensions = responseData["extensions"] as? [String: Any]
        let category = extensions?["category"] as? String
        let tags = extensions?["tags"] as? [String]
        let description = extensions?["description"] as? String

        print("✅ Authenticated API call successful!")
        print("💰 Parsed Amount: \(amount) \(currency)")
        print("🏪 Parsed Merchant: \(merchant ?? "nil")")
        print("💳 Parsed Payment: \(paymentMethod ?? "nil") - \(paymentCard ?? "nil")")
        print("📍 Parsed Location: \(location ?? "nil")")
        print("🎯 Confidence: \(confidence)")
        print("🏷️ Tags: \(tags?.description ?? "nil")")

        return (
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            confidence: confidence,
            category: category,
            tags: tags,
            description: description,
            authInfo: (deviceId: deviceId, timestamp: timestamp, signature: signature)
        )


    }

    private func exportToCSV() {
        let expenses = dataManager.expenses
        csvExportContentInDebug = CSVManager.exportExpensesToCSV(expenses)

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd_HH-mm-ss"
        csvExportFilenameInDebug = "FinPin_Export_\(dateFormatter.string(from: Date())).csv"

        showingCSVExporterInDebug = true
    }

    private func handleCSVFileExportInDebug(result: Result<URL, Error>) {
        Task { @MainActor in
            switch result {
            case .success(let url):
                let expenses = dataManager.expenses

                serverResponse = """
                ✅ CSV Export Complete:

                Exported: \(expenses.count) expense records
                File: \(csvExportFilenameInDebug)
                Location: \(url.path)
                File Size: \(csvExportContentInDebug.count) characters

                ✅ File successfully saved to selected location!
                """

                showingServerResponse = true
                alertMessage = "Successfully exported \(expenses.count) expense records to CSV!\n\nFile: \(csvExportFilenameInDebug)\nLocation: \(url.path)"
                showingAlert = true

            case .failure(let error):
                serverResponse = """
                ❌ CSV Export Failed:

                Error: \(error.localizedDescription)

                Please try again or check file permissions.
                """

                showingServerResponse = true
                alertMessage = "❌ Export failed: \(error.localizedDescription)"
                showingAlert = true
            }
        }
    }

    private func generateSampleCSV() {
        Task { @MainActor in
            let sampleCSV = CSVManager.generateSampleCSV()
            let filename = "FinPin_Sample_Template.csv"

            if let fileURL = CSVManager.saveCSVToDocuments(sampleCSV, filename: filename) {
                serverResponse = """
                ✅ Sample CSV Template Generated:

                File: \(filename)
                Location: Documents folder
                Records: 2 sample entries

                File Path: \(fileURL.path)

                This template shows the correct CSV format:
                - ID: Unique identifier (UUID)
                - Amount: Decimal number
                - Currency: Currency code (USD, CNY, etc.)
                - Merchant: Store/business name
                - Date: yyyy-MM-dd HH:mm:ss format
                - Latitude/Longitude: GPS coordinates
                - LocationName: Human-readable location
                - Tags: Semicolon-separated categories
                - PaymentMethod: Payment type
                - PaymentCard: Card info (optional)
                - Notes: Additional notes (optional)
                - RawText: Original text (optional)

                ✅ Edit this file and import it back!
                """

                showingServerResponse = true
                alertMessage = "Sample CSV template created!\n\nFile: \(filename)\nLocation: Documents folder\n\nEdit this file and import it back to add your data."
            } else {
                alertMessage = "❌ Failed to create sample CSV template."
            }

            showingAlert = true
        }
    }

    private func importFromCSV(_ csvContent: String) {
        Task { @MainActor in
            let startTime = Date()
            print("📥 Importing expenses from CSV...")

            let importedExpenses = CSVManager.importExpensesFromCSV(csvContent)

            if importedExpenses.isEmpty {
                serverResponse = """
                ❌ CSV Import Failed:

                No valid expense records found in CSV.

                Please check:
                - CSV format matches the template
                - Header row is present
                - Date format is yyyy-MM-dd HH:mm:ss
                - Amount values are valid numbers
                - Required fields are not empty
                """

                showingServerResponse = true
                alertMessage = "❌ Import failed! No valid records found in CSV."
                showingAlert = true
                return
            }

            // Add to data manager
            for expense in importedExpenses {
                dataManager.addExpense(expense)
            }

            let endTime = Date()
            let duration = endTime.timeIntervalSince(startTime)

            serverResponse = """
            ✅ CSV Import Complete:

            Imported: \(importedExpenses.count) expense records
            Import Time: \(String(format: "%.2f", duration)) seconds

            Data Summary:
            - Merchants: \(Set(importedExpenses.compactMap { $0.merchant }).count) unique
            - Categories: \(Set(importedExpenses.flatMap { $0.tags }).count) unique
            - Currencies: \(Set(importedExpenses.map { $0.currency }).count) unique
            - Date Range: \(importedExpenses.map { $0.date }.min()?.formatted(date: .abbreviated, time: .omitted) ?? "N/A") to \(importedExpenses.map { $0.date }.max()?.formatted(date: .abbreviated, time: .omitted) ?? "N/A")

            Total Amount: $\(String(format: "%.2f", importedExpenses.reduce(0) { $0 + NSDecimalNumber(decimal: $1.amount).doubleValue }))

            ✅ All records successfully imported!
            """

            showingServerResponse = true
            alertMessage = "Successfully imported \(importedExpenses.count) expense records from CSV!"
            showingAlert = true
        }
    }

    private func handleCSVFileImportInDebug(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            Task { @MainActor in
                do {
                    // Start accessing the security-scoped resource
                    guard url.startAccessingSecurityScopedResource() else {
                        serverResponse = """
                        ❌ CSV Import Failed:

                        Failed to access security-scoped resource.
                        Please check file permissions.
                        """
                        showingServerResponse = true
                        alertMessage = "❌ Failed to access CSV file"
                        showingAlert = true
                        return
                    }

                    defer {
                        url.stopAccessingSecurityScopedResource()
                    }

                    // Read the CSV content
                    let csvContent = try String(contentsOf: url, encoding: .utf8)

                    // Import the expenses using existing method
                    importFromCSV(csvContent)

                } catch {
                    serverResponse = """
                    ❌ CSV Import Failed:

                    Error reading CSV file: \(error.localizedDescription)

                    Please check:
                    - File format is valid CSV
                    - File encoding is UTF-8
                    - File is not corrupted
                    """

                    showingServerResponse = true
                    alertMessage = "❌ Error reading CSV file: \(error.localizedDescription)"
                    showingAlert = true
                }
            }

        case .failure(let error):
            Task { @MainActor in
                serverResponse = """
                ❌ CSV File Selection Failed:

                Error: \(error.localizedDescription)

                Please try selecting the file again.
                """

                showingServerResponse = true
                alertMessage = "❌ Error selecting CSV file: \(error.localizedDescription)"
                showingAlert = true
            }
        }
    }

    private func clearAllData() {
        Task { @MainActor in
            let count = dataManager.expenses.count

            serverResponse = """
            🗑️ Clear All Data:

            Found: \(count) expense records

            ⚠️ This feature requires manual implementation.
            For now, you can delete records individually from the main list.

            Alternative: Export data first, then manually clear from the app.
            """

            showingServerResponse = true

            alertMessage = "Clear all data feature needs implementation. For now, delete records individually from the main list."
            showingAlert = true
        }
    }
}

// MARK: - Tag Manager View
struct TagManagerView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @State private var searchText = ""
    @State private var selectedTag: IdentifiableTag? = nil
    @State private var displayedTagsCount = 100 // 初始显示100个标签
    private let tagsPerPage = 50 // 每次加载50个标签

    // 获取所有已使用的标签及其使用次数（缓存优化）
    private var allUsedTags: [(tag: String, count: Int)] {
        let allTags = dataManager.expenses.flatMap { $0.tags }
        let tagCounts = Dictionary(grouping: allTags, by: { $0 })
            .mapValues { $0.count }

        return tagCounts.map { (tag: $0.key, count: $0.value) }
            .sorted { $0.count > $1.count } // 按使用次数排序
    }

    // 所有筛选后的标签
    private var allFilteredTags: [(tag: String, count: Int)] {
        if searchText.isEmpty {
            return allUsedTags
        } else {
            return allUsedTags.filter { $0.tag.localizedCaseInsensitiveContains(searchText) }
        }
    }

    // 分页显示的标签（性能优化）
    private var filteredTags: [(tag: String, count: Int)] {
        let allTags = allFilteredTags
        return Array(allTags.prefix(displayedTagsCount))
    }

    // 是否还有更多标签可以加载
    private var hasMoreTags: Bool {
        allFilteredTags.count > displayedTagsCount
    }

    var body: some View {
        NavigationStack {
            VStack(spacing: 16) {
                // 搜索栏
                VStack(alignment: .leading, spacing: 8) {
                    Text("Search Tags")
                        .font(.headline)
                        .foregroundColor(.primary)

                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)

                        TextField("Search tags...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 8)
                    .background(
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemGray6))
                    )
                }
                .padding(.horizontal)

                // 标签统计信息
                HStack {
                    HStack {
                        Text("\(allFilteredTags.count) tags found")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        if hasMoreTags {
                            Text("(showing \(displayedTagsCount))")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .opacity(0.7)
                        }
                    }

                    Spacer()

                    if !searchText.isEmpty {
                        Button("Clear") {
                            searchText = ""
                            resetTagPagination()
                        }
                        .font(.caption)
                        .foregroundColor(.blue)
                    }
                }
                .padding(.horizontal)

                // 标签网格
                if filteredTags.isEmpty {
                    VStack(spacing: 16) {
                        Image(systemName: "tag")
                            .font(.system(size: 48))
                            .foregroundColor(.secondary)

                        VStack(spacing: 8) {
                            Text(searchText.isEmpty ? "No Tags Found" : "No Matching Tags")
                                .font(.headline)
                                .fontWeight(.semibold)

                            Text(searchText.isEmpty ?
                                 "Tags will appear here as you add them to expenses" :
                                 "Try a different search term")
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .padding(32)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                } else {
                    ScrollView {
                        LazyVGrid(columns: [
                            GridItem(.flexible()),
                            GridItem(.flexible())
                        ], spacing: 12) {
                            ForEach(filteredTags, id: \.tag) { tagInfo in
                                TagCard(
                                    tag: tagInfo.tag,
                                    count: tagInfo.count,
                                    onTap: {
                                        selectedTag = IdentifiableTag(value: tagInfo.tag)
                                    }
                                )
                            }

                            // 加载更多按钮（跨两列）
                            if hasMoreTags {
                                LoadMoreTagsButton {
                                    loadMoreTags()
                                }
                                .gridCellColumns(2) // 跨两列显示
                            }
                        }
                        .padding(.horizontal)
                    }
                }

                Spacer()
            }
            .navigationTitle("Tag Browser")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(item: $selectedTag) { identifiable in
            let tag = identifiable.value
            NavigationStack {
                ExpenseLogViewWithTagFilter(selectedTag: tag)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarLeading) {
                            Button("Close") {
                                selectedTag = nil
                            }
                        }
                    }
            }
        }
        .onChange(of: searchText) { _ in
            resetTagPagination()
        }
    }

    private func loadMoreTags() {
        displayedTagsCount = min(displayedTagsCount + tagsPerPage, allFilteredTags.count)
    }

    private func resetTagPagination() {
        displayedTagsCount = 100 // 重置为初始值
    }
}

// MARK: - Load More Tags Button Component
struct LoadMoreTagsButton: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Spacer()
                HStack(spacing: 8) {
                    Image(systemName: "tag")
                        .font(.subheadline)
                    Text("Load More Tags")
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
                .foregroundColor(.blue)
                Spacer()
            }
            .padding(.vertical, 16)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color.blue.opacity(0.1))
                    .overlay(
                        RoundedRectangle(cornerRadius: 10)
                            .stroke(Color.blue.opacity(0.3), lineWidth: 1)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Tag Card Component
struct TagCard: View {
    let tag: String
    let count: Int
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("#\(tag)")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.blue)
                        .lineLimit(1)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                HStack {
                    Text("\(count) expense\(count == 1 ? "" : "s")")
                        .font(.caption)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }
            .padding(12)
            .background(
                RoundedRectangle(cornerRadius: 10)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.05), radius: 3, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Expense Log View with Tag Filter
struct ExpenseLogViewWithTagFilter: View {
    let selectedTag: String
    @EnvironmentObject private var dataManager: DataManager

    private var filteredExpenses: [ExpenseRecord] {
        dataManager.expenses.filter { expense in
            expense.tags.contains(selectedTag)
        }.sorted { $0.effectiveDate > $1.effectiveDate }
    }

    var body: some View {
        VStack(spacing: 0) {
            // Header with tag info
            VStack(spacing: 8) {
                HStack {
                    Text("Tag: #\(selectedTag)")
                        .font(.headline)
                        .fontWeight(.semibold)
                        .foregroundColor(.blue)

                    Spacer()
                }

                HStack {
                    Text("\(filteredExpenses.count) expense\(filteredExpenses.count == 1 ? "" : "s")")
                        .font(.subheadline)
                        .foregroundColor(.secondary)

                    Spacer()
                }
            }
            .padding(.horizontal, 20)
            .padding(.vertical, 16)
            .background(Color(.systemGray6))

            // Expense list
            if filteredExpenses.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "tag.slash")
                        .font(.system(size: 48))
                        .foregroundColor(.secondary)

                    VStack(spacing: 8) {
                        Text("No Expenses Found")
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text("No expenses found with tag #\(selectedTag)")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(32)
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List {
                    ForEach(filteredExpenses) { expense in
                        NavigationLink(destination: ExpenseDetailView(expenseId: expense.id)) {
                            LogExpenseRow(expense: expense)
                        }
                        .listRowInsets(EdgeInsets(top: 8, leading: 20, bottom: 8, trailing: 20))
                        .listRowSeparator(.hidden)
                        .listRowBackground(Color.clear)
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
        .navigationTitle("Expenses")
        .navigationBarTitleDisplayMode(.inline)
    }
}

// MARK: - Original Text Section
struct OriginalTextSection: View {
    let rawText: String
    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Button(action: {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isExpanded.toggle()
                }
            }) {
                HStack {
                    Text("Original Text")
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.secondary)

                    Spacer()

                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .buttonStyle(PlainButtonStyle())

            if isExpanded {
                Text(rawText)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(8)
                    .background(Color(.systemGray6))
                    .cornerRadius(6)
                    .transition(.opacity.combined(with: .scale(scale: 0.95)))
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color(.systemGray6).opacity(0.5))
        )
    }
}

// MARK: - CSV Import View
struct CSVImportView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var csvText: String
    let onImport: (String) -> Void

    var body: some View {
        NavigationStack {
            VStack(spacing: 20) {
                Text("Import CSV Data")
                    .font(.title2)
                    .fontWeight(.bold)
                    .padding(.top)

                Text("Paste your CSV content below or type/edit the data directly")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                VStack(alignment: .leading, spacing: 8) {
                    Text("CSV Content:")
                        .font(.headline)
                        .foregroundColor(.primary)

                    TextEditor(text: $csvText)
                        .font(.system(.caption, design: .monospaced))
                        .padding(8)
                        .background(Color(.systemGray6))
                        .cornerRadius(8)
                        .frame(minHeight: 300)
                }

                HStack(spacing: 12) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color(.systemGray5))
                    .foregroundColor(.primary)
                    .cornerRadius(12)

                    Button("Import") {
                        onImport(csvText)
                        dismiss()
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                    .disabled(csvText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }

                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - CSV Manager
class CSVManager {

    // MARK: - CSV Export

    static func exportExpensesToCSV(_ expenses: [ExpenseRecord]) -> String {
        // CSV Header with timezone information
        let timezoneInfo = TimeZone.current.identifier
        var csvContent = "# FinPin Expense Export - Times in Local Timezone (\(timezoneInfo))\n"
        csvContent += "ID,Amount,Currency,Merchant,TransactionDate,RecordDate,Latitude,Longitude,LocationName,Tags,PaymentMethod,PaymentCard,Notes,RawText\n"

        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone.current // Use local timezone for user-friendly CSV

        for expense in expenses {
            let row = [
                expense.id.uuidString,
                "\(NSDecimalNumber(decimal: expense.amount).doubleValue)",
                expense.currency,
                escapeCSVField(expense.merchant ?? ""),
                dateFormatter.string(from: expense.transactionDate ?? expense.date),
                dateFormatter.string(from: expense.date),
                "\(expense.latitude)",
                "\(expense.longitude)",
                escapeCSVField(expense.locationName ?? ""),
                escapeCSVField(expense.tags.joined(separator: ";")),
                escapeCSVField(expense.paymentMethod ?? ""),
                escapeCSVField(expense.paymentCard ?? ""),
                escapeCSVField(expense.notes ?? ""),
                escapeCSVField(expense.rawText ?? "")
            ].joined(separator: ",")

            csvContent += row + "\n"
        }

        return csvContent
    }

    // MARK: - CSV Import

    static func importExpensesFromCSV(_ csvContent: String) -> [ExpenseRecord] {
        let lines = csvContent.components(separatedBy: .newlines)
        guard lines.count > 1 else { return [] }

        var expenses: [ExpenseRecord] = []
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0) // Use UTC to match the exported date format

        // Skip header row
        for i in 1..<lines.count {
            let line = lines[i].trimmingCharacters(in: .whitespacesAndNewlines)
            if line.isEmpty { continue }

            let fields = parseCSVLine(line)
            guard fields.count >= 14 else { continue } // Now expecting 14 fields

            // Parse fields
            let idString = fields[0]
            let amountString = fields[1]
            let currency = fields[2]
            let merchant = fields[3].isEmpty ? nil : fields[3]
            let transactionDateString = fields[4]  // This is the transaction date
            let recordDateString = fields[5]       // This is when the record was created
            let latitudeString = fields[6]
            let longitudeString = fields[7]
            let locationName = fields[8].isEmpty ? nil : fields[8]
            let tagsString = fields[9]
            let paymentMethod = fields[10].isEmpty ? nil : fields[10]
            let paymentCard = fields[11].isEmpty ? nil : fields[11]
            let notes = fields[12].isEmpty ? nil : fields[12]
            let rawText = fields[13].isEmpty ? nil : fields[13]

            // Convert to appropriate types
            guard let amount = Decimal(string: amountString),
                  let transactionDate = dateFormatter.date(from: transactionDateString),
                  let recordDate = dateFormatter.date(from: recordDateString),
                  let latitude = Double(latitudeString),
                  let longitude = Double(longitudeString) else {
                continue
            }

            let id = UUID(uuidString: idString) ?? UUID()
            let tags = tagsString.isEmpty ? [] : tagsString.components(separatedBy: ";")

            let expense = ExpenseRecord(
                id: id,
                amount: amount,
                currency: currency,
                merchant: merchant,
                date: recordDate,           // Use record date for when the expense was added to the app
                transactionDate: transactionDate, // Use transaction date for when the expense actually occurred
                latitude: latitude,
                longitude: longitude,
                locationName: locationName,
                tags: tags,
                paymentMethod: paymentMethod,
                paymentCard: paymentCard,
                notes: notes,
                rawText: rawText,
                confidence: .manual,
                exchangeRate: nil, // CSV导入的历史数据不设置汇率
                baseCurrency: nil,
                convertedAmount: nil
            )

            expenses.append(expense)
        }

        return expenses
    }

    // MARK: - Helper Methods

    private static func escapeCSVField(_ field: String) -> String {
        if field.contains(",") || field.contains("\"") || field.contains("\n") {
            return "\"" + field.replacingOccurrences(of: "\"", with: "\"\"") + "\""
        }
        return field
    }

    private static func parseCSVLine(_ line: String) -> [String] {
        var fields: [String] = []
        var currentField = ""
        var insideQuotes = false
        var i = line.startIndex

        while i < line.endIndex {
            let char = line[i]

            if char == "\"" {
                if insideQuotes {
                    // Check if this is an escaped quote
                    let nextIndex = line.index(after: i)
                    if nextIndex < line.endIndex && line[nextIndex] == "\"" {
                        currentField += "\""
                        i = line.index(after: nextIndex)
                        continue
                    } else {
                        insideQuotes = false
                    }
                } else {
                    insideQuotes = true
                }
            } else if char == "," && !insideQuotes {
                fields.append(currentField)
                currentField = ""
            } else {
                currentField += String(char)
            }

            i = line.index(after: i)
        }

        fields.append(currentField)
        return fields
    }

    // MARK: - File Operations

    static func saveCSVToDocuments(_ csvContent: String, filename: String) -> URL? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }

        let fileURL = documentsDirectory.appendingPathComponent(filename)

        do {
            try csvContent.write(to: fileURL, atomically: true, encoding: .utf8)
            return fileURL
        } catch {
            print("Error saving CSV file: \(error)")
            return nil
        }
    }

    static func loadCSVFromDocuments(_ filename: String) -> String? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }

        let fileURL = documentsDirectory.appendingPathComponent(filename)

        do {
            return try String(contentsOf: fileURL, encoding: .utf8)
        } catch {
            print("Error loading CSV file: \(error)")
            return nil
        }
    }

    // MARK: - Sample CSV Template

    static func generateSampleCSV() -> String {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss"

        let sampleData = [
            [
                UUID().uuidString,
                "25.50",
                "USD",
                "Starbucks",
                dateFormatter.string(from: Date()), // Transaction Date
                dateFormatter.string(from: Date()), // Record Date
                "40.7128",
                "-74.0060",
                "New York, NY",
                "餐饮;咖啡",
                "信用卡",
                "Visa ****1234",
                "Morning coffee",
                "Starbucks\n$25.50\n2025-07-29 11:30\nVisa ****1234"
            ],
            [
                UUID().uuidString,
                "120.00",
                "USD",
                "Amazon",
                dateFormatter.string(from: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()), // Transaction Date
                dateFormatter.string(from: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date()), // Record Date
                "47.6062",
                "-122.3321",
                "Seattle, WA",
                "购物;电子产品",
                "借记卡",
                "Mastercard ****5678",
                "Online purchase",
                "Amazon.com\n$120.00\nOrder #123456789"
            ]
        ]

        var csvContent = "ID,Amount,Currency,Merchant,Date,RecordDate,Latitude,Longitude,LocationName,Tags,PaymentMethod,PaymentCard,Notes,RawText\n"

        for data in sampleData {
            let row = data.map { escapeCSVField($0) }.joined(separator: ",")
            csvContent += row + "\n"
        }

        return csvContent
    }
}

// MARK: - CSV Document for File Export
struct CSVDocument: FileDocument {
    static var readableContentTypes: [UTType] { [.commaSeparatedText] }

    var content: String

    init(content: String) {
        self.content = content
    }

    init(configuration: ReadConfiguration) throws {
        guard let data = configuration.file.regularFileContents,
              let string = String(data: data, encoding: .utf8)
        else {
            throw CocoaError(.fileReadCorruptFile)
        }
        content = string
    }

    func fileWrapper(configuration: WriteConfiguration) throws -> FileWrapper {
        let data = content.data(using: .utf8)!
        return .init(regularFileWithContents: data)
    }
}

// MARK: - Modern Expense Detail Components

struct ModernHeroSection: View {
    let expense: ExpenseRecord
    let exchangeRateService: ExchangeRateService

    var body: some View {
        VStack(spacing: 20) {
            // Amount Display
            VStack(spacing: 8) {
                Text("\(expense.amount, format: .currency(code: expense.currency))")
                    .font(.system(size: 48, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                if expense.currency != exchangeRateService.baseCurrency {
                    if let convertedAmount = exchangeRateService.convertAmount(
                        expense.amount,
                        from: expense.currency,
                        to: exchangeRateService.baseCurrency
                    ) {
                        Text("\(convertedAmount, format: .currency(code: exchangeRateService.baseCurrency))")
                            .font(.title3)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Merchant and Date
            HStack(spacing: 12) {
                if let merchant = expense.merchant, !merchant.isEmpty {
                    HStack(spacing: 6) {
                        Image(systemName: "building.2.fill")
                            .font(.caption)
                        Text(merchant)
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(.blue.opacity(0.1))
                    )
                    .foregroundColor(.blue)

                    Text("•")
                        .foregroundColor(.secondary)
                }

                Text(DateFormatter.abbreviatedMonthFormatter.string(from: expense.effectiveDate))
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.top, 40)
        .padding(.bottom, 60)
        .frame(maxWidth: .infinity)
    }
}

struct ModernInfoCard: View {
    let expense: ExpenseRecord

    var body: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "info.circle.fill")
                    .foregroundColor(.blue)
                Text("Details")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }

            // Info Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ModernInfoItem(
                    icon: "creditcard.fill",
                    title: "Payment",
                    value: expense.paymentMethod ?? "Unknown",
                    color: .purple
                )

                ModernInfoItem(
                    icon: "dollarsign.circle.fill",
                    title: "Currency",
                    value: expense.currency,
                    color: .orange
                )

                if let locationName = expense.locationName, !locationName.isEmpty {
                    ModernInfoItem(
                        icon: "location.fill",
                        title: "Location",
                        value: locationName,
                        color: .red
                    )
                }

                if let notes = expense.notes, !notes.isEmpty {
                    ModernInfoItem(
                        icon: "text.alignleft",
                        title: "Notes",
                        value: notes,
                        color: .green
                    )
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        )
    }
}

struct ModernInfoItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 6) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.caption)
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }

            Text(value)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(color.opacity(0.05))
        )
    }
}

struct ModernTagsCard: View {
    let tags: [String]
    let onTagTap: (String) -> Void

    var body: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "tag.fill")
                    .foregroundColor(.blue)
                Text("Tags")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }

            // Tags Flow Layout
            FlowLayout(spacing: 8) {
                ForEach(tags, id: \.self) { tag in
                    Button(action: { onTagTap(tag) }) {
                        Text("#\(tag)")
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                Capsule()
                                    .fill(.blue.opacity(0.1))
                            )
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        )
    }
}

struct ModernDetailsCard: View {
    let expense: ExpenseRecord
    let exchangeRateService: ExchangeRateService

    var body: some View {
        VStack(spacing: 16) {
            // Card Header
            HStack {
                Image(systemName: "chart.line.uptrend.xyaxis")
                    .foregroundColor(.green)
                Text("Exchange Rate")
                    .font(.headline)
                    .fontWeight(.semibold)
                Spacer()
            }

            // Exchange Rate Info
            if expense.currency != exchangeRateService.baseCurrency {
                VStack(alignment: .leading, spacing: 8) {
                    if let exchangeRate = expense.exchangeRate,
                       let baseCurrency = expense.baseCurrency {
                        let rateString = String(format: "%.4f", exchangeRate)
                        Text("1 \(baseCurrency) = \(rateString) \(expense.currency)")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Text("Rate at time of expense")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    } else if let currentRate = exchangeRateService.exchangeRates[expense.currency] {
                        let rateString = String(format: "%.4f", currentRate)
                        Text("1 \(exchangeRateService.baseCurrency) = \(rateString) \(expense.currency)")
                            .font(.subheadline)
                            .foregroundColor(.primary)

                        Text("Current rate")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(.green.opacity(0.05))
                )
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 4)
        )
    }
}

// Simple FlowLayout for tags
struct FlowLayout<Content: View>: View {
    let spacing: CGFloat
    let content: () -> Content

    init(spacing: CGFloat = 8, @ViewBuilder content: @escaping () -> Content) {
        self.spacing = spacing
        self.content = content
    }

    var body: some View {
        LazyVGrid(columns: [
            GridItem(.adaptive(minimum: 80), spacing: spacing)
        ], spacing: spacing) {
            content()
        }
    }
}

// MARK: - Apple Style Detail Components

struct DetailRow: View {
    let icon: String
    let title: String
    let value: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.system(size: 16))
                .foregroundColor(.blue)
                .frame(width: 20)

            Text(title)
                .font(.body)
                .foregroundColor(.primary)

            Spacer()

            Text(value)
                .font(.body)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.trailing)
        }
        .padding(.vertical, 2)
    }
}

// MARK: - Compact Expense Detail Components

struct CompactHeroSection: View {
    let expense: ExpenseRecord
    let exchangeRateService: ExchangeRateService

    var body: some View {
        VStack(spacing: 12) {
            // Amount Display
            VStack(spacing: 4) {
                Text("\(expense.amount, format: .currency(code: expense.currency))")
                    .font(.system(size: 36, weight: .bold, design: .rounded))
                    .foregroundColor(.primary)

                if expense.currency != exchangeRateService.baseCurrency {
                    if let convertedAmount = exchangeRateService.convertAmount(
                        expense.amount,
                        from: expense.currency,
                        to: exchangeRateService.baseCurrency
                    ) {
                        Text("\(convertedAmount, format: .currency(code: exchangeRateService.baseCurrency))")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
            }

            // Merchant and Date
            HStack(spacing: 8) {
                if let merchant = expense.merchant, !merchant.isEmpty {
                    HStack(spacing: 4) {
                        Image(systemName: "building.2.fill")
                            .font(.caption2)
                        Text(merchant)
                            .font(.caption)
                            .fontWeight(.medium)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(.blue.opacity(0.1))
                    )
                    .foregroundColor(.blue)

                    Text("•")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }

                Text(DateFormatter.abbreviatedMonthFormatter.string(from: expense.effectiveDate))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.top, 20)
        .padding(.bottom, 30)
        .frame(maxWidth: .infinity)
    }
}

struct CompactInfoCard: View {
    let expense: ExpenseRecord

    var body: some View {
        VStack(spacing: 12) {
            // Info Grid
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 8) {
                CompactInfoItem(
                    icon: "creditcard.fill",
                    title: "Payment",
                    value: expense.paymentMethod ?? "Unknown",
                    color: .purple
                )

                CompactInfoItem(
                    icon: "dollarsign.circle.fill",
                    title: "Currency",
                    value: expense.currency,
                    color: .orange
                )

                if let locationName = expense.locationName, !locationName.isEmpty {
                    CompactInfoItem(
                        icon: "location.fill",
                        title: "Location",
                        value: locationName,
                        color: .red
                    )
                }

                if let notes = expense.notes, !notes.isEmpty {
                    CompactInfoItem(
                        icon: "text.alignleft",
                        title: "Notes",
                        value: notes,
                        color: .green
                    )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 6, x: 0, y: 2)
        )
    }
}

struct CompactInfoItem: View {
    let icon: String
    let title: String
    let value: String
    let color: Color

    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 4) {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.caption2)
                Text(title)
                    .font(.caption2)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)
            }

            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.primary)
                .lineLimit(1)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding(8)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(color.opacity(0.05))
        )
    }
}

struct CompactTagsCard: View {
    let tags: [String]
    let onTagTap: (String) -> Void

    var body: some View {
        VStack(spacing: 8) {
            // Tags Flow Layout
            LazyVGrid(columns: [
                GridItem(.adaptive(minimum: 60))
            ], spacing: 6) {
                ForEach(tags, id: \.self) { tag in
                    Button(action: { onTagTap(tag) }) {
                        Text("#\(tag)")
                            .font(.caption)
                            .fontWeight(.medium)
                            .padding(.horizontal, 8)
                            .padding(.vertical, 4)
                            .background(
                                Capsule()
                                    .fill(.blue.opacity(0.1))
                            )
                            .foregroundColor(.blue)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }
}

struct CompactDetailsCard: View {
    let expense: ExpenseRecord
    let exchangeRateService: ExchangeRateService

    var body: some View {
        VStack(spacing: 8) {
            // Exchange Rate Info
            if expense.currency != exchangeRateService.baseCurrency {
                VStack(alignment: .leading, spacing: 4) {
                    if let exchangeRate = expense.exchangeRate,
                       let baseCurrency = expense.baseCurrency {
                        let rateString = String(format: "%.4f", exchangeRate)
                        Text("1 \(baseCurrency) = \(rateString) \(expense.currency)")
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text("Rate at time of expense")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } else if let currentRate = exchangeRateService.exchangeRates[expense.currency] {
                        let rateString = String(format: "%.4f", currentRate)
                        Text("1 \(exchangeRateService.baseCurrency) = \(rateString) \(expense.currency)")
                            .font(.caption)
                            .foregroundColor(.primary)

                        Text("Current rate")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(8)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(.green.opacity(0.05))
                )
            }
        }
        .padding(12)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: .black.opacity(0.05), radius: 4, x: 0, y: 1)
        )
    }
}

// MARK: - Example Text Component
struct ExampleText: View {
    let text: String

    init(_ text: String) {
        self.text = text
    }

    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: "quote.bubble")
                .font(.caption)
                .foregroundColor(.blue)

            Text(text)
                .font(.caption)
                .foregroundColor(.secondary)
                .italic()
        }
    }
}

// MARK: - Custom Endpoint Configuration View
struct CustomEndpointConfigView: View {
    @Binding var customEndpoint: String
    @Binding var isUsingCustomEndpoint: Bool
    let onSave: (String, Bool) -> Void

    @Environment(\.dismiss) private var dismiss
    @State private var tempEndpoint: String = ""
    @State private var tempIsEnabled: Bool = false
    @State private var showingTestResult = false
    @State private var testResultMessage = ""
    @State private var isTestingConnection = false

    var body: some View {
        NavigationView {
            Form {
                Section {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "server.rack")
                                .foregroundColor(.purple)
                                .font(.title2)

                            VStack(alignment: .leading, spacing: 4) {
                                Text("Custom API Endpoint")
                                    .font(.title2)
                                    .fontWeight(.bold)

                                Text("Use your own server for enhanced privacy")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }

                        Text("When you deploy your own FinPin server, you can configure the app to use your endpoint instead of the default service. This ensures your data stays completely under your control.")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.top, 4)
                    }
                    .padding(.vertical, 8)
                }

                Section("Configuration") {
                    Toggle("Enable Custom Endpoint", isOn: $tempIsEnabled)
                        .tint(.purple)

                    if tempIsEnabled {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Server URL")
                                .font(.headline)

                            TextField("https://your-server.com/api/v1", text: $tempEndpoint)
                                .textFieldStyle(RoundedBorderTextFieldStyle())
                                .autocapitalization(.none)
                                .disableAutocorrection(true)
                                .keyboardType(.URL)

                            Text("Enter the full API endpoint URL of your deployed FinPin server")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 4)

                        Button(action: testConnection) {
                            HStack {
                                if isTestingConnection {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                } else {
                                    Image(systemName: "network")
                                }

                                Text(isTestingConnection ? "Testing..." : "Test Connection")
                            }
                            .foregroundColor(.blue)
                        }
                        .disabled(tempEndpoint.isEmpty || isTestingConnection)
                    }
                }

                if tempIsEnabled {
                    Section("How to Deploy Your Own Server?") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "server.rack")
                                    .foregroundColor(.blue)
                                Text("Deploy Your Own API Server")
                                    .font(.headline)
                                    .foregroundColor(.blue)
                            }

                            VStack(alignment: .leading, spacing: 6) {
                                Text("Example endpoint: https://api.finpin.app/api/v1")
                                    .font(.caption)
                                    .foregroundColor(.purple)
                                    .padding(.bottom, 4)

                                Text("To deploy your own server:")
                                    .font(.caption)
                                    .fontWeight(.medium)
                                    .foregroundColor(.primary)

                                Text("Visit: https://github.com/finpin-app/finpin-serverless-api")
                                    .font(.caption)
                                    .foregroundColor(.blue)
                                    .underline()

                                Text("• Complete privacy control over your data")
                                Text("• Free deployment on Cloudflare Workers")
                                Text("• Support for OpenAI and ARK APIs")
                                Text("• Easy setup with automated scripts")
                            }
                            .font(.caption)
                            .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("API Configuration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Save") {
                        onSave(tempEndpoint, tempIsEnabled)
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .onAppear {
            tempEndpoint = customEndpoint
            tempIsEnabled = isUsingCustomEndpoint
        }
        .alert("Connection Test", isPresented: $showingTestResult) {
            Button("OK") { }
        } message: {
            Text(testResultMessage)
        }
    }

    private func testConnection() {
        guard !tempEndpoint.isEmpty else { return }

        isTestingConnection = true

        Task { @MainActor in
            do {
                // Test the custom endpoint
                let testURL = tempEndpoint.hasSuffix("/") ? tempEndpoint + "health" : tempEndpoint + "/health"
                guard let url = URL(string: testURL) else {
                    throw URLError(.badURL)
                }

                let (_, response) = try await URLSession.shared.data(from: url)

                if let httpResponse = response as? HTTPURLResponse {
                    if httpResponse.statusCode == 200 {
                        testResultMessage = "✅ Connection successful!\n\nYour server is responding correctly."
                    } else {
                        testResultMessage = "⚠️ Server responded with status code \(httpResponse.statusCode).\n\nPlease check your server configuration."
                    }
                } else {
                    testResultMessage = "⚠️ Unexpected response format.\n\nPlease verify your endpoint URL."
                }

            } catch {
                testResultMessage = "❌ Connection failed.\n\nError: \(error.localizedDescription)\n\nPlease check your URL and server status."
            }

            isTestingConnection = false
            showingTestResult = true
        }
    }
}



// MARK: - Base Currency Picker View
struct BaseCurrencyPickerView: View {
    let selectedCurrency: String
    let onCurrencySelected: (String) -> Void
    @Environment(\.dismiss) private var dismiss
    
    @State private var searchText = ""
    
    // Use hardcoded currency list (from exchange-rates.finpin.app)
    private let majorCurrencies = CurrencyHelper.majorCurrencies
    private let allCurrencies = CurrencyHelper.allSupportedCurrencies
    
    // Filtered search results
    private var filteredCurrencies: [String] {
        if searchText.isEmpty {
            return []
        }
        
        let searchUpper = searchText.uppercased()
        return allCurrencies.filter { currency in
            // Exclude currencies already in major currencies list
            !majorCurrencies.contains(currency) &&
            (currency.uppercased().contains(searchUpper) ||
             CurrencyHelper.name(for: currency).uppercased().contains(searchUpper))
        }.sorted()
    }

    var body: some View {
        NavigationView {
            List {
                // Major currencies section (keep original design)
                Section {
                    ForEach(majorCurrencies, id: \.self) { currency in
                        CurrencyRow(
                            currency: currency,
                            isSelected: currency == selectedCurrency,
                            onTap: {
                                onCurrencySelected(currency)
                                dismiss()
                            }
                        )
                    }
                } header: {
                    Text("Major Currencies")
                }
                
                // Search other currencies section
                Section {
                    // Search bar
                    HStack {
                        Image(systemName: "magnifyingglass")
                            .foregroundColor(.secondary)
                        
                        TextField("Search currency code or name...", text: $searchText)
                            .textFieldStyle(PlainTextFieldStyle())
                            .autocapitalization(.allCharacters)
                            .disableAutocorrection(true)
                        
                        if !searchText.isEmpty {
                            Button(action: { searchText = "" }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                    
                    // Show selected currency if it's not in major currencies
                    if !majorCurrencies.contains(selectedCurrency) && allCurrencies.contains(selectedCurrency) {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Currently Selected")
                                .font(.caption)
                                .fontWeight(.medium)
                                .foregroundColor(.blue)
                            
                            CurrencyRow(
                                currency: selectedCurrency,
                                isSelected: true,
                                onTap: {
                                    // Already selected, just dismiss
                                    dismiss()
                                }
                            )
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(Color.blue.opacity(0.1))
                            )
                        }
                        .padding(.vertical, 4)
                    }
                    
                    // Search results
                    if !searchText.isEmpty {
                        if filteredCurrencies.isEmpty {
                            Text("No matching currencies found")
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .padding(.vertical, 8)
                        } else {
                            ForEach(filteredCurrencies.prefix(20), id: \.self) { currency in
                                CurrencyRow(
                                    currency: currency,
                                    isSelected: currency == selectedCurrency,
                                    onTap: {
                                        onCurrencySelected(currency)
                                        dismiss()
                                    }
                                )
                            }
                            
                            if filteredCurrencies.count > 20 {
                                Text("Showing first 20 results, please narrow your search")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .padding(.vertical, 4)
                            }
                        }
                    } else {
                        Text("Enter currency code or name to start searching")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.vertical, 8)
                    }
                } header: {
                    Text("More Currencies")
                }
            }
            .navigationTitle("Base Currency")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}



// MARK: - DateFormatter Extensions
extension DateFormatter {
    static let shortDateTime: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let timeFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter
    }()
    
    static let abbreviatedMonthFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d, yyyy"
        return formatter
    }()
}

#Preview {
    ContentView()
        .environmentObject(DataManager.shared)
}
