import SwiftUI

// MARK: - Main API Configuration View
struct APIConfigurationView: View {
    @StateObject private var configManager = APIConfigurationManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var showingAddConfiguration = false
    @State private var showingEditConfiguration: APIConfiguration?
    @State private var showingDeleteAlert = false
    @State private var configurationToDelete: APIConfiguration?
    
    let onSave: () -> Void
    
    var body: some View {
        NavigationView {
            Form {
                // Mode Selection Section
                Section {
                    VStack(alignment: .leading, spacing: 12) {
                        HStack {
                            Image(systemName: "gear")
                                .foregroundColor(.blue)
                                .font(.title2)
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("API Configuration")
                                    .font(.title2)
                                    .fontWeight(.bold)
                                
                                Text("Choose how FinPin connects to AI services")
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                Section("Connection Mode") {
                    VStack(spacing: 12) {
                        // FinPin Hosted Mode
                        ModeSelectionRow(
                            title: "FinPin Hosted",
                            subtitle: "Secure, managed service with built-in privacy",
                            iconName: "cloud.fill",
                            iconColor: .blue,
                            isSelected: configManager.currentMode == .hosted
                        ) {
                            configManager.setMode(.hosted)
                        }
                        
                        // Custom Provider Mode
                        ModeSelectionRow(
                            title: "Custom Provider",
                            subtitle: "Use your own API keys with OpenAI or Anthropic",
                            iconName: "key.fill",
                            iconColor: .purple,
                            isSelected: configManager.currentMode == .custom
                        ) {
                            configManager.setMode(.custom)
                        }
                    }
                }
                
                // Custom Configurations Section (only show in custom mode)
                if configManager.currentMode == .custom {
                    Section {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: "exclamationmark.triangle.fill")
                                    .foregroundColor(.orange)
                                Text("Security Notice")
                                    .font(.headline)
                                    .foregroundColor(.orange)
                            }
                            
                            Text("When using custom providers, signature verification is disabled. Your API keys are stored securely on your device only.")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .padding(.vertical, 4)
                    }
                    
                    Section("Custom API Configurations") {
                        ForEach(configManager.configurations.filter { $0.provider != .finpin }) { config in
                            ConfigurationRow(
                                configuration: config,
                                isActive: config.id == configManager.activeConfiguration?.id,
                                onSelect: {
                                    configManager.setActiveConfiguration(config)
                                },
                                onEdit: {
                                    showingEditConfiguration = config
                                },
                                onDelete: {
                                    configurationToDelete = config
                                    showingDeleteAlert = true
                                }
                            )
                        }
                        
                        // Add Configuration Button
                        Button(action: { showingAddConfiguration = true }) {
                            HStack {
                                Image(systemName: "plus.circle.fill")
                                    .foregroundColor(.green)
                                Text("Add New Configuration")
                                    .foregroundColor(.primary)
                            }
                        }
                    }
                }
                
                // Current Active Configuration Info
                if let activeConfig = configManager.activeConfiguration {
                    Section("Active Configuration") {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Image(systemName: activeConfig.provider.iconName)
                                    .foregroundColor(.green)
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(activeConfig.name)
                                        .font(.headline)
                                    Text(activeConfig.provider.displayName)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                                Spacer()
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.green)
                            }
                            
                            if configManager.currentMode == .custom {
                                Text("Endpoint: \(activeConfig.baseURL)")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }
            }
            .navigationTitle("API Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        onSave()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingAddConfiguration) {
            ConfigurationEditView(
                configuration: nil,
                onSave: { config in
                    configManager.addConfiguration(config)
                    showingAddConfiguration = false
                }
            )
        }
        .sheet(item: $showingEditConfiguration) { config in
            ConfigurationEditView(
                configuration: config,
                onSave: { updatedConfig in
                    configManager.updateConfiguration(updatedConfig)
                    showingEditConfiguration = nil
                }
            )
        }
        .alert("Delete Configuration", isPresented: $showingDeleteAlert) {
            Button("Cancel", role: .cancel) { }
            Button("Delete", role: .destructive) {
                if let config = configurationToDelete {
                    configManager.deleteConfiguration(config)
                }
            }
        } message: {
            if let config = configurationToDelete {
                Text("Are you sure you want to delete '\(config.name)'? This action cannot be undone.")
            }
        }
    }
}

// MARK: - Mode Selection Row
struct ModeSelectionRow: View {
    let title: String
    let subtitle: String
    let iconName: String
    let iconColor: Color
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                Image(systemName: iconName)
                    .foregroundColor(iconColor)
                    .font(.title2)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.headline)
                        .foregroundColor(.primary)
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title2)
                } else {
                    Image(systemName: "circle")
                        .foregroundColor(.secondary)
                        .font(.title2)
                }
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - Configuration Row
struct ConfigurationRow: View {
    let configuration: APIConfiguration
    let isActive: Bool
    let onSelect: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        HStack {
            Button(action: onSelect) {
                HStack(spacing: 12) {
                    Image(systemName: configuration.provider.iconName)
                        .foregroundColor(isActive ? .green : .secondary)
                        .font(.title3)
                        .frame(width: 24)
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(configuration.name)
                            .font(.headline)
                            .foregroundColor(.primary)
                        Text(configuration.provider.displayName)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    if isActive {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            Menu {
                if !isActive {
                    Button("Activate") {
                        onSelect()
                    }
                }
                Button("Edit", action: onEdit)
                Button("Delete", role: .destructive, action: onDelete)
            } label: {
                Image(systemName: "ellipsis.circle")
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
}

// MARK: - Preview
struct APIConfigurationView_Previews: PreviewProvider {
    static var previews: some View {
        APIConfigurationView(onSave: {})
    }
}
