import Foundation

// MARK: - API Provider Types
enum APIProvider: String, CaseIterable, Codable {
    case finpin = "finpin"
    case openai = "openai"
    case anthropic = "anthropic"
    
    var displayName: String {
        switch self {
        case .finpin:
            return "FinPin (Hosted)"
        case .openai:
            return "OpenAI"
        case .anthropic:
            return "Anthropic"
        }
    }
    
    var iconName: String {
        switch self {
        case .finpin:
            return "server.rack"
        case .openai:
            return "brain.head.profile"
        case .anthropic:
            return "cpu"
        }
    }
    
    var defaultBaseURL: String {
        switch self {
        case .finpin:
            return "https://api.finpin.app"
        case .openai:
            return "https://api.openai.com/v1"
        case .anthropic:
            return "https://api.anthropic.com/v1"
        }
    }
    
    var requiresAPIKey: Bool {
        switch self {
        case .finpin:
            return false
        case .openai, .anthropic:
            return true
        }
    }
}

// MARK: - API Configuration Mode
enum APIConfigurationMode: String, Codable {
    case hosted = "hosted"    // FinPin托管模式
    case custom = "custom"    // 自定义模式
    
    var displayName: String {
        switch self {
        case .hosted:
            return "FinPin Hosted"
        case .custom:
            return "Custom Provider"
        }
    }
}

// MARK: - API Configuration Model
struct APIConfiguration: Codable, Identifiable, Equatable {
    let id: UUID
    var name: String
    var provider: APIProvider
    var baseURL: String
    var apiKey: String
    var isActive: Bool
    var createdAt: Date
    var updatedAt: Date
    
    // Additional configuration for different providers
    var modelName: String?
    var maxTokens: Int?
    var temperature: Double?
    
    init(
        id: UUID = UUID(),
        name: String,
        provider: APIProvider,
        baseURL: String? = nil,
        apiKey: String = "",
        isActive: Bool = false,
        modelName: String? = nil,
        maxTokens: Int? = nil,
        temperature: Double? = nil
    ) {
        self.id = id
        self.name = name
        self.provider = provider
        self.baseURL = baseURL ?? provider.defaultBaseURL
        self.apiKey = apiKey
        self.isActive = isActive
        self.createdAt = Date()
        self.updatedAt = Date()
        self.modelName = modelName
        self.maxTokens = maxTokens
        self.temperature = temperature
    }
    
    // Default configurations for different providers
    static func defaultOpenAI() -> APIConfiguration {
        return APIConfiguration(
            name: "OpenAI GPT-4",
            provider: .openai,
            modelName: "gpt-4",
            maxTokens: 4096,
            temperature: 0.7
        )
    }
    
    static func defaultAnthropic() -> APIConfiguration {
        return APIConfiguration(
            name: "Claude 3.5 Sonnet",
            provider: .anthropic,
            modelName: "claude-3-5-sonnet-20241022",
            maxTokens: 4096,
            temperature: 0.7
        )
    }
    
    static func defaultFinPin() -> APIConfiguration {
        return APIConfiguration(
            name: "FinPin Hosted",
            provider: .finpin,
            isActive: true
        )
    }
    
    // Validation
    var isValid: Bool {
        guard !name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              !baseURL.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty,
              URL(string: baseURL) != nil else {
            return false
        }
        
        if provider.requiresAPIKey && apiKey.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return false
        }
        
        return true
    }
    
    mutating func updateTimestamp() {
        updatedAt = Date()
    }
}

// MARK: - API Configuration Manager
class APIConfigurationManager: ObservableObject {
    static let shared = APIConfigurationManager()
    
    @Published var configurations: [APIConfiguration] = []
    @Published var currentMode: APIConfigurationMode = .hosted
    @Published var activeConfiguration: APIConfiguration?
    
    private let userDefaults = UserDefaults.standard
    private let configurationsKey = "api_configurations"
    private let currentModeKey = "api_configuration_mode"
    private let activeConfigurationKey = "active_api_configuration_id"
    
    private init() {
        loadConfigurations()
    }
    
    // MARK: - Configuration Management
    func addConfiguration(_ config: APIConfiguration) {
        var newConfig = config
        newConfig.updateTimestamp()
        configurations.append(newConfig)
        saveConfigurations()
    }
    
    func updateConfiguration(_ config: APIConfiguration) {
        guard let index = configurations.firstIndex(where: { $0.id == config.id }) else { return }
        var updatedConfig = config
        updatedConfig.updateTimestamp()
        configurations[index] = updatedConfig
        
        if activeConfiguration?.id == config.id {
            activeConfiguration = updatedConfig
        }
        
        saveConfigurations()
    }
    
    func deleteConfiguration(_ config: APIConfiguration) {
        configurations.removeAll { $0.id == config.id }
        
        if activeConfiguration?.id == config.id {
            activeConfiguration = configurations.first { $0.isActive }
        }
        
        saveConfigurations()
    }
    
    func setActiveConfiguration(_ config: APIConfiguration) {
        // Deactivate all configurations
        for i in configurations.indices {
            configurations[i].isActive = false
        }
        
        // Activate the selected configuration
        if let index = configurations.firstIndex(where: { $0.id == config.id }) {
            configurations[index].isActive = true
            activeConfiguration = configurations[index]
        }
        
        saveConfigurations()
    }
    
    func setMode(_ mode: APIConfigurationMode) {
        currentMode = mode
        userDefaults.set(mode.rawValue, forKey: currentModeKey)

        // Update active configuration based on mode
        if mode == .hosted {
            // Ensure FinPin configuration exists and is active
            if !configurations.contains(where: { $0.provider == .finpin }) {
                addConfiguration(.defaultFinPin())
            }
            if let finpinConfig = configurations.first(where: { $0.provider == .finpin }) {
                setActiveConfiguration(finpinConfig)
            }
        } else if mode == .custom {
            // In custom mode, if there's no active configuration or it's FinPin,
            // try to set the first non-FinPin configuration as active
            if activeConfiguration?.provider == .finpin || activeConfiguration == nil {
                if let customConfig = configurations.first(where: { $0.provider != .finpin }) {
                    setActiveConfiguration(customConfig)
                }
            }
        }
    }
    
    // MARK: - Persistence
    private func saveConfigurations() {
        do {
            let data = try JSONEncoder().encode(configurations)
            userDefaults.set(data, forKey: configurationsKey)
            
            if let activeId = activeConfiguration?.id {
                userDefaults.set(activeId.uuidString, forKey: activeConfigurationKey)
            }
        } catch {
            print("Failed to save API configurations: \(error)")
        }
    }
    
    private func loadConfigurations() {
        // Load mode
        if let modeString = userDefaults.string(forKey: currentModeKey),
           let mode = APIConfigurationMode(rawValue: modeString) {
            currentMode = mode
        }
        
        // Load configurations
        if let data = userDefaults.data(forKey: configurationsKey) {
            do {
                configurations = try JSONDecoder().decode([APIConfiguration].self, from: data)
            } catch {
                print("Failed to load API configurations: \(error)")
                configurations = []
            }
        }
        
        // Ensure default FinPin configuration exists
        if !configurations.contains(where: { $0.provider == .finpin }) {
            configurations.append(.defaultFinPin())
        }
        
        // Load active configuration
        if let activeIdString = userDefaults.string(forKey: activeConfigurationKey),
           let activeId = UUID(uuidString: activeIdString),
           let config = configurations.first(where: { $0.id == activeId }) {
            activeConfiguration = config
        } else {
            // Default to FinPin if no active configuration
            activeConfiguration = configurations.first { $0.provider == .finpin }
        }
    }
    
    // MARK: - Utility Methods
    var isCustomModeEnabled: Bool {
        return currentMode == .custom
    }

    var isSignatureVerificationDisabled: Bool {
        return currentMode == .custom && activeConfiguration?.provider != .finpin
    }

    func getActiveEndpoint() -> String {
        return activeConfiguration?.baseURL ?? APIProvider.finpin.defaultBaseURL
    }

    func getActiveAPIKey() -> String {
        return activeConfiguration?.apiKey ?? ""
    }
}
