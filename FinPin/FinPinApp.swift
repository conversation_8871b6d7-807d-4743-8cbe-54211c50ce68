import SwiftUI
import UIKit
import Foundation
import Vision
import PhotosUI
import CryptoKit
import StoreKit
import CloudKit
import Intents
import IntentsUI
import AppIntents

// MARK: - Time Extraction Utilities
class TimeExtractor {
    static let shared = TimeExtractor()

    private let timePatterns: [NSRegularExpression] = {
        let patterns = [
            // 中文时间格式
            "(?:打印时间|创单时间|交易时间|消费时间|时间)[:：]?\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 英文时间格式
            "(?:Time|Date|Transaction Time|Print Time)[:：]?\\s*(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 纯时间格式 YYYY-MM-DD HH:mm:ss
            "(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?)",
            // 带AM/PM的时间
            "(\\d{4}[-/]\\d{1,2}[-/]\\d{1,2}\\s+\\d{1,2}:\\d{2}(?::\\d{2})?\\s*(?:AM|PM|am|pm))",
            // ISO格式但不带时区
            "(\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2})",
        ]

        return patterns.compactMap { pattern in
            try? NSRegularExpression(pattern: pattern, options: [.caseInsensitive])
        }
    }()

    private let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "en_US_POSIX")
        return formatter
    }()

    func extractLocalTime(from text: String) -> Date? {
        print("🕐 TimeExtractor: Extracting time from text...")

        for pattern in timePatterns {
            let range = NSRange(text.startIndex..<text.endIndex, in: text)
            let matches = pattern.matches(in: text, options: [], range: range)

            for match in matches {
                if match.numberOfRanges > 1 {
                    let timeRange = match.range(at: 1)
                    if let swiftRange = Range(timeRange, in: text) {
                        let timeString = String(text[swiftRange]).trimmingCharacters(in: .whitespacesAndNewlines)
                        print("🕐 TimeExtractor: Found time string: '\(timeString)'")

                        if let date = parseTimeString(timeString) {
                            print("✅ TimeExtractor: Successfully parsed local time: \(date)")
                            return date
                        }
                    }
                }
            }
        }

        print("❌ TimeExtractor: No valid time found in text")
        return nil
    }

    private func parseTimeString(_ timeString: String) -> Date? {
        let formats = [
            "yyyy-MM-dd HH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd HH:mm:ss a",
            "yyyy/MM/dd HH:mm:ss a"
        ]

        for format in formats {
            dateFormatter.dateFormat = format
            if let date = dateFormatter.date(from: timeString) {
                return date
            }
        }

        return nil
    }

    func formatLocalTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }
}

// MARK: - API Endpoint Manager
class APIEndpointManager {
    static let shared = APIEndpointManager()

    // MARK: - Version Configuration
    private let currentAPIVersion = "v1.1.0"
    private let legacyAPIVersion = "v1.0.8"

    // MARK: - Domain Configuration
    private struct Domains {
        static let legacy = "api.finpin.app"
        static let current = "v110.finpin.app"  // New domain for v1.1.0+
        static let fallback = "finpin-api-v1-1-0.r6r.workers.dev"  // Updated worker URL
    }

    // MARK: - Endpoint Configuration
    struct APIEndpoints {
        let baseURL: String
        let deviceRegister: String
        let expenseParse: String
        let version: String

        init(baseURL: String, version: String) {
            self.baseURL = baseURL
            self.version = version
            self.deviceRegister = "\(baseURL)/api/v1/device/register"
            self.expenseParse = "\(baseURL)/api/v1/parse/expense"
        }
    }

    // MARK: - Current Endpoints
    private(set) var currentEndpoints: APIEndpoints
    private(set) var legacyEndpoints: APIEndpoints

    private init() {
        // Initialize current endpoints (v1.1.0+)
        self.currentEndpoints = APIEndpoints(
            baseURL: "https://\(Domains.current)",
            version: currentAPIVersion
        )

        // Initialize legacy endpoints (v1.0.8 and below)
        self.legacyEndpoints = APIEndpoints(
            baseURL: "https://\(Domains.legacy)",
            version: legacyAPIVersion
        )

        print("🌐 APIEndpointManager initialized")
        print("📍 Current API: \(currentEndpoints.baseURL) (v\(currentAPIVersion))")
        print("📍 Legacy API: \(legacyEndpoints.baseURL) (v\(legacyAPIVersion))")
    }

    // MARK: - Public Methods
    func getCurrentBaseURL() -> String {
        return currentEndpoints.baseURL
    }

    func getLegacyBaseURL() -> String {
        return legacyEndpoints.baseURL
    }

    func getDeviceRegisterURL(useLegacy: Bool = false) -> String {
        return useLegacy ? legacyEndpoints.deviceRegister : currentEndpoints.deviceRegister
    }

    func getExpenseParseURL(useLegacy: Bool = false) -> String {
        return useLegacy ? legacyEndpoints.expenseParse : currentEndpoints.expenseParse
    }
}

// MARK: - Migration Manager
@MainActor
class MigrationManager: ObservableObject {
    static let shared = MigrationManager()

    @Published var isMigrating = false
    @Published var migrationProgress: Double = 0.0
    @Published var migrationStatus = ""
    @Published var showMigrationAlert = false

    private let currentVersion = "1.1.0"
    private let lastMigrationVersionKey = "LastMigrationVersion"

    private init() {}

    func checkAndPerformMigration() async {
        let lastMigrationVersion = UserDefaults.standard.string(forKey: lastMigrationVersionKey)

        // Check if this is a fresh install (no previous data)
        let dataManager = DataManager.shared
        let hasExistingData = !dataManager.expenses.isEmpty

        if lastMigrationVersion == nil {
            if hasExistingData {
                // User has data but no migration version - this is an upgrade from pre-1.1.0
                print("🔄 Migration needed from pre-1.1.0 to \(currentVersion)")
                await performMigration(from: nil)
            } else {
                // Fresh install - no migration needed
                print("✅ Fresh install detected - setting version to \(currentVersion)")
                UserDefaults.standard.set(currentVersion, forKey: lastMigrationVersionKey)
            }
        } else if shouldMigrate(from: lastMigrationVersion!, to: currentVersion) {
            print("🔄 Migration needed from \(lastMigrationVersion!) to \(currentVersion)")
            await performMigration(from: lastMigrationVersion)
        } else {
            print("✅ No migration needed - already at version \(currentVersion)")
        }
    }

    private func shouldMigrate(from oldVersion: String, to newVersion: String) -> Bool {
        // Simple version comparison - migrate if old version is before 1.1.0
        let oldComponents = oldVersion.split(separator: ".").compactMap { Int($0) }
        let newComponents = newVersion.split(separator: ".").compactMap { Int($0) }

        guard oldComponents.count >= 2, newComponents.count >= 2 else { return true }

        // Check major.minor version
        if oldComponents[0] < newComponents[0] { return true }
        if oldComponents[0] == newComponents[0] && oldComponents[1] < newComponents[1] { return true }

        return false
    }

    private func performMigration(from oldVersion: String?) async {
        isMigrating = true
        migrationProgress = 0.0
        showMigrationAlert = true

        defer {
            isMigrating = false
            showMigrationAlert = false
        }

        do {
            migrationStatus = "Checking data integrity..."
            migrationProgress = 0.1
            try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

            // Step 1: Validate RawText storage
            await CloudKitManager.shared.validateRawTextStorage()

            migrationStatus = "Loading expense records..."
            migrationProgress = 0.2
            try? await Task.sleep(nanoseconds: 500_000_000)

            // Step 2: Load all expenses
            let dataManager = DataManager.shared
            let expenses = dataManager.expenses

            migrationStatus = "Fixing timestamp formats..."
            migrationProgress = 0.3

            // Step 3: Fix timestamp issues
            var updatedExpenses: [ExpenseRecord] = []
            let totalExpenses = expenses.count

            for (index, expense) in expenses.enumerated() {
                let updatedExpense = await fixExpenseTimestamp(expense)
                updatedExpenses.append(updatedExpense)

                // Update progress
                let progress = 0.3 + (Double(index + 1) / Double(totalExpenses)) * 0.5
                migrationProgress = progress
                migrationStatus = "Processing record \(index + 1)/\(totalExpenses)..."

                // Small delay to show progress
                if index % 10 == 0 {
                    try? await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
                }
            }

            migrationStatus = "Saving updates..."
            migrationProgress = 0.9
            try? await Task.sleep(nanoseconds: 500_000_000)

            // Step 4: Save updated expenses
            dataManager.expenses = updatedExpenses
            dataManager.saveExpenses()

            // Step 5: Sync to CloudKit if available
            if CloudKitManager.shared.isCloudKitAvailable {
                migrationStatus = "Syncing to iCloud..."
                try await dataManager.syncToiCloud()
            }

            migrationProgress = 1.0
            migrationStatus = "Migration completed!"
            try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

            // Mark migration as completed
            UserDefaults.standard.set(currentVersion, forKey: lastMigrationVersionKey)

            print("✅ Migration completed successfully to version \(currentVersion)")

        } catch {
            print("❌ Migration failed: \(error)")
            migrationStatus = "Migration failed: \(error.localizedDescription)"
            try? await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
        }
    }

    private func fixExpenseTimestamp(_ expense: ExpenseRecord) async -> ExpenseRecord {
        var updatedExpense = expense

        // Priority 1: Try to extract time from rawText using regex
        if let rawText = expense.rawText, !rawText.isEmpty {
            if let extractedTime = TimeExtractor.shared.extractLocalTime(from: rawText) {
                print("🔧 Fixed timestamp for expense \(expense.id) using regex extraction")
                updatedExpense.transactionDate = extractedTime
                updatedExpense.originalTimestamp = TimeExtractor.shared.formatLocalTime(extractedTime)
                return updatedExpense
            }
        }

        // Priority 2: Fix existing timestamp if it has timezone issues
        if let originalTimestamp = expense.originalTimestamp {
            // Check if timestamp has 'Z' suffix (UTC timezone marker)
            if originalTimestamp.hasSuffix("Z") {
                let localTimeString = String(originalTimestamp.dropLast()) // Remove 'Z'

                let localFormatter = DateFormatter()
                localFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
                localFormatter.timeZone = TimeZone.current

                if let localTime = localFormatter.date(from: localTimeString) {
                    print("🔧 Fixed UTC timestamp for expense \(expense.id): \(originalTimestamp) -> \(localTimeString)")
                    updatedExpense.transactionDate = localTime
                    updatedExpense.originalTimestamp = localTimeString
                    return updatedExpense
                }
            }
        }

        // No changes needed
        return updatedExpense
    }
}

// MARK: - Migration Alert View
struct MigrationAlertView: View {
    @ObservedObject var migrationManager = MigrationManager.shared

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "arrow.up.circle.fill")
                .font(.system(size: 50))
                .foregroundColor(.blue)

            Text("Upgrading Data")
                .font(.title2)
                .fontWeight(.semibold)

            Text("We're optimizing your expense records to ensure more accurate time display. This may take a few minutes.")
                .font(.body)
                .multilineTextAlignment(.center)
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                ProgressView(value: migrationManager.migrationProgress)
                    .progressViewStyle(LinearProgressViewStyle())

                Text(migrationManager.migrationStatus)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(30)
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(radius: 10)
    }
}

// MARK: - Expense Record Model
struct ExpenseRecord: Identifiable, Codable, Hashable {
    let id: UUID
    var amount: Decimal
    var currency: String
    var merchant: String?
    var date: Date
    var transactionDate: Date? // 实际消费时间，优先使用
    var originalTimestamp: String? // Original timestamp from API
    var latitude: Double
    var longitude: Double
    var locationName: String?
    var tags: [String]
    var paymentMethod: String?
    var paymentCard: String?
    var notes: String?
    var rawText: String?
    var confidence: DataConfidence // 数据可信度

    // 汇率转换信息 (记录创建时的汇率)
    var exchangeRate: Double? // 相对于基础货币的汇率
    var baseCurrency: String? // 记录时的基础货币
    var convertedAmount: Decimal? // 转换后的金额

    // 多币种预存储 - 记录创建时转换并存储所有主流货币金额
    var majorCurrencyAmounts: [String: Decimal]? // 主流货币金额映射
    var exchangeRatesSnapshot: [String: Double]? // 记录时的汇率快照

    // 数据可信度枚举
    enum DataConfidence: String, Codable, CaseIterable {
        case high = "high"       // 服务器API解析
        case medium = "medium"   // 本地OCR解析
        case low = "low"         // 手动输入或部分识别
        case manual = "manual"   // 完全手动输入

        var displayName: String {
            switch self {
            case .high: return "高"
            case .medium: return "中"
            case .low: return "低"
            case .manual: return "手动"
            }
        }

        var color: Color {
            switch self {
            case .high: return .green
            case .medium: return .orange
            case .low: return .red
            case .manual: return .blue
            }
        }
    }

    init(
        id: UUID = UUID(),
        amount: Decimal,
        currency: String = "USD",
        merchant: String? = nil,
        date: Date = Date(),
        transactionDate: Date? = nil,
        originalTimestamp: String? = nil,
        latitude: Double = 0.0,
        longitude: Double = 0.0,
        locationName: String? = nil,
        tags: [String] = [],
        paymentMethod: String? = nil,
        paymentCard: String? = nil,
        notes: String? = nil,
        rawText: String? = nil,
        confidence: DataConfidence = .manual,
        exchangeRate: Double? = nil,
        baseCurrency: String? = nil,
        convertedAmount: Decimal? = nil
    ) {
        self.originalTimestamp = originalTimestamp
        self.id = id
        self.amount = amount
        self.currency = currency
        self.merchant = merchant
        self.date = date
        self.transactionDate = transactionDate
        self.latitude = latitude
        self.longitude = longitude
        self.locationName = locationName
        self.tags = tags
        self.paymentMethod = paymentMethod
        self.paymentCard = paymentCard
        self.notes = notes
        self.rawText = rawText
        self.confidence = confidence
        self.exchangeRate = exchangeRate
        self.baseCurrency = baseCurrency
        self.convertedAmount = convertedAmount
    }

    // 便利初始化方法：自动获取当前汇率信息
    static func createWithCurrentExchangeRate(
        amount: Decimal,
        currency: String = "USD",
        merchant: String? = nil,
        date: Date = Date(),
        transactionDate: Date? = nil,
        originalTimestamp: String? = nil,
        latitude: Double = 0.0,
        longitude: Double = 0.0,
        locationName: String? = nil,
        tags: [String] = [],
        paymentMethod: String? = nil,
        paymentCard: String? = nil,
        notes: String? = nil,
        rawText: String? = nil,
        confidence: DataConfidence = .manual
    ) -> ExpenseRecord {
        let exchangeService = ExchangeRateService.shared
        let currentBaseCurrency = exchangeService.baseCurrency

        var exchangeRate: Double? = nil
        var convertedAmount: Decimal? = nil

        // 如果货币与基础货币不同，获取汇率
        if currency != currentBaseCurrency {
            if let rate = exchangeService.exchangeRates[currency] {
                exchangeRate = rate
                convertedAmount = amount / Decimal(rate)
            }
        } else {
            // 如果是基础货币，汇率为1
            exchangeRate = 1.0
            convertedAmount = amount
        }

        return ExpenseRecord(
            amount: amount,
            currency: currency,
            merchant: merchant,
            date: date,
            transactionDate: transactionDate,
            originalTimestamp: originalTimestamp,
            latitude: latitude,
            longitude: longitude,
            locationName: locationName,
            tags: tags,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            notes: notes,
            rawText: rawText,
            confidence: confidence,
            exchangeRate: exchangeRate,
            baseCurrency: currentBaseCurrency,
            convertedAmount: convertedAmount
        )
    }

    // 计算属性：优先使用交易时间，否则使用记录创建时间
    var effectiveDate: Date {
        return transactionDate ?? date
    }

    // 计算属性：安全的商户名称显示
    var displayMerchant: String {
        if let merchant = merchant, !merchant.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return merchant
        }
        return "Unknown Merchant"
    }

    // 计算属性：安全的位置名称显示
    var displayLocation: String {
        if let location = locationName, !location.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return location
        }
        return "Unknown Location"
    }

    // 计算属性：格式化的金额显示
    var formattedAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.maximumFractionDigits = 2

        if let formattedString = formatter.string(from: NSDecimalNumber(decimal: amount)) {
            return formattedString
        }

        // 备用格式化方案
        let currencySymbol = CurrencyHelper.symbol(for: currency)
        return "\(currencySymbol)\(amount)"
    }

    // 计算属性：是否有位置信息
    var hasLocationData: Bool {
        return latitude != 0.0 || longitude != 0.0 || locationName != nil
    }

    // Search matching
    func matches(searchText: String) -> Bool {
        let lowercased = searchText.lowercased()
        return merchant?.lowercased().contains(lowercased) == true ||
               locationName?.lowercased().contains(lowercased) == true ||
               tags.contains { $0.lowercased().contains(lowercased) } ||
               notes?.lowercased().contains(lowercased) == true ||
               currency.lowercased().contains(lowercased) ||
               formattedAmount.lowercased().contains(lowercased) ||
               rawText?.lowercased().contains(lowercased) == true ||
               DateFormatter.searchFormatter.string(from: date).contains(lowercased)
    }

    // Format tags display
    var formattedTags: String {
        tags.map { "#\($0)" }.joined(separator: " ")
    }
}

// MARK: - Subscription Manager
@MainActor
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()

    @Published var isSubscribed = false
    @Published var isTrialActive = false
    @Published var subscriptionStatus: SubscriptionStatus = .notSubscribed
    @Published var products: [Product] = []
    @Published var purchaseError: String?
    @Published var isLoading = false
    @Published var subscriptionExpiryDate: Date?

    // Expense count tracking
    @Published var expenseCount: Int = 0 {
        didSet {
            UserDefaults.standard.set(expenseCount, forKey: "ExpenseCount")
        }
    }
    
    // Expense limit - 50 for debugging, 100 for production
    let expenseLimit: Int = {
        #if DEBUG
        return 10  // Debug limit
        #else
        return 100  // Production limit
        #endif
    }()

    // Use the product ID from premium-1.storekit
    private let possibleProductIDs = ["p1"]

    private var activeProductID: String?
    private var updateListenerTask: Task<Void, Error>?

    enum SubscriptionStatus {
        case notSubscribed
        case trial
        case subscribed
        case expired
        case gracePeriod
    }

    init() {
        print("🚀 SubscriptionManager initializing...")

        // Load expense count from UserDefaults
        self.expenseCount = UserDefaults.standard.integer(forKey: "ExpenseCount")
        print("📊 Loaded expense count: \(expenseCount)")

        // Load cached subscription status from UserDefaults for immediate UI update
        loadCachedSubscriptionStatus()

        // DEVELOPER TOOLS: Clear all subscription caches for testing if requested
        #if DEBUG || targetEnvironment(simulator)
        clearAllSubscriptionCachesIfNeeded()
        #endif

        // MIGRATION: Check for legacy subscription status and provide backward compatibility
        // migrateLegacySubscriptionStatus() - Function not found, commented out

        // Check StoreKit availability
        checkStoreKitAvailability()

        // Start listening for transaction updates
        updateListenerTask = listenForTransactions()

        Task {
            await loadProducts()
            await checkSubscriptionStatus()
        }
    }

    // MARK: - Cached Status Loading
    private func loadCachedSubscriptionStatus() {
        // Load cached subscription status for immediate UI update
        let cachedIsSubscribed = UserDefaults.standard.bool(forKey: "IsSubscribed")
        let cachedIsTrialActive = UserDefaults.standard.bool(forKey: "IsTrialActive")
        let cachedExpiryDate = UserDefaults.standard.object(forKey: "SubscriptionExpiryDate") as? Date

        if cachedIsSubscribed || cachedIsTrialActive {
            print("📱 Loading cached subscription status:")
            print("   - Subscribed: \(cachedIsSubscribed)")
            print("   - Trial: \(cachedIsTrialActive)")
            print("   - Expiry: \(cachedExpiryDate?.description ?? "nil")")

            self.isSubscribed = cachedIsSubscribed
            self.isTrialActive = cachedIsTrialActive
            self.subscriptionExpiryDate = cachedExpiryDate

            // Set appropriate status
            if cachedIsSubscribed {
                self.subscriptionStatus = cachedIsTrialActive ? .trial : .subscribed
            } else {
                self.subscriptionStatus = .notSubscribed
            }

            print("✅ Cached subscription status loaded for immediate UI update")
        } else {
            print("📱 No cached subscription status found")
        }
    }

    private func cacheSubscriptionStatus() {
        // Cache current subscription status for faster app startup
        UserDefaults.standard.set(isSubscribed, forKey: "IsSubscribed")
        UserDefaults.standard.set(isTrialActive, forKey: "IsTrialActive")

        if let expiryDate = subscriptionExpiryDate {
            UserDefaults.standard.set(expiryDate, forKey: "SubscriptionExpiryDate")
        } else {
            UserDefaults.standard.removeObject(forKey: "SubscriptionExpiryDate")
        }

        print("💾 Subscription status cached for faster startup")
    }

    // MARK: - Developer Tools: Clear All Subscription Caches
    private func clearAllSubscriptionCachesIfNeeded() {
        // Check if we should reset subscription caches (for development/testing)
        let shouldReset = ProcessInfo.processInfo.environment["RESET_SUBSCRIPTION"] == "1"

        if shouldReset {
            print("🔧 DEVELOPER MODE: Resetting all subscription caches...")
            clearAllSubscriptionCaches()
        }
    }
    
    func clearAllSubscriptionCaches() {
        print("🧹 Clearing all subscription caches...")
        
        // Clear UserDefaults subscription keys
        let userDefaultsKeys = [
            "IsSubscribed",
            "IsTrialActive", 
            "SubscriptionExpiryDate",
            "ExpenseCount",
            "SubscriptionMigrationCompleted",
            "HadLegacySubscription"
        ]
        
        for key in userDefaultsKeys {
            UserDefaults.standard.removeObject(forKey: key)
            print("   Removed UserDefaults key: \(key)")
        }
        
        // Clear Keychain subscription items (if any)
        // Note: This is a simplified approach. In a real app, you'd want to use a proper Keychain wrapper.
        let keychainKeys = [
            "subscription_token",
            "subscription_receipt"
        ]
        
        for key in keychainKeys {
            // This is a simplified Keychain removal - you might need to adapt this to your actual Keychain implementation
            let query: [String: Any] = [
                kSecClass as String: kSecClassGenericPassword,
                kSecAttrService as String: "com.finpin.app",
                kSecAttrAccount as String: key
            ]
            
            let status = SecItemDelete(query as CFDictionary)
            if status == errSecSuccess {
                print("   Removed Keychain item: \(key)")
            } else if status == errSecItemNotFound {
                print("   Keychain item not found: \(key)")
            } else {
                print("   Failed to remove Keychain item \(key): \(status)")
            }
        }
        
        // Clear any local file caches
        let fileManager = FileManager.default
        let documentsDirectory = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
        
        let cacheFiles = [
            "subscription.plist",
            "subscription_cache.plist"
        ]
        
        for fileName in cacheFiles {
            let fileURL = documentsDirectory.appendingPathComponent(fileName)
            if fileManager.fileExists(atPath: fileURL.path) {
                do {
                    try fileManager.removeItem(at: fileURL)
                    print("   Removed cache file: \(fileName)")
                } catch {
                    print("   Failed to remove cache file \(fileName): \(error)")
                }
            }
        }
        
        print("✅ All subscription caches cleared!")
        
        // Reset in-memory state
        self.isSubscribed = false
        self.isTrialActive = false
        self.subscriptionExpiryDate = nil
        self.expenseCount = 0
        self.subscriptionStatus = .notSubscribed
        
        print("🔄 In-memory subscription state reset to defaults")
        
        // Notify UI that subscription status has changed
        NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
    }

    private func checkStoreKitAvailability() {
        print("🔍 Checking StoreKit availability...")
        print("   Bundle ID: \(Bundle.main.bundleIdentifier ?? "Unknown")")
        print("   App Store Receipt URL: \(Bundle.main.appStoreReceiptURL?.absoluteString ?? "None")")

        #if targetEnvironment(simulator)
        print("   Running in Simulator - StoreKit Testing should be enabled")
        #else
        print("   Running on Device")
        #endif
    }

    deinit {
        updateListenerTask?.cancel()
    }

    // MARK: - Timeout Helper
    private func withTimeout<T>(seconds: TimeInterval, operation: @escaping () async throws -> T) async throws -> T {
        return try await withThrowingTaskGroup(of: T.self) { group in
            group.addTask {
                try await operation()
            }

            group.addTask {
                try await Task.sleep(nanoseconds: UInt64(seconds * 1_000_000_000))
                throw TimeoutError()
            }

            guard let result = try await group.next() else {
                throw TimeoutError()
            }

            group.cancelAll()
            return result
        }
    }

    // MARK: - Transaction Listener
    private func listenForTransactions() -> Task<Void, Error> {
        return Task.detached {
            // Iterate through any transactions that don't come from a direct call to `purchase()`.
            for await result in Transaction.updates {
                do {
                    let transaction = try await self.checkVerified(result)

                    // Deliver products to the user.
                    await self.updateCustomerProductStatus()

                    // Always finish a transaction.
                    await transaction.finish()
                } catch {
                    // StoreKit has a transaction that fails verification. Don't deliver content to the user.
                    print("Transaction failed verification")
                }
            }
        }
    }

    func loadProducts() async {
        isLoading = true
        defer { isLoading = false }

        print("🔍 Loading products for IDs: \(possibleProductIDs)")
        print("🔍 StoreKit Environment Check:")
        print("   Bundle ID: \(Bundle.main.bundleIdentifier ?? "Unknown")")
        print("   App Store Receipt URL: \(Bundle.main.appStoreReceiptURL?.absoluteString ?? "None")")

        // Check environment type
        #if targetEnvironment(simulator)
        print("   Running in Simulator - StoreKit Testing should be enabled")
        #else
        print("   Running on Device")
        #endif
        
        // Check if it's TestFlight or App Store version
        if let receiptURL = Bundle.main.appStoreReceiptURL {
            if receiptURL.lastPathComponent == "sandboxReceipt" {
                print("   Environment: App Store/TestFlight (Sandbox)")
            } else {
                print("   Environment: Development/Simulator")
            }
        }

        do {
            // Add timeout to the product loading
            let products = try await withTimeout(seconds: 10) {
                try await Product.products(for: self.possibleProductIDs)
            }

            self.products = products
            print("✅ Loaded \(products.count) products")

            if products.isEmpty {
                print("⚠️ No products found! This usually means:")
                print("   1. StoreKit Configuration file is not set in scheme")
                print("   2. Product ID 'p1' doesn't exist in premium-1.storekit")
                print("   3. StoreKit Testing is not enabled")
                print("   4. App Store Connect product configuration issues")
                print("   Tried product IDs: \(possibleProductIDs)")
                
                // Enhanced error message with environment-specific guidance
                #if targetEnvironment(simulator)
                purchaseError = "No subscription products available in Simulator. Please check StoreKit configuration in Xcode scheme settings."
                #else
                if let receiptURL = Bundle.main.appStoreReceiptURL, receiptURL.lastPathComponent == "sandboxReceipt" {
                    purchaseError = "No subscription products available. This may be a TestFlight configuration issue. Try 'Restore Purchases' or contact support."
                } else {
                    purchaseError = "No subscription products available. Please check StoreKit configuration in Xcode scheme settings."
                }
                #endif
            } else {
                for product in products {
                    print("📦 Product: \(product.id)")
                    print("   Display Name: \(product.displayName)")
                    print("   Price: \(product.displayPrice)")
                    print("   Type: \(product.type)")

                    // Set the active product ID for the first found product
                    if activeProductID == nil {
                        activeProductID = product.id
                        print("🎯 Set active product ID: \(product.id)")
                    }
                }
                // Clear any previous error
                purchaseError = nil
            }
        } catch is TimeoutError {
            print("⏰ Product loading timed out after 10 seconds")
            print("   This usually indicates StoreKit configuration issues")
            
            // Enhanced timeout error message
            #if targetEnvironment(simulator)
            purchaseError = "Product loading timed out in Simulator. Please check your network connection and ensure StoreKit Testing is properly configured."
            #else
            if let receiptURL = Bundle.main.appStoreReceiptURL, receiptURL.lastPathComponent == "sandboxReceipt" {
                purchaseError = "Product loading timed out in TestFlight. Please check your network connection. If issue persists, try 'Restore Purchases'."
            } else {
                purchaseError = "Product loading timed out. Please check your network connection and StoreKit configuration."
            }
            #endif
        } catch {
            print("❌ Failed to load products: \(error)")
            print("   Error type: \(type(of: error))")
            print("   Error details: \(error.localizedDescription)")

            if let storeKitError = error as? StoreKitError {
                print("   StoreKit Error: \(storeKitError)")
            }

            // Enhanced error message with environment-specific guidance
            #if targetEnvironment(simulator)
            purchaseError = "Failed to load subscription options in Simulator: \(error.localizedDescription). Please check StoreKit configuration."
            #else
            if let receiptURL = Bundle.main.appStoreReceiptURL, receiptURL.lastPathComponent == "sandboxReceipt" {
                purchaseError = "Failed to load subscription options in TestFlight: \(error.localizedDescription). Try 'Restore Purchases' or check network connection."
            } else {
                purchaseError = "Failed to load subscription options: \(error.localizedDescription)"
            }
            #endif
        }
    }

    func purchase() async {
        guard let product = products.first else {
            purchaseError = "Product not available"
            return
        }

        isLoading = true
        purchaseError = nil

        defer { isLoading = false }

        do {
            let result = try await product.purchase()

            switch result {
            case .success(let verification):
                let transaction = try checkVerified(verification)
                await updateCustomerProductStatus()
                await transaction.finish()
                print("✅ Purchase successful")

            case .userCancelled:
                print("🚫 Purchase cancelled by user")

            case .pending:
                print("⏳ Purchase pending approval")
                purchaseError = "Purchase is pending approval"

            @unknown default:
                print("❓ Unknown purchase result")
                purchaseError = "Unknown purchase result"
            }
        } catch StoreKitError.userCancelled {
            // User cancelled, no error needed
            print("🚫 Purchase cancelled")
        } catch StoreKitError.notAvailableInStorefront {
            purchaseError = "This subscription is not available in your region"
        } catch StoreKitError.networkError {
            purchaseError = "Network error. Please check your connection and try again"

            // Provide helpful guidance with retry suggestion
            NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                message: "Network error. Please check your connection and try again. If the problem persists, try 'Restore Purchases'.",
                type: .error
            ))
        } catch {
            print("❌ Purchase failed: \(error)")
            purchaseError = "Purchase failed: \(error.localizedDescription)"
        }
    }

    func restorePurchases() async {
        isLoading = true
        purchaseError = nil

        defer { isLoading = false }

        do {
            print("🔄 Attempting to restore purchases...")
            try await AppStore.sync()
            await updateCustomerProductStatus()
            print("✅ Purchases restored successfully")

            // Check if we actually have an active subscription after restore
            if hasActiveSubscription {
                NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                    message: "✅ Premium subscription restored successfully!",
                    type: .success
                ))
            } else {
                NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                    message: "Restore completed, but no active subscription found. If you believe this is an error, please contact support.",
                    type: .info
                ))
            }
        } catch StoreKitError.userCancelled {
            print("🚫 Restore cancelled")
            purchaseError = "Restore cancelled"

            // User feedback
            NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                message: "Restore cancelled",
                type: .info
            ))
        } catch StoreKitError.networkError {
            purchaseError = "Network error. Please check your connection and try again"

            // Provide more helpful guidance
            NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                message: "Network error. Please check your connection and try again",
                type: .error
            ))
        } catch {
            print("❌ Restore failed: \(error)")
            purchaseError = "Failed to restore purchases: \(error.localizedDescription)"

            // Even if there's an error, check subscription status as it might have worked
            await updateCustomerProductStatus()

            // Provide more specific feedback based on subscription status
            if hasActiveSubscription {
                NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                    message: "✅ Premium subscription found and restored!",
                    type: .success
                ))
                // Clear the error since we actually succeeded
                purchaseError = nil
            } else {
                NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                    message: "Restore attempt completed, but no active subscription found. If you have an active subscription, please try again or contact support.",
                    type: .info
                ))
            }
        }
    }

    // MARK: - Verification Helper
    private func checkVerified<T>(_ result: VerificationResult<T>) throws -> T {
        // Check whether the JWS passes StoreKit verification.
        switch result {
        case .unverified:
            // StoreKit parses the JWS, but it fails verification.
            throw NSError(domain: "StoreKitError", code: 1001, userInfo: [NSLocalizedDescriptionKey: "Transaction failed verification"])
        case .verified(let safe):
            // The result is verified. Return the unwrapped value.
            return safe
        }
    }

    func checkSubscriptionStatus() async {
        await updateCustomerProductStatus()
    }

    @MainActor
    private func updateCustomerProductStatus() async {
        var isCurrentlySubscribed = false
        var isCurrentlyInTrial = false
        var expiryDate: Date?

        for await result in Transaction.currentEntitlements {
            do {
                let transaction = try checkVerified(result)

                if possibleProductIDs.contains(transaction.productID) {
                    isCurrentlySubscribed = true
                    expiryDate = transaction.expirationDate

                    // Check if it's a trial or introductory offer
                    if let offerType = transaction.offerType {
                        switch offerType {
                        case .introductory:
                            isCurrentlyInTrial = true
                        default:
                            break
                        }
                    }

                    // Check if subscription is in grace period
                    if let expirationDate = transaction.expirationDate,
                       expirationDate < Date() {
                        // Subscription has expired
                        isCurrentlySubscribed = false

                        // Check if in grace period (within 16 days of expiration)
                        let gracePeriodEnd = expirationDate.addingTimeInterval(16 * 24 * 60 * 60)
                        if Date() <= gracePeriodEnd {
                            self.subscriptionStatus = .gracePeriod
                        } else {
                            self.subscriptionStatus = .expired
                        }
                    }
                }
            } catch {
                print("❌ Failed to verify transaction: \(error)")
            }
        }

        self.isSubscribed = isCurrentlySubscribed
        self.isTrialActive = isCurrentlyInTrial
        self.subscriptionExpiryDate = expiryDate

        if isCurrentlySubscribed {
            self.subscriptionStatus = isCurrentlyInTrial ? .trial : .subscribed
        } else if subscriptionStatus != .gracePeriod && subscriptionStatus != .expired {
            self.subscriptionStatus = .notSubscribed
        }

        // Save subscription status to UserDefaults
        UserDefaults.standard.set(isCurrentlySubscribed, forKey: "IsSubscribed")
        UserDefaults.standard.set(isCurrentlyInTrial, forKey: "IsTrialActive")

        if let expiryDate = expiryDate {
            UserDefaults.standard.set(expiryDate, forKey: "SubscriptionExpiryDate")
        }

        print("📊 Subscription Status: \(subscriptionStatus), Subscribed: \(isSubscribed), Trial: \(isTrialActive)")
        
        // Notify UI that subscription status has changed
        NotificationCenter.default.post(name: .subscriptionStatusChanged, object: nil)
    }

    var hasActiveSubscription: Bool {
        return isSubscribed || isTrialActive || subscriptionStatus == .gracePeriod
    }

    var subscriptionPrice: String {
        guard let product = products.first else { return "$3.99" }
        return product.displayPrice
    }

    var hasFreeTrial: Bool {
        return false // No free trial for this app
    }

    var subscriptionStatusText: String {
        switch subscriptionStatus {
        case .notSubscribed:
            return "Not Subscribed"
        case .trial:
            return "Free Trial Active"
        case .subscribed:
            return "Premium Active"
        case .expired:
            return "Subscription Expired"
        case .gracePeriod:
            return "Grace Period"
        }
    }

    var subscriptionExpiryText: String? {
        guard let expiryDate = subscriptionExpiryDate else { return nil }

        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .none

        return "Expires: \(formatter.string(from: expiryDate))"
    }

    // MARK: - Premium Feature Checks
    func canUseFeature(_ feature: PremiumFeature) -> Bool {
        // All features are now available in free version
        return true
    }

    enum PremiumFeature {
        case basicTracking
        case csvExport
        case csvImport
        case iCloudSync
        case advancedAnalytics
    }
    
    // MARK: - Expense Limit Check
    func canAddNewExpense() -> Bool {
        // Subscribed users can always add expenses
        if hasActiveSubscription {
            return true
        }
        
        // Free users can add expenses up to the limit
        return expenseCount < expenseLimit
    }
    
    func incrementExpenseCount() {
        expenseCount += 1
        print("📊 Expense count incremented to: \(expenseCount)")
    }

    func decrementExpenseCount() {
        if expenseCount > 0 {
            expenseCount -= 1
            print("📊 Expense count decremented to: \(expenseCount)")
        }
    }

    func recalculateExpenseCount(totalExpenses: Int) {
        // Recalculate expense count based on actual expenses for free users
        if !hasActiveSubscription {
            expenseCount = totalExpenses
            print("📊 Expense count recalculated to: \(expenseCount)")
        }
    }
}

// MARK: - Timeout Error
struct TimeoutError: Error {
    let localizedDescription = "Operation timed out"
}

// MARK: - CloudKit Manager
@MainActor
class CloudKitManager: ObservableObject {
    static let shared = CloudKitManager()

    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var syncError: String?
    @Published var isCloudKitAvailable = false

    private let container = CKContainer(identifier: "iCloud.com.finpin.app")
    private let database: CKDatabase

    private init() {
        self.database = container.privateCloudDatabase
        checkCloudKitAvailability()
        print("🔧 CloudKit initialized with container: \(container.containerIdentifier ?? "unknown")")
        print("🔧 Using database: private")
    }

    // MARK: - RawText Storage Validation
    func validateRawTextStorage() async {
        print("🔍 Validating RawText storage integrity...")

        do {
            let expenses = try await fetchExpenses()
            var missingRawTextCount = 0

            for expense in expenses {
                if expense.rawText == nil || expense.rawText?.isEmpty == true {
                    print("⚠️ Found expense without rawText: \(expense.id)")
                    missingRawTextCount += 1
                }
            }

            if missingRawTextCount > 0 {
                print("🔧 Found \(missingRawTextCount) expenses missing rawText - will be addressed in migration")
            } else {
                print("✅ All \(expenses.count) expenses have rawText stored properly")
            }
        } catch {
            print("❌ Failed to validate rawText storage: \(error)")
        }
    }

    func checkCloudKitAvailability() {
        print("🔍 Checking CloudKit availability for container: \(container.containerIdentifier ?? "unknown")")

        container.accountStatus { [weak self] status, error in
            DispatchQueue.main.async {
                print("🔍 CloudKit account status: \(status.rawValue)")

                switch status {
                case .available:
                    self?.isCloudKitAvailable = true
                    self?.syncError = nil
                    print("✅ CloudKit is available")
                case .noAccount:
                    self?.isCloudKitAvailable = false
                    self?.syncError = "Please sign in to iCloud in Settings to enable sync"
                    print("❌ No iCloud account")
                case .restricted:
                    self?.isCloudKitAvailable = false
                    self?.syncError = "iCloud access is restricted on this device"
                    print("❌ iCloud access restricted")
                case .couldNotDetermine:
                    self?.isCloudKitAvailable = false
                    self?.syncError = "Unable to determine iCloud status"
                    print("❌ Could not determine iCloud status")
                case .temporarilyUnavailable:
                    self?.isCloudKitAvailable = false
                    self?.syncError = "iCloud is temporarily unavailable"
                    print("❌ iCloud temporarily unavailable")
                @unknown default:
                    self?.isCloudKitAvailable = false
                    self?.syncError = "Unknown iCloud status"
                    print("❌ Unknown iCloud status")
                }

                if let error = error {
                    self?.syncError = "iCloud error: \(error.localizedDescription)"
                    print("❌ CloudKit error: \(error)")
                }
            }
        }
    }

    func syncExpenses(_ expenses: [ExpenseRecord]) async throws {
        guard isCloudKitAvailable else {
            throw CloudKitError.notAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        do {
            // Convert expenses to CloudKit records
            let records = expenses.map { expense in
                let record = CKRecord(recordType: "ExpenseRecord", recordID: CKRecord.ID(recordName: expense.id.uuidString))
                
                // Ensure consistent type storage for amount field
                let amountDecimal = NSDecimalNumber(decimal: expense.amount)
                record["amount"] = amountDecimal
                
                record["currency"] = expense.currency
                record["merchant"] = expense.merchant
                record["date"] = expense.date
                record["transactionDate"] = expense.transactionDate
                record["originalTimestamp"] = expense.originalTimestamp
                record["latitude"] = expense.latitude
                record["longitude"] = expense.longitude
                record["locationName"] = expense.locationName
                record["tags"] = expense.tags
                record["paymentMethod"] = expense.paymentMethod
                record["paymentCard"] = expense.paymentCard
                record["notes"] = expense.notes
                record["rawText"] = expense.rawText
                
                // Handle convertedAmount with consistent typing
                if let convertedAmount = expense.convertedAmount {
                    record["convertedAmount"] = NSDecimalNumber(decimal: convertedAmount)
                } else {
                    record["convertedAmount"] = nil
                }
                
                record["baseCurrency"] = expense.baseCurrency
                record["exchangeRate"] = expense.exchangeRate
                return record
            }

            print("🚀 Starting CloudKit sync for \(records.count) expense records")
            
            // Save records in batches
            let batchSize = 100
            for i in stride(from: 0, to: records.count, by: batchSize) {
                let endIndex = min(i + batchSize, records.count)
                let batch = Array(records[i..<endIndex])

                let operation = CKModifyRecordsOperation(recordsToSave: batch, recordIDsToDelete: nil)
                operation.savePolicy = .changedKeys
                operation.qualityOfService = .userInitiated

                try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                    operation.modifyRecordsResultBlock = { result in
                        switch result {
                        case .success:
                            print("✅ Successfully saved \(batch.count) records to CloudKit")
                            for (index, record) in batch.enumerated() {
                                print("📝 Saved record \(i + index + 1): \(record.recordID.recordName)")
                            }
                            continuation.resume()
                        case .failure(let error):
                            print("❌ Failed to save records to CloudKit: \(error)")
                            if let ckError = error as? CKError {
                                print("🔍 CKError code: \(ckError.code.rawValue)")
                                print("🔍 CKError message: \(ckError.localizedDescription)")
                                
                                // 检查部分失败的情况
                                if ckError.code == .partialFailure {
                                    if let partialErrors = ckError.userInfo[CKPartialErrorsByItemIDKey] as? [CKRecord.ID: Error] {
                                        for (recordID, error) in partialErrors {
                                            print("❌ Failed to save record \(recordID.recordName): \(error)")
                                        }
                                    }
                                }
                            }
                            continuation.resume(throwing: error)
                        }
                    }
                    database.add(operation)
                }
            }

            lastSyncDate = Date()
            syncError = nil

        } catch {
            let errorMessage = handleCloudKitError(error)
            syncError = errorMessage
            throw CloudKitError.syncFailed(errorMessage)
        }
    }

    func fetchExpenses() async throws -> [ExpenseRecord] {
        guard isCloudKitAvailable else {
            throw CloudKitError.notAvailable
        }

        isSyncing = true
        defer { isSyncing = false }

        do {
            let query = CKQuery(recordType: "ExpenseRecord", predicate: NSPredicate(value: true))
            query.sortDescriptors = [NSSortDescriptor(key: "transactionDate", ascending: false)]

            let operation = CKQueryOperation(query: query)
            operation.qualityOfService = .userInitiated

            var fetchedRecords: [CKRecord] = []

            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                operation.recordMatchedBlock = { recordID, result in
                    switch result {
                    case .success(let record):
                        fetchedRecords.append(record)
                    case .failure(let error):
                        print("Failed to fetch record \(recordID): \(error)")
                    }
                }

                operation.queryResultBlock = { result in
                    switch result {
                    case .success(let cursor):
                        if cursor != nil {
                            print("Query has more results - cursor available")
                        }
                        continuation.resume()
                    case .failure(let error):
                        continuation.resume(throwing: error)
                    }
                }

                database.add(operation)
            }

            print("🔍 Found \(fetchedRecords.count) records in CloudKit")
            for (index, record) in fetchedRecords.enumerated() {
                print("📝 Record \(index + 1): \(record.recordID.recordName)")
            }

            // Convert CloudKit records to ExpenseRecord objects
            print("🔄 Converting \(fetchedRecords.count) CloudKit records to ExpenseRecord objects")
            
            let expenses = fetchedRecords.compactMap { record -> ExpenseRecord? in
                print("🔄 Processing record: \(record.recordID.recordName)")
                
                // Handle amount field type conversion (Double -> NSDecimalNumber)
                let amount: NSDecimalNumber
                if let decimalAmount = record["amount"] as? NSDecimalNumber {
                    amount = decimalAmount
                } else if let doubleAmount = record["amount"] as? Double {
                    amount = NSDecimalNumber(value: doubleAmount)
                } else if let intAmount = record["amount"] as? Int {
                    amount = NSDecimalNumber(value: intAmount)
                } else {
                    print("❌ Failed to convert record \(record.recordID.recordName): amount field missing or wrong type")
                    print("   amount: \(record["amount"] ?? "nil")")
                    print("   amount type: \(type(of: record["amount"]))")
                    return nil
                }
                
                guard let currency = record["currency"] as? String,
                      let date = record["date"] as? Date else {
                    print("❌ Failed to convert record \(record.recordID.recordName): missing required fields")
                    print("   amount: \(amount) (converted successfully)")
                    print("   currency: \(record["currency"] ?? "nil")")
                    print("   date: \(record["date"] ?? "nil")")
                    return nil
                }

                // Handle convertedAmount field similarly
                let convertedAmount: Decimal?
                if let decimalConverted = record["convertedAmount"] as? NSDecimalNumber {
                    convertedAmount = decimalConverted as Decimal
                } else if let doubleConverted = record["convertedAmount"] as? Double {
                    convertedAmount = Decimal(doubleConverted)
                } else {
                    convertedAmount = nil
                }

                let expense = ExpenseRecord(
                    id: UUID(uuidString: record.recordID.recordName) ?? UUID(),
                    amount: amount as Decimal,
                    currency: currency,
                    merchant: record["merchant"] as? String,
                    date: date,
                    transactionDate: record["transactionDate"] as? Date,
                    originalTimestamp: record["originalTimestamp"] as? String,
                    latitude: record["latitude"] as? Double ?? 0.0,
                    longitude: record["longitude"] as? Double ?? 0.0,
                    locationName: record["locationName"] as? String,
                    tags: record["tags"] as? [String] ?? [],
                    paymentMethod: record["paymentMethod"] as? String,
                    paymentCard: record["paymentCard"] as? String,
                    notes: record["notes"] as? String,
                    rawText: record["rawText"] as? String,
                    confidence: .manual,
                    exchangeRate: record["exchangeRate"] as? Double,
                    baseCurrency: record["baseCurrency"] as? String,
                    convertedAmount: convertedAmount
                )
                
                print("✅ Successfully converted record to expense: \(expense.merchant ?? "Unknown") - \(expense.amount) \(expense.currency)")
                return expense
            }
            
            print("🔄 Conversion complete: \(expenses.count) expenses created from \(fetchedRecords.count) records")

            lastSyncDate = Date()
            syncError = nil
            return expenses

        } catch {
            let errorMessage = handleCloudKitError(error)
            syncError = errorMessage
            throw CloudKitError.syncFailed(errorMessage)
        }
    }

    func deleteExpense(_ expense: ExpenseRecord) async throws {
        guard isCloudKitAvailable else {
            throw CloudKitError.notAvailable
        }
        
        let recordID = CKRecord.ID(recordName: expense.id.uuidString)
        let operation = CKModifyRecordsOperation(recordsToSave: nil, recordIDsToDelete: [recordID])
        operation.savePolicy = .changedKeys
        operation.qualityOfService = .userInitiated
        
        try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
            operation.modifyRecordsResultBlock = { result in
                switch result {
                case .success:
                    print("✅ Successfully deleted expense from CloudKit: \(expense.id)")
                    continuation.resume()
                case .failure(let error):
                    print("❌ Failed to delete expense from CloudKit: \(error)")
                    continuation.resume(throwing: error)
                }
            }
            database.add(operation)
        }
    }

    func deleteAllExpenses(_ expenseIDs: [UUID]) async throws {
        guard isCloudKitAvailable else {
            throw CloudKitError.notAvailable
        }
        
        let recordIDs = expenseIDs.map { CKRecord.ID(recordName: $0.uuidString) }
        
        // Delete in batches to avoid hitting limits
        let batchSize = 100
        for i in stride(from: 0, to: recordIDs.count, by: batchSize) {
            let endIndex = min(i + batchSize, recordIDs.count)
            let batch = Array(recordIDs[i..<endIndex])
            
            let operation = CKModifyRecordsOperation(recordsToSave: nil, recordIDsToDelete: batch)
            operation.savePolicy = .changedKeys
            operation.qualityOfService = .userInitiated
            
            try await withCheckedThrowingContinuation { (continuation: CheckedContinuation<Void, Error>) in
                operation.modifyRecordsResultBlock = { result in
                    switch result {
                    case .success:
                        print("✅ Successfully deleted \(batch.count) expenses from CloudKit")
                        continuation.resume()
                    case .failure(let error):
                        print("❌ Failed to delete expenses from CloudKit: \(error)")
                        continuation.resume(throwing: error)
                    }
                }
                database.add(operation)
            }
        }
    }

    enum CloudKitError: LocalizedError {
        case notAvailable
        case syncFailed(String)

        var errorDescription: String? {
            switch self {
            case .notAvailable:
                return "iCloud is not available. Please check your iCloud settings."
            case .syncFailed(let message):
                return message
            }
        }
    }

    private func handleCloudKitError(_ error: Error) -> String {
        if let ckError = error as? CKError {
            switch ckError.code {
            case .notAuthenticated:
                return "Please sign in to iCloud in Settings to enable sync"
            case .networkUnavailable, .networkFailure:
                return "Network connection required for iCloud sync"
            case .quotaExceeded:
                return "iCloud storage quota exceeded"
            case .zoneBusy, .serviceUnavailable:
                return "iCloud service temporarily unavailable. Please try again later"
            case .requestRateLimited:
                return "Too many sync requests. Please wait and try again"
            case .partialFailure:
                return "Some data failed to sync. Please try again"
            default:
                return "iCloud sync error: \(ckError.localizedDescription)"
            }
        }
        return "Sync error: \(error.localizedDescription)"
    }
}

// MARK: - Exchange Rate Models
struct ExchangeRateResponse: Codable {
    let rates: [String: Double]
    let base: String
    let date: String
}

struct ExchangeRate: Codable {
    let fromCurrency: String
    let toCurrency: String
    let rate: Double
    let lastUpdated: Date

    var isExpired: Bool {
        Date().timeIntervalSince(lastUpdated) > 3600 // 1 hour
    }
}

// MARK: - Exchange Rate Service
class ExchangeRateService: ObservableObject {
    static let shared = ExchangeRateService()

    @Published var baseCurrency: String = "USD"
    @Published var exchangeRates: [String: Double] = [:]
    @Published var lastUpdated: Date?
    @Published var isLoading = false

    private let userDefaults = UserDefaults.standard
    private let baseCurrencyKey = "BaseCurrency"
    private let exchangeRatesKey = "ExchangeRates"
    private let lastUpdatedKey = "ExchangeRatesLastUpdated"

    // Use Cloudflare service as backend
    private let cloudflareService = CloudflareExchangeRateService.shared

    init() {
        loadSettings()
        loadCachedRates()

        // Auto-detect currency based on locale if not set
        if baseCurrency == "USD" && !userDefaults.bool(forKey: "BaseCurrencyManuallySet") {
            detectLocalCurrency()
        }
    }

    private func detectLocalCurrency() {
        let locale = Locale.current
        if let currencyCode = locale.currency?.identifier,
           CurrencyHelper.isSupported(currencyCode) {
            baseCurrency = currencyCode
            saveSettings()
        }
    }

    func setBaseCurrency(_ currency: String) {
        baseCurrency = currency
        userDefaults.set(true, forKey: "BaseCurrencyManuallySet")
        saveSettings()
        fetchExchangeRates()
    }

    func fetchExchangeRates() {
        guard !isLoading else { return }

        isLoading = true

        // Use Cloudflare service for exchange rates
        Task { @MainActor in
            await cloudflareService.fetchRatesForCurrency(baseCurrency)

            // Update local rates from Cloudflare service
            if let rates = cloudflareService.cachedRates[baseCurrency] {
                self.exchangeRates = rates
                self.lastUpdated = cloudflareService.lastFetchTime[baseCurrency]
                self.saveRates()
                print("✅ Fetched \(rates.count) rates for \(self.baseCurrency) from Cloudflare")
            } else {
                print("❌ Failed to fetch rates from Cloudflare service")
                // 如果当前没有任何汇率数据，使用fallback
                if self.exchangeRates.isEmpty {
                    print("🔄 No rates available, loading fallback rates...")
                    self.loadFallbackRates()
                }
            }

            self.isLoading = false
        }
    }



    private func loadFallbackRates() {
        print("⚠️ Loading emergency fallback rates to ensure basic functionality")

        // 紧急fallback汇率，确保基本功能可用
        // 这些是近似汇率，仅用于应急情况
        self.exchangeRates = [
            "USD": 1.0,
            "EUR": 0.85,
            "GBP": 0.73,
            "JPY": 110.0,
            "CNY": 7.2,
            "CAD": 1.25,
            "AUD": 1.35,
            "CHF": 0.92,
            "KRW": 1200.0,
            "SGD": 1.35,
            "ISK": 130.0
        ]
        self.lastUpdated = Date()
        self.saveRates()
        print("✅ Emergency fallback rates loaded - \(exchangeRates.count) currencies available")
    }

    func convertAmount(_ amount: Decimal, from fromCurrency: String, to toCurrency: String) -> Decimal? {
        guard fromCurrency != toCurrency else { return amount }

        // 禁止使用硬编码汇率！只有在有API数据时才进行转换
        guard let lastUpdated = lastUpdated,
              Date().timeIntervalSince(lastUpdated) < 86400 * 7 else { // 7天内的数据才有效
            print("⚠️ ExchangeRateService: No recent API data available, refusing to convert")
            return nil
        }

        // exchangeRates格式: 1 baseCurrency = X otherCurrency
        // 例如: 1 USD = 7.2 CNY, 1 USD = 0.85 EUR

        // If converting to base currency
        if toCurrency == baseCurrency {
            if let rate = exchangeRates[fromCurrency] {
                // 从其他货币转换到基础货币: amount / rate
                // 例如: 7.2 CNY -> USD: 7.2 / 7.2 = 1 USD
                print("✅ ExchangeRateService API conversion: \(amount) \(fromCurrency) ÷ \(rate) = \(amount / Decimal(rate)) \(toCurrency)")
                return amount / Decimal(rate)
            }
        }
        // If converting from base currency
        else if fromCurrency == baseCurrency {
            if let rate = exchangeRates[toCurrency] {
                // 从基础货币转换到其他货币: amount * rate
                // 例如: 1 USD -> CNY: 1 * 7.2 = 7.2 CNY
                print("✅ ExchangeRateService API conversion: \(amount) \(fromCurrency) × \(rate) = \(amount * Decimal(rate)) \(toCurrency)")
                return amount * Decimal(rate)
            }
        }
        // Cross-currency conversion
        else {
            if let fromRate = exchangeRates[fromCurrency],
               let toRate = exchangeRates[toCurrency] {
                // 先转换到基础货币，再转换到目标货币
                let baseAmount = amount / Decimal(fromRate)
                let result = baseAmount * Decimal(toRate)
                print("✅ ExchangeRateService API cross conversion: \(amount) \(fromCurrency) → \(result) \(toCurrency)")
                return result
            }
        }

        print("⚠️ ExchangeRateService: No API rate available for \(fromCurrency) to \(toCurrency)")
        return nil
    }

    func getFormattedConvertedAmount(_ amount: Decimal, from fromCurrency: String) -> String? {
        guard let convertedAmount = convertAmount(amount, from: fromCurrency, to: baseCurrency) else {
            return nil
        }

        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = baseCurrency
        formatter.maximumFractionDigits = 2

        return formatter.string(from: NSDecimalNumber(decimal: convertedAmount))
    }

    private func saveSettings() {
        userDefaults.set(baseCurrency, forKey: baseCurrencyKey)
    }

    private func loadSettings() {
        baseCurrency = userDefaults.string(forKey: baseCurrencyKey) ?? "USD"
    }

    private func saveRates() {
        if let encoded = try? JSONEncoder().encode(exchangeRates) {
            userDefaults.set(encoded, forKey: exchangeRatesKey)
        }
        if let lastUpdated = lastUpdated {
            userDefaults.set(lastUpdated, forKey: lastUpdatedKey)
        }
    }

    private func loadCachedRates() {
        if let data = userDefaults.data(forKey: exchangeRatesKey),
           let rates = try? JSONDecoder().decode([String: Double].self, from: data) {
            exchangeRates = rates
        }

        lastUpdated = userDefaults.object(forKey: lastUpdatedKey) as? Date

        // 如果没有任何汇率数据，立即加载fallback汇率确保功能可用
        if exchangeRates.isEmpty {
            print("⚠️ No cached rates found, loading fallback rates immediately")
            loadFallbackRates()
        }

        // Fetch new rates if cache is expired or empty
        if lastUpdated == nil || Date().timeIntervalSince(lastUpdated!) > 3600 {
            print("🔄 Cache expired, attempting to fetch fresh rates...")
            fetchExchangeRates()
        }
    }
}

// MARK: - Cloudflare Exchange Rate Service
class CloudflareExchangeRateService: ObservableObject {
    static let shared = CloudflareExchangeRateService()

    private let baseURL = "https://exchange-rates.finpin.app"
    @Published var isLoading = false
    @Published var lastError: String?

    var cachedRates: [String: [String: Double]] = [:]
    var lastFetchTime: [String: Date] = [:]
    private let cacheTimeout: TimeInterval = 86400 // 24 hours - 更高效的缓存策略

    // 预加载主流货币汇率缓存
    private var majorCurrenciesCache: [String: [String: Double]] = [:]
    private var majorCurrenciesLastFetch: Date?

    private init() {
        // 启动时预加载主流货币汇率
        preloadMajorCurrencies()
    }

    // MARK: - Public Methods

    /// Get exchange rate from one currency to another
    func getRate(from: String, to: String) -> Double? {
        // If same currency, return 1
        if from == to {
            return 1.0
        }

        // Strategy 1: 优先使用预加载缓存（最快）
        if let rate = getRateFromCache(from: from, to: to) {
            return rate
        }

        // Strategy 2: Use USD as intermediate currency
        // Convert from -> USD -> to
        if let usdRates = cachedRates["USD"],
           let lastFetch = lastFetchTime["USD"],
           Date().timeIntervalSince(lastFetch) < cacheTimeout {

            if from == "USD" {
                // USD to target currency
                return usdRates[to]
            } else if to == "USD" {
                // Source currency to USD (inverse rate)
                if let fromToUsdRate = usdRates[from] {
                    return 1.0 / fromToUsdRate
                }
            } else {
                // Source -> USD -> Target
                if let fromToUsdRate = usdRates[from],
                   let usdToTargetRate = usdRates[to] {
                    return usdToTargetRate / fromToUsdRate
                }
            }
        }

        return nil
    }

    /// Convert amount from one currency to another
    func convertAmount(_ amount: Decimal, from: String, to: String) -> Decimal? {
        guard let rate = getRate(from: from, to: to) else {
            return nil
        }
        return amount * Decimal(rate)
    }

    /// Fetch exchange rates only if needed (smart caching)
    @MainActor
    func fetchRatesIfNeeded() async {
        // 如果没有任何汇率数据，强制获取
        if cachedRates.isEmpty {
            print("🔄 No cached rates available, forcing API fetch")
            await fetchRatesForCurrency("USD")
            return
        }

        // Check if we have recent USD rates (most important)
        if let lastFetch = lastFetchTime["USD"],
           Date().timeIntervalSince(lastFetch) < cacheTimeout {
            print("💾 Using cached rates, last fetch: \(lastFetch)")
            return
        }

        // Only fetch USD rates (we can convert to any currency from USD)
        await fetchRatesForCurrency("USD")
    }

    /// Fetch exchange rates for a specific base currency
    @MainActor
    func fetchRatesForCurrency(_ baseCurrency: String) async {
        // Check cache first
        if let lastFetch = lastFetchTime[baseCurrency],
           Date().timeIntervalSince(lastFetch) < cacheTimeout {
            print("💾 Using cached rates for \(baseCurrency)")
            return
        }

        isLoading = true
        lastError = nil

        do {
            let url = URL(string: "\(baseURL)/api/rates?base=\(baseCurrency)")!
            let (data, response) = try await URLSession.shared.data(from: url)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                throw CloudflareExchangeError.invalidResponse
            }

            let ratesResponse = try JSONDecoder().decode(CloudflareRatesResponse.self, from: data)

            if ratesResponse.success {
                cachedRates[baseCurrency] = ratesResponse.rates
                lastFetchTime[baseCurrency] = Date()
                print("✅ Fetched \(ratesResponse.rates.count) rates for \(baseCurrency)")
            } else {
                throw CloudflareExchangeError.apiError(ratesResponse.error ?? "Unknown error")
            }

        } catch {
            lastError = error.localizedDescription
            print("❌ Failed to fetch rates for \(baseCurrency): \(error)")
        }

        isLoading = false
    }

    /// Legacy method for backward compatibility
    @MainActor
    func fetchRates() async {
        await fetchRatesIfNeeded()
    }

    /// Check service status
    @MainActor
    func checkStatus() async -> CloudflareStatusResponse? {
        do {
            let url = URL(string: "\(baseURL)/api/status")!
            let (data, _) = try await URLSession.shared.data(from: url)
            return try JSONDecoder().decode(CloudflareStatusResponse.self, from: data)
        } catch {
            print("❌ Failed to check status: \(error)")
            return nil
        }
    }

    /// Trigger manual sync on server
    @MainActor
    func triggerSync() async -> Bool {
        do {
            let url = URL(string: "\(baseURL)/api/sync")!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                return false
            }

            let syncResponse = try JSONDecoder().decode(CloudflareSyncResponse.self, from: data)
            return syncResponse.success

        } catch {
            print("❌ Failed to trigger sync: \(error)")
            return false
        }
    }

    // MARK: - Major Currencies Preloading

    /// 预加载所有主流货币的汇率，提升性能
    func preloadMajorCurrencies() {
        // 检查是否需要更新缓存
        if let lastFetch = majorCurrenciesLastFetch,
           Date().timeIntervalSince(lastFetch) < cacheTimeout {
            print("💾 Major currencies cache still valid, skipping preload")
            return
        }

        print("🔄 Preloading major currencies exchange rates...")

        // 异步预加载所有主流货币对
        Task {
            await preloadAllMajorCurrencyPairs()
        }
    }

    @MainActor
    private func preloadAllMajorCurrencyPairs() async {
        let majorCurrencies = CurrencyHelper.majorCurrencies
        var newCache: [String: [String: Double]] = [:]

        // 为每种主流货币获取对其他货币的汇率
        for baseCurrency in majorCurrencies {
            if let rates = await fetchRatesForCurrencySync(baseCurrency) {
                newCache[baseCurrency] = rates
                print("✅ Preloaded rates for \(baseCurrency): \(rates.count) currencies")
            } else {
                print("❌ Failed to preload rates for \(baseCurrency)")
            }
        }

        // 更新缓存
        majorCurrenciesCache = newCache
        majorCurrenciesLastFetch = Date()

        print("🎯 Major currencies preload completed: \(newCache.count) base currencies cached")
    }

    private func fetchRatesForCurrencySync(_ baseCurrency: String) async -> [String: Double]? {
        let url = URL(string: "\(baseURL)/rates/\(baseCurrency)")!

        do {
            let (data, _) = try await URLSession.shared.data(from: url)
            let response = try JSONDecoder().decode(CloudflareRatesResponse.self, from: data)

            if response.success {
                return response.rates
            } else {
                print("❌ API error for \(baseCurrency): \(response.error ?? "Unknown error")")
                return nil
            }
        } catch {
            print("❌ Network error for \(baseCurrency): \(error.localizedDescription)")
            return nil
        }
    }

    /// 从预加载缓存中获取汇率，提升性能
    func getRateFromCache(from: String, to: String) -> Double? {
        // 检查预加载缓存
        if let rates = majorCurrenciesCache[from],
           let rate = rates[to] {
            return rate
        }

        // 检查常规缓存
        if let rates = cachedRates[from],
           let rate = rates[to],
           let lastFetch = lastFetchTime[from],
           Date().timeIntervalSince(lastFetch) < cacheTimeout {
            return rate
        }

        return nil
    }
}

// MARK: - Cloudflare Response Models
struct CloudflareRatesResponse: Codable {
    let success: Bool
    let base: String
    let rates: [String: Double]
    let lastSync: String?
    let source: String?
    let timestamp: Int64
    let convertedFrom: String?
    let error: String?
}

struct CloudflareStatusResponse: Codable {
    let success: Bool
    let status: String
    let lastSync: String?
    let cacheStats: [String: String]
    let timestamp: Int64
}

struct CloudflareSyncResponse: Codable {
    let success: Bool
    let message: String
    let base: String?
    let ratesCount: Int?
    let timestamp: Int64
}

enum CloudflareExchangeError: Error, LocalizedError {
    case invalidResponse
    case apiError(String)
    case networkError

    var errorDescription: String? {
        switch self {
        case .invalidResponse:
            return "Invalid response from exchange rate service"
        case .apiError(let message):
            return "API Error: \(message)"
        case .networkError:
            return "Network connection error"
        }
    }
}

// MARK: - Base Currency Manager
class BaseCurrencyManager: ObservableObject {
    static let shared = BaseCurrencyManager()

    @Published var baseCurrency: String {
        didSet {
            UserDefaults.standard.set(baseCurrency, forKey: "BaseCurrency")
            print("🔄 Base currency changed to: \(baseCurrency)")
        }
    }

    private init() {
        self.baseCurrency = UserDefaults.standard.string(forKey: "BaseCurrency") ?? "USD"
    }
}

// MARK: - Currency Helper
struct CurrencyHelper {
    // 主流货币列表（保持当前设计的10个货币）
    static let majorCurrencies: [String] = [
        "USD", "EUR", "GBP", "JPY", "CNY",
        "CAD", "AUD", "CHF", "KRW", "SGD"
    ]
    
    // Legal tender currencies only (excluding precious metals, cryptocurrencies, and special drawing rights)
    static let allSupportedCurrencies = [
        "AED", "AFN", "ALL", "AMD", "ANG", "AOA", "ARS", "AUD", "AWG", "AZN",
        "BAM", "BBD", "BDT", "BGN", "BHD", "BIF", "BMD", "BND", "BOB", "BRL",
        "BSD", "BTN", "BWP", "BYN", "BZD", "CAD", "CDF", "CHF", "CLP", "CNH",
        "CNY", "COP", "CRC", "CUC", "CUP", "CVE", "CZK", "DJF", "DKK", "DOP",
        "DZD", "EGP", "ERN", "ETB", "EUR", "FJD", "FKP", "GBP", "GEL", "GGP",
        "GHS", "GIP", "GMD", "GNF", "GTQ", "GYD", "HKD", "HNL", "HRK", "HTG",
        "HUF", "IDR", "ILS", "IMP", "INR", "IQD", "IRR", "ISK", "JEP", "JMD",
        "JOD", "JPY", "KES", "KGS", "KHR", "KMF", "KPW", "KRW", "KWD", "KYD",
        "KZT", "LAK", "LBP", "LKR", "LRD", "LSL", "LYD", "MAD", "MDL", "MGA",
        "MKD", "MMK", "MNT", "MOP", "MRU", "MUR", "MVR", "MWK", "MXN", "MYR",
        "MZN", "NAD", "NGN", "NIO", "NOK", "NPR", "NZD", "OMR", "PAB", "PEN",
        "PGK", "PHP", "PKR", "PLN", "PYG", "QAR", "RON", "RSD", "RUB", "RWF",
        "SAR", "SBD", "SCR", "SDG", "SEK", "SGD", "SHP", "SLE", "SLL", "SOS",
        "SRD", "SSP", "STN", "SVC", "SYP", "SZL", "THB", "TJS", "TMT", "TND",
        "TOP", "TRY", "TTD", "TWD", "TZS", "UAH", "UGX", "USD", "UYU", "UZS",
        "VES", "VND", "VUV", "WST", "XAF", "XCD", "XOF", "XPF", "YER", "ZAR",
        "ZMW", "ZWL"
    ]

    static let supportedCurrencies: [String: String] = [
        "USD": "$", "EUR": "€", "GBP": "£", "JPY": "¥", "CNY": "¥", "KRW": "₩",
        "CAD": "C$", "AUD": "A$", "CHF": "CHF", "SEK": "kr", "NOK": "kr", "DKK": "kr",
        "PLN": "zł", "CZK": "Kč", "HUF": "Ft", "RUB": "₽", "INR": "₹", "SGD": "S$",
        "HKD": "HK$", "TWD": "NT$", "THB": "฿", "MYR": "RM", "IDR": "Rp", "PHP": "₱",
        "VND": "₫", "BRL": "R$", "MXN": "$", "ARS": "$", "CLP": "$", "COP": "$",
        "PEN": "S/", "ZAR": "R", "EGP": "£", "TRY": "₺", "ILS": "₪", "SAR": "﷼",
        "AED": "د.إ", "QAR": "﷼", "KWD": "د.ك", "BHD": ".د.ب", "OMR": "﷼",
        "NZD": "NZ$"
    ]

    static func symbol(for currencyCode: String) -> String {
        return supportedCurrencies[currencyCode.uppercased()] ?? currencyCode
    }

    static func isSupported(_ currencyCode: String) -> Bool {
        return allSupportedCurrencies.contains(currencyCode.uppercased())
    }
    
    // Get currency name with country/region information
    static func name(for currency: String) -> String {
        let currencyNames: [String: String] = [
            // Major currencies
            "USD": "US Dollar - United States", 
            "EUR": "Euro - European Union", 
            "GBP": "British Pound - United Kingdom", 
            "JPY": "Japanese Yen - Japan", 
            "CNY": "Chinese Yuan - China",
            "CAD": "Canadian Dollar - Canada", 
            "AUD": "Australian Dollar - Australia", 
            "CHF": "Swiss Franc - Switzerland", 
            "KRW": "South Korean Won - South Korea", 
            "SGD": "Singapore Dollar - Singapore",
            
            // Asian currencies
            "HKD": "Hong Kong Dollar - Hong Kong SAR", 
            "TWD": "Taiwan Dollar - Taiwan", 
            "THB": "Thai Baht - Thailand", 
            "MYR": "Malaysian Ringgit - Malaysia", 
            "PHP": "Philippine Peso - Philippines",
            "IDR": "Indonesian Rupiah - Indonesia", 
            "VND": "Vietnamese Dong - Vietnam", 
            "INR": "Indian Rupee - India", 
            "PKR": "Pakistani Rupee - Pakistan", 
            "LKR": "Sri Lankan Rupee - Sri Lanka",
            "NPR": "Nepalese Rupee - Nepal", 
            "BTN": "Bhutanese Ngultrum - Bhutan", 
            "BDT": "Bangladeshi Taka - Bangladesh", 
            "MMK": "Myanmar Kyat - Myanmar", 
            "LAK": "Lao Kip - Laos",
            "KHR": "Cambodian Riel - Cambodia", 
            "MNT": "Mongolian Tugrik - Mongolia", 
            "KZT": "Kazakhstani Tenge - Kazakhstan", 
            "UZS": "Uzbekistani Som - Uzbekistan",
            "KGS": "Kyrgystani Som - Kyrgyzstan", 
            "TJS": "Tajikistani Somoni - Tajikistan", 
            "TMT": "Turkmenistani Manat - Turkmenistan", 
            "AFN": "Afghan Afghani - Afghanistan",
            "IRR": "Iranian Rial - Iran", 
            "IQD": "Iraqi Dinar - Iraq", 
            "JOD": "Jordanian Dinar - Jordan", 
            "KWD": "Kuwaiti Dinar - Kuwait", 
            "BHD": "Bahraini Dinar - Bahrain",
            "QAR": "Qatari Riyal - Qatar", 
            "AED": "UAE Dirham - United Arab Emirates", 
            "SAR": "Saudi Riyal - Saudi Arabia", 
            "OMR": "Omani Rial - Oman", 
            "YER": "Yemeni Rial - Yemen",
            "SYP": "Syrian Pound - Syria", 
            "LBP": "Lebanese Pound - Lebanon", 
            "ILS": "Israeli New Shekel - Israel", 
            "GEL": "Georgian Lari - Georgia", 
            "AMD": "Armenian Dram - Armenia",
            "AZN": "Azerbaijani Manat - Azerbaijan", 
            "MOP": "Macanese Pataca - Macao SAR", 
            "CNH": "Chinese Yuan (Offshore) - China",
            
            // European currencies
            "RUB": "Russian Ruble - Russia", 
            "UAH": "Ukrainian Hryvnia - Ukraine", 
            "BYN": "Belarusian Ruble - Belarus", 
            "PLN": "Polish Zloty - Poland",
            "CZK": "Czech Koruna - Czech Republic", 
            "HUF": "Hungarian Forint - Hungary", 
            "RON": "Romanian Leu - Romania", 
            "BGN": "Bulgarian Lev - Bulgaria",
            "HRK": "Croatian Kuna - Croatia", 
            "RSD": "Serbian Dinar - Serbia", 
            "BAM": "Convertible Mark - Bosnia and Herzegovina", 
            "MKD": "Macedonian Denar - North Macedonia",
            "ALL": "Albanian Lek - Albania", 
            "MDL": "Moldovan Leu - Moldova", 
            "SEK": "Swedish Krona - Sweden", 
            "NOK": "Norwegian Krone - Norway",
            "DKK": "Danish Krone - Denmark", 
            "ISK": "Icelandic Krona - Iceland", 
            "TRY": "Turkish Lira - Turkey",
            
            // African currencies
            "ZAR": "South African Rand - South Africa", 
            "EGP": "Egyptian Pound - Egypt", 
            "NGN": "Nigerian Naira - Nigeria", 
            "KES": "Kenyan Shilling - Kenya", 
            "UGX": "Ugandan Shilling - Uganda",
            "TZS": "Tanzanian Shilling - Tanzania", 
            "RWF": "Rwandan Franc - Rwanda", 
            "ETB": "Ethiopian Birr - Ethiopia", 
            "GHS": "Ghanaian Cedi - Ghana", 
            "XAF": "Central African CFA Franc - Central African Economic and Monetary Union",
            "XOF": "West African CFA Franc - West African Economic and Monetary Union", 
            "MAD": "Moroccan Dirham - Morocco", 
            "TND": "Tunisian Dinar - Tunisia", 
            "DZD": "Algerian Dinar - Algeria", 
            "LYD": "Libyan Dinar - Libya",
            "SDG": "Sudanese Pound - Sudan", 
            "SSP": "South Sudanese Pound - South Sudan", 
            "ERN": "Eritrean Nakfa - Eritrea", 
            "DJF": "Djiboutian Franc - Djibouti", 
            "SOS": "Somali Shilling - Somalia",
            "MUR": "Mauritian Rupee - Mauritius", 
            "SCR": "Seychellois Rupee - Seychelles", 
            "MWK": "Malawian Kwacha - Malawi", 
            "ZMW": "Zambian Kwacha - Zambia", 
            "BWP": "Botswanan Pula - Botswana",
            "SZL": "Swazi Lilangeni - Eswatini", 
            "LSL": "Lesotho Loti - Lesotho", 
            "NAD": "Namibian Dollar - Namibia", 
            "AOA": "Angolan Kwanza - Angola", 
            "MZN": "Mozambican Metical - Mozambique",
            "MGA": "Malagasy Ariary - Madagascar", 
            "KMF": "Comorian Franc - Comoros", 
            "CVE": "Cape Verdean Escudo - Cape Verde", 
            "STN": "São Tomé and Príncipe Dobra - São Tomé and Príncipe",
            "GMD": "Gambian Dalasi - Gambia", 
            "SLE": "Sierra Leonean Leone - Sierra Leone", 
            "LRD": "Liberian Dollar - Liberia", 
            "GNF": "Guinean Franc - Guinea", 
            "BIF": "Burundian Franc - Burundi",
            "CDF": "Congolese Franc - Democratic Republic of the Congo",
            
            // American currencies
            "BRL": "Brazilian Real - Brazil", 
            "MXN": "Mexican Peso - Mexico", 
            "ARS": "Argentine Peso - Argentina", 
            "CLP": "Chilean Peso - Chile", 
            "COP": "Colombian Peso - Colombia",
            "PEN": "Peruvian Sol - Peru", 
            "UYU": "Uruguayan Peso - Uruguay", 
            "PYG": "Paraguayan Guarani - Paraguay", 
            "BOB": "Bolivian Boliviano - Bolivia", 
            "VES": "Venezuelan Bolívar - Venezuela",
            "GYD": "Guyanaese Dollar - Guyana", 
            "SRD": "Surinamese Dollar - Suriname", 
            "TTD": "Trinidad and Tobago Dollar - Trinidad and Tobago", 
            "JMD": "Jamaican Dollar - Jamaica", 
            "BBD": "Barbadian Dollar - Barbados",
            "BSD": "Bahamian Dollar - Bahamas", 
            "BZD": "Belize Dollar - Belize", 
            "GTQ": "Guatemalan Quetzal - Guatemala", 
            "HNL": "Honduran Lempira - Honduras", 
            "NIO": "Nicaraguan Córdoba - Nicaragua",
            "CRC": "Costa Rican Colón - Costa Rica", 
            "PAB": "Panamanian Balboa - Panama", 
            "CUP": "Cuban Peso - Cuba", 
            "CUC": "Cuban Convertible Peso - Cuba", 
            "HTG": "Haitian Gourde - Haiti",
            "DOP": "Dominican Peso - Dominican Republic", 
            "XCD": "East Caribbean Dollar - Eastern Caribbean Currency Union", 
            "AWG": "Aruban Florin - Aruba", 
            "ANG": "Netherlands Antillean Guilder - Curaçao and Sint Maarten", 
            "KYD": "Cayman Islands Dollar - Cayman Islands",
            "BMD": "Bermudan Dollar - Bermuda", 
            "FKP": "Falkland Islands Pound - Falkland Islands",
            
            // Oceanian currencies
            "NZD": "New Zealand Dollar - New Zealand", 
            "FJD": "Fijian Dollar - Fiji", 
            "PGK": "Papua New Guinean Kina - Papua New Guinea", 
            "SBD": "Solomon Islands Dollar - Solomon Islands", 
            "VUV": "Vanuatu Vatu - Vanuatu",
            "WST": "Samoan Tala - Samoa", 
            "TOP": "Tongan Paʻanga - Tonga",
            
            // Regional currencies
            "XPF": "CFP Franc - French Polynesia, New Caledonia, Wallis and Futuna",
            "MVR": "Maldivian Rufiyaa - Maldives"
        ]
        
        return currencyNames[currency] ?? currency
    }

    static func detectCurrency(from text: String) -> String? {
        let text = text.uppercased()

        // Check currency symbols
        for (code, symbol) in supportedCurrencies {
            if text.contains(symbol) || text.contains(code) {
                return code
            }
        }

        // Special check for USD
        if text.contains("$") && !text.contains("HK$") && !text.contains("A$") && !text.contains("C$") && !text.contains("S$") {
            return "USD"
        }

        return nil
    }
}

// MARK: - OCR Result Model
struct OCRResult {
    let amount: String
    let currency: String
    let time: String?
    let location: String?
    let card: String?
    let rawText: String
}

// MARK: - Extensions
extension DateFormatter {
    static let searchFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter
    }()

    static let displayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}

// MARK: - OCR Service
class OCRService: ObservableObject {
    static let shared = OCRService()

    func recognizeText(from image: UIImage, completion: @escaping (Result<OCRResult, Error>) -> Void) {
        guard let cgImage = image.cgImage else {
            completion(.failure(OCRError.invalidImage))
            return
        }

        let request = VNRecognizeTextRequest { request, error in
            if let error = error {
                completion(.failure(error))
                return
            }

            guard let observations = request.results as? [VNRecognizedTextObservation] else {
                completion(.failure(OCRError.noTextFound))
                return
            }

            let recognizedStrings = observations.compactMap { observation in
                observation.topCandidates(1).first?.string
            }

            let fullText = recognizedStrings.joined(separator: "\n")

            do {
                let result = try self.parsePaymentText(fullText)
                completion(.success(result))
            } catch {
                completion(.failure(error))
            }
        }

        request.recognitionLevel = .accurate
        request.usesLanguageCorrection = true

        let handler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        DispatchQueue.global(qos: .userInitiated).async {
            do {
                try handler.perform([request])
            } catch {
                completion(.failure(error))
            }
        }
    }

    func parsePaymentText(_ text: String) throws -> OCRResult {
        let lines = text.split(separator: "\n").map { String($0).trimmingCharacters(in: .whitespacesAndNewlines) }
        let data = lines.enumerated().map { (index: $0, content: $1) }

        // Use similar logic to regex-util.js
        let statusKeywords = ["Status", "Complete", "Completed", "Success", "Successful"]
        let totalKeywords = ["Total", "Amount", "Sum", "Balance"]

        var statusIndex: Int?
        // var totalIndex: Int? = nil // Temporarily commented out unused variable

        for (index, line) in data.enumerated() {
            if statusKeywords.contains(where: { line.content.hasPrefix($0) }) {
                statusIndex = index
            } else if totalKeywords.contains(where: { line.content.hasPrefix($0) }) {
                // totalIndex = index // Temporarily commented out
                break
            }
        }

        var amount = ""
        var currency = ""
        var time: String?
        var location: String?
        var card: String?

        // Find amount and currency
        for line in lines {
            if let match = line.range(of: #"([^\d]*)(\d+[,.]?\d*)\s*([^\d]*)"#, options: .regularExpression) {
                let matchedText = String(line[match])
                let components = matchedText.components(separatedBy: CharacterSet.decimalDigits.inverted)
                let numbers = components.filter { !$0.isEmpty }

                if let firstNumber = numbers.first, !firstNumber.isEmpty {
                    amount = firstNumber
                    // Extract currency symbol
                    if line.contains("¥") || line.contains("CNY") {
                        currency = "CNY"
                    } else if line.contains("$") || line.contains("USD") {
                        currency = "USD"
                    } else if line.contains("€") || line.contains("EUR") {
                        currency = "EUR"
                    } else {
                        currency = "USD" // Default
                    }
                    break
                }
            }
        }

        // Find time
        for line in lines {
            if line.range(of: #"\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2}"#, options: .regularExpression) != nil {
                time = line
                break
            }
        }

        // Find location (usually before status line)
        if let statusIdx = statusIndex, statusIdx > 0 {
            for i in stride(from: statusIdx - 1, through: 0, by: -1) {
                let line = data[i].content
                if line.range(of: #"\d{2}/\d{2}/\d{4}|\d{4}-\d{2}-\d{2}"#, options: .regularExpression) == nil {
                    location = line
                    break
                }
            }
        }

        // Find card information
        if let statusIdx = statusIndex, statusIdx + 1 < data.count {
            card = data[statusIdx + 1].content
        }

        guard !amount.isEmpty else {
            throw OCRError.parsingFailed
        }

        return OCRResult(
            amount: amount,
            currency: currency,
            time: time,
            location: location,
            card: card,
            rawText: text
        )
    }
}

enum OCRError: LocalizedError {
    case invalidImage
    case noTextFound
    case parsingFailed

    var errorDescription: String? {
        switch self {
        case .invalidImage:
            return "Invalid image"
        case .noTextFound:
            return "No text found"
        case .parsingFailed:
            return "Parsing failed"
        }
    }
}

// MARK: - Data Manager
class DataManager: ObservableObject {
    static let shared = DataManager()

    @Published var expenses: [ExpenseRecord] = []
    @Published var availableTags: [String] = [
        "Shopping", "Food", "Transport", "Entertainment",
        "Healthcare", "Education", "Travel", "Bills",
        "Groceries", "Coffee", "Gas", "Parking"
    ]

    @Published var availablePaymentCards: [String] = [
        "Monzo", "Starling", "Revolut", "Chase", "HSBC",
        "Barclays", "Santander", "Lloyds", "NatWest", "TSB"
    ]

    @Published var availableCurrencies: [String] = Array(CurrencyHelper.supportedCurrencies.keys).sorted()

    private let userDefaults = UserDefaults.standard
    private let expensesKey = "SavedExpenses"
    private let tagsKey = "AvailableTags"
    private let currenciesKey = "AvailableCurrencies"
    @MainActor private let cloudKitManager = CloudKitManager.shared

    init() {
        loadExpenses()
        loadTags()
        loadCurrencies()

        // Calibrate expense count after loading expenses
        Task { @MainActor in
            calibrateExpenseCount()
        }
    }

    @MainActor
    private func calibrateExpenseCount() {
        // Ensure expense count is accurate for free users
        let subscriptionManager = SubscriptionManager.shared
        subscriptionManager.recalculateExpenseCount(totalExpenses: expenses.count)
    }

    @MainActor
    func addExpense(_ expense: ExpenseRecord) {
        // Check if user can add new expense (limit for free users)
        let subscriptionManager = SubscriptionManager.shared
        if !subscriptionManager.canAddNewExpense() {
            // Notify UI that limit has been reached and show purchase view
            NotificationCenter.default.post(name: .showToast, object: ToastMessage(
                message: "Free limit reached! You can only add \(subscriptionManager.expenseLimit) expenses. Upgrade to Premium for unlimited expenses.",
                type: .error
            ))
            // Post notification to show purchase view
            NotificationCenter.default.post(name: .showPurchaseView, object: nil)
            return
        }
        
        // 创建带有多币种预存储的expense
        var enhancedExpense = expense
        enhancedExpense = addMultiCurrencyAmounts(to: enhancedExpense)

        expenses.insert(enhancedExpense, at: 0) // Insert at beginning
        saveExpenses()
        
        // Increment expense count for free users
        if !subscriptionManager.hasActiveSubscription {
            subscriptionManager.incrementExpenseCount()
        }
    }

    // 为expense添加多币种预存储
    private func addMultiCurrencyAmounts(to expense: ExpenseRecord) -> ExpenseRecord {
        var enhancedExpense = expense
        let cloudflareService = CloudflareExchangeRateService.shared

        var majorCurrencyAmounts: [String: Decimal] = [:]
        var exchangeRatesSnapshot: [String: Double] = [:]

        print("🔄 Starting multi-currency pre-storage for \(expense.amount) \(expense.currency)")

        // 为每种主流货币计算并存储金额
        for targetCurrency in CurrencyHelper.majorCurrencies {
            if targetCurrency == expense.currency {
                // 原始货币，直接存储
                majorCurrencyAmounts[targetCurrency] = expense.amount
                exchangeRatesSnapshot[targetCurrency] = 1.0
                print("💾 Original currency stored: \(expense.amount) \(targetCurrency)")
            } else {
                // 转换到目标货币
                if let rate = cloudflareService.getRate(from: expense.currency, to: targetCurrency) {
                    let convertedAmount = expense.amount * Decimal(rate)
                    majorCurrencyAmounts[targetCurrency] = convertedAmount
                    exchangeRatesSnapshot[targetCurrency] = rate
                    print("💱 Pre-stored: \(expense.amount) \(expense.currency) = \(convertedAmount) \(targetCurrency) (rate: \(rate))")
                } else {
                    print("⚠️ Failed to get rate for \(expense.currency) to \(targetCurrency) - will skip this currency")
                }
            }
        }

        enhancedExpense.majorCurrencyAmounts = majorCurrencyAmounts
        enhancedExpense.exchangeRatesSnapshot = exchangeRatesSnapshot

        print("✅ Multi-currency pre-storage completed: \(majorCurrencyAmounts.count)/\(CurrencyHelper.majorCurrencies.count) currencies stored")
        print("📊 Stored currencies: \(Array(majorCurrencyAmounts.keys).sorted())")
        return enhancedExpense
    }

    func updateExpense(_ expense: ExpenseRecord) {
        if let index = expenses.firstIndex(where: { $0.id == expense.id }) {
            expenses[index] = expense
            saveExpenses()
        }
    }

    @MainActor
    func deleteExpense(_ expense: ExpenseRecord) {
        expenses.removeAll { $0.id == expense.id }
        saveExpenses()

        // Update expense count for free users
        let subscriptionManager = SubscriptionManager.shared
        if !subscriptionManager.hasActiveSubscription {
            subscriptionManager.decrementExpenseCount()
        }

        // Also delete from CloudKit if available
        Task {
            do {
                try await cloudKitManager.deleteExpense(expense)
            } catch {
                print("❌ Failed to delete expense from CloudKit: \(error)")
            }
        }
    }

    @MainActor
    func deleteAllExpenses() {
        let expenseIDs = expenses.map { $0.id }
        expenses.removeAll()
        saveExpenses()

        // Reset expense count for free users
        let subscriptionManager = SubscriptionManager.shared
        if !subscriptionManager.hasActiveSubscription {
            subscriptionManager.expenseCount = 0
        }

        // Also delete all from CloudKit if available
        Task {
            do {
                try await cloudKitManager.deleteAllExpenses(expenseIDs)
            } catch {
                print("❌ Failed to delete all expenses from CloudKit: \(error)")
            }
        }
    }

    func searchExpenses(_ searchText: String) -> [ExpenseRecord] {
        if searchText.isEmpty {
            return expenses
        }
        return expenses.filter { $0.matches(searchText: searchText) }
    }

    // TAG management - limit to maximum 15 tags
    func addTag(_ tag: String) {
        let cleanTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        if !cleanTag.isEmpty && !availableTags.contains(cleanTag) && availableTags.count < 15 {
            availableTags.append(cleanTag)
            saveTags()
        }
    }

    func removeTag(_ tag: String) {
        availableTags.removeAll { $0 == tag }
        saveTags()
    }

    // MARK: - Currency Management
    func addCurrency(_ currency: String) {
        let upperCurrency = currency.uppercased()
        if !availableCurrencies.contains(upperCurrency) {
            availableCurrencies.append(upperCurrency)
            availableCurrencies.sort()
            saveCurrencies()
        }
    }

    func getOrAddCurrency(_ currency: String) -> String {
        let upperCurrency = currency.uppercased()
        if !availableCurrencies.contains(upperCurrency) {
            addCurrency(upperCurrency)
        }
        return upperCurrency
    }

    // Image processing function - temporarily use local OCR, pending LLM service integration
    func processImage(_ image: UIImage) {
        // TODO: Integrate LLM parsing service
        // Task { @MainActor in
        //     do {
        //         let llmService = LLMParsingService.shared
        //         let expense = try await llmService.parseExpenseFromImage(image)
        //         self.addExpense(expense)
        //     } catch {
        //         print("LLM parsing failed: \(error.localizedDescription)")
        //         fallbackToLocalOCR(image)
        //     }
        // }

        // Temporarily use local OCR directly
        fallbackToLocalOCR(image)
    }

    // Fallback to local OCR parsing
    private func fallbackToLocalOCR(_ image: UIImage) {
        let ocrService = OCRService()

        ocrService.recognizeText(from: image) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let ocrResult):
                    guard let amount = Decimal(string: ocrResult.amount) else {
                        print("Unable to parse amount: \(ocrResult.amount)")
                        return
                    }

                    let expense = ExpenseRecord(
                        amount: amount,
                        currency: ocrResult.currency,
                        merchant: ocrResult.location,
                        date: Date(),
                        locationName: ocrResult.location,
                        tags: [],
                        paymentMethod: "Local Recognition",
                        rawText: ocrResult.rawText
                    )

                    self?.addExpense(expense)
                case .failure(let error):
                    print("OCR recognition failed: \(error)")
                }
            }
        }
    }

    // Statistics function
    func getTotalAmount(for currency: String = "USD") -> Decimal {
        return expenses
            .filter { $0.currency == currency }
            .reduce(0) { $0 + $1.amount }
    }

    // 获取所有费用的总额（转换为基础货币）
    func getTotalAmountInBaseCurrency() -> Decimal {
        let cloudflareService = CloudflareExchangeRateService.shared

        return expenses.reduce(Decimal(0)) { total, expense in
            let convertedAmount = convertToBaseCurrency(
                amount: expense.amount,
                fromCurrency: expense.currency,
                toBaseCurrency: "USD",
                using: cloudflareService
            )
            return total + convertedAmount
        }
    }

    private func convertToBaseCurrency(amount: Decimal, fromCurrency: String, toBaseCurrency: String, using service: CloudflareExchangeRateService) -> Decimal {
        // If same currency, no conversion needed
        if fromCurrency == toBaseCurrency {
            return amount
        }

        // 只使用Cloudflare service，不使用任何硬编码汇率
        if let rate = service.getRate(from: fromCurrency, to: toBaseCurrency) {
            let convertedAmount = amount * Decimal(rate)
            return convertedAmount
        }

        // 如果没有API汇率数据，返回原始金额，不使用硬编码汇率
        return amount
    }

    func getExpensesByTag() -> [String: [ExpenseRecord]] {
        var result: [String: [ExpenseRecord]] = [:]
        for expense in expenses {
            for tag in expense.tags {
                if result[tag] == nil {
                    result[tag] = []
                }
                result[tag]?.append(expense)
            }
        }
        return result
    }

    func getExpensesByMonth() -> [String: [ExpenseRecord]] {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM"
        return Dictionary(grouping: expenses) { formatter.string(from: $0.date) }
    }

    func filterExpenses(by tag: String) -> [ExpenseRecord] {
        return expenses.filter { $0.tags.contains(tag) }
    }

    func createSampleData() {
        let now = Date()
        let calendar = Calendar.current

        let sampleExpenses = [
            // Today's expenses with Coffee tag
            ExpenseRecord(
                amount: 5.99,
                currency: "USD",
                merchant: "Starbucks",
                date: now.addingTimeInterval(-3600), // 1 hour ago
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Monzo",
                notes: "Morning coffee",
                rawText: "Starbucks payment $5.99"
            ),
            ExpenseRecord(
                amount: 15.50,
                currency: "USD",
                merchant: "Blue Bottle Coffee",
                date: now.addingTimeInterval(-7200), // 2 hours ago
                latitude: 37.7849,
                longitude: -122.4094,
                locationName: "San Francisco, CA",
                tags: ["Coffee"],
                paymentMethod: "Credit Card",
                paymentCard: "Chase",
                notes: "Afternoon coffee",
                rawText: "Blue Bottle Coffee $15.50"
            ),
            // Yesterday's expenses
            ExpenseRecord(
                amount: 25.99,
                currency: "USD",
                merchant: "Starbucks",
                date: calendar.date(byAdding: .day, value: -1, to: now)!,
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Monzo",
                notes: "Morning coffee",
                rawText: "Starbucks payment $25.99"
            ),
            ExpenseRecord(
                amount: 12.50,
                currency: "USD",
                merchant: "McDonald's",
                date: calendar.date(byAdding: .day, value: -1, to: now)!,
                latitude: 37.7849,
                longitude: -122.4094,
                locationName: "San Francisco, CA",
                tags: ["Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Starling",
                notes: "Quick lunch",
                rawText: "McDonald's payment $12.50"
            ),
            // This week's expenses
            ExpenseRecord(
                amount: 89.99,
                currency: "USD",
                merchant: "Target",
                date: calendar.date(byAdding: .day, value: -3, to: now)!,
                latitude: 37.7649,
                longitude: -122.4294,
                locationName: "San Francisco, CA",
                tags: ["Shopping", "Groceries"],
                paymentMethod: "Apple Pay",
                paymentCard: "Chase",
                notes: "Household items",
                rawText: "Target payment $89.99"
            ),
            ExpenseRecord(
                amount: 8.75,
                currency: "USD",
                merchant: "Peet's Coffee",
                date: calendar.date(byAdding: .day, value: -4, to: now)!,
                latitude: 37.7549,
                longitude: -122.4394,
                locationName: "San Francisco, CA",
                tags: ["Coffee"],
                paymentMethod: "Debit Card",
                paymentCard: "Bank of America",
                notes: "Weekend coffee",
                rawText: "Peet's Coffee $8.75"
            ),
            // This month's expenses
            ExpenseRecord(
                amount: 45.00,
                currency: "CNY",
                merchant: "Didi Ride",
                date: calendar.date(byAdding: .day, value: -10, to: now)!,
                latitude: 39.9042,
                longitude: 116.4074,
                locationName: "Beijing",
                tags: ["Transport"],
                paymentMethod: "WeChat Pay",
                notes: "Taxi fare",
                rawText: "Didi Ride ¥45.00"
            ),
            ExpenseRecord(
                amount: 18.99,
                currency: "USD",
                merchant: "Philz Coffee",
                date: calendar.date(byAdding: .day, value: -15, to: now)!,
                latitude: 37.7649,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Coffee", "Food"],
                paymentMethod: "Apple Pay",
                paymentCard: "Wells Fargo",
                notes: "Special blend coffee",
                rawText: "Philz Coffee $18.99"
            ),
            // Older expenses (beyond this month)
            ExpenseRecord(
                amount: 120.00,
                currency: "USD",
                merchant: "Amazon",
                date: calendar.date(byAdding: .month, value: -2, to: now)!,
                latitude: 37.7749,
                longitude: -122.4194,
                locationName: "San Francisco, CA",
                tags: ["Shopping", "Electronics"],
                paymentMethod: "Credit Card",
                paymentCard: "Amazon Card",
                notes: "Online purchase",
                rawText: "Amazon $120.00"
            )
        ]

        for expense in sampleExpenses {
            if !expenses.contains(where: { $0.merchant == expense.merchant && $0.amount == expense.amount }) {
                expenses.append(expense)
            }
        }
        saveExpenses()

        // Verify filtering functionality
        verifyFilteringFunctionality()
    }

    func saveExpenses() {
        if let encoded = try? JSONEncoder().encode(expenses) {
            userDefaults.set(encoded, forKey: expensesKey)
        }
    }

    // MARK: - Filtering Verification

    private func verifyFilteringFunctionality() {
        print("🧪 Starting filtering functionality verification...")

        // Checkpoint 1: Test #tag and time filter combination
        testCoffeeTagWithTodayFilter()
        testCoffeeTagWithWeekFilter()
        testFoodTagWithTodayFilter()

        print("✅ All filtering tests passed!")
    }

    private func testCoffeeTagWithTodayFilter() {
        let filteredExpenses = filterExpenses(tag: "Coffee", timeRange: .today)

        print("☕ Coffee + Today filter: Found \(filteredExpenses.count) expenses")

        // Verify all results have Coffee tag
        let allHaveCoffeeTag = filteredExpenses.allSatisfy { $0.tags.contains("Coffee") }
        assert(allHaveCoffeeTag, "All results should have Coffee tag")

        // Verify all results are from today
        let calendar = Calendar.current
        let today = Date()
        let allFromToday = filteredExpenses.allSatisfy { calendar.isDate($0.date, inSameDayAs: today) }
        assert(allFromToday, "All results should be from today")

        let totalAmount = filteredExpenses.reduce(0) { $0 + $1.amount }
        print("   Total amount: $\(totalAmount)")

        for expense in filteredExpenses {
            print("   - \(expense.merchant ?? "Unknown"): $\(expense.amount) [\(expense.tags.joined(separator: ", "))]")
        }
    }

    private func testCoffeeTagWithWeekFilter() {
        let filteredExpenses = filterExpenses(tag: "Coffee", timeRange: .week)

        print("☕ Coffee + Week filter: Found \(filteredExpenses.count) expenses")

        // Verify all results have Coffee tag
        let allHaveCoffeeTag = filteredExpenses.allSatisfy { $0.tags.contains("Coffee") }
        assert(allHaveCoffeeTag, "All results should have Coffee tag")

        let totalAmount = filteredExpenses.reduce(0) { $0 + $1.amount }
        print("   Total amount: $\(totalAmount)")
    }

    private func testFoodTagWithTodayFilter() {
        let filteredExpenses = filterExpenses(tag: "Food", timeRange: .today)

        print("🍔 Food + Today filter: Found \(filteredExpenses.count) expenses")

        // Verify all results have Food tag
        let allHaveFoodTag = filteredExpenses.allSatisfy { $0.tags.contains("Food") }
        assert(allHaveFoodTag, "All results should have Food tag")

        let totalAmount = filteredExpenses.reduce(0) { $0 + $1.amount }
        print("   Total amount: $\(totalAmount)")

        for expense in filteredExpenses {
            print("   - \(expense.merchant ?? "Unknown"): $\(expense.amount) [\(expense.tags.joined(separator: ", "))]")
        }
    }

    private enum TimeRange {
        case all, today, week, month, year
    }

    private func filterExpenses(tag: String, timeRange: TimeRange) -> [ExpenseRecord] {
        var filteredExpenses = expenses

        // Apply tag filter
        if !tag.isEmpty {
            filteredExpenses = filteredExpenses.filter { expense in
                expense.tags.contains(tag)
            }
        }

        // Apply time range filter
        let calendar = Calendar.current
        let now = Date()

        switch timeRange {
        case .all:
            break
        case .today:
            filteredExpenses = filteredExpenses.filter { calendar.isDate($0.date, inSameDayAs: now) }
        case .week:
            let weekAgo = calendar.date(byAdding: .day, value: -7, to: now)!
            filteredExpenses = filteredExpenses.filter { $0.date >= weekAgo }
        case .month:
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now)!
            filteredExpenses = filteredExpenses.filter { $0.date >= monthAgo }
        case .year:
            let yearAgo = calendar.date(byAdding: .year, value: -1, to: now)!
            filteredExpenses = filteredExpenses.filter { $0.date >= yearAgo }
        }

        return filteredExpenses
    }

    private func loadExpenses() {
        if let data = userDefaults.data(forKey: expensesKey),
           let decoded = try? JSONDecoder().decode([ExpenseRecord].self, from: data) {
            expenses = decoded
        }
    }

    private func saveTags() {
        if let encoded = try? JSONEncoder().encode(availableTags) {
            userDefaults.set(encoded, forKey: tagsKey)
        }
    }

    private func loadTags() {
        if let data = userDefaults.data(forKey: tagsKey),
           let decoded = try? JSONDecoder().decode([String].self, from: data) {
            availableTags = decoded
        }
    }

    private func saveCurrencies() {
        if let encoded = try? JSONEncoder().encode(availableCurrencies) {
            userDefaults.set(encoded, forKey: currenciesKey)
        }
    }

    private func loadCurrencies() {
        if let data = userDefaults.data(forKey: currenciesKey),
           let decoded = try? JSONDecoder().decode([String].self, from: data) {
            availableCurrencies = decoded
        } else {
            // 如果没有保存的货币，使用默认的支持货币列表
            availableCurrencies = Array(CurrencyHelper.supportedCurrencies.keys).sorted()
        }
    }

    // CSV export function
    func exportToCSV() -> String {
        var csvContent = "Date,Merchant,Amount,Currency,Location,Tags,Payment Method,Payment Card,Notes\n"

        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss"

        for expense in expenses {
            let date = formatter.string(from: expense.date)
            let merchant = expense.merchant?.replacingOccurrences(of: ",", with: ";") ?? ""
            let amount = String(describing: expense.amount)
            let currency = expense.currency
            let location = expense.locationName?.replacingOccurrences(of: ",", with: ";") ?? ""
            let tags = expense.formattedTags.replacingOccurrences(of: ",", with: ";")
            let paymentMethod = expense.paymentMethod?.replacingOccurrences(of: ",", with: ";") ?? ""
            let paymentCard = expense.paymentCard?.replacingOccurrences(of: ",", with: ";") ?? ""
            let notes = expense.notes?.replacingOccurrences(of: ",", with: ";") ?? ""

            csvContent += "\(date),\(merchant),\(amount),\(currency),\(location),\(tags),\(paymentMethod),\(paymentCard),\(notes)\n"
        }

        return csvContent
    }

    // MARK: - iCloud Sync Methods
    func syncToiCloud() async throws {
        try await cloudKitManager.syncExpenses(expenses)
    }

    func syncFromiCloud() async throws {
        let cloudExpenses = try await cloudKitManager.fetchExpenses()
        
        print("🔄 syncFromiCloud: Found \(cloudExpenses.count) cloud expenses")
        print("🔄 syncFromiCloud: Current local expenses: \(expenses.count)")

        // If CloudKit is empty, this might be first sync - don't delete local data
        if cloudExpenses.isEmpty {
            print("🔄 syncFromiCloud: CloudKit is empty - assuming first sync, keeping local data")
            return
        }

        // If we have no local expenses, just use the cloud ones
        if expenses.isEmpty {
            print("🔄 syncFromiCloud: No local expenses, using cloud data")
            await MainActor.run {
                expenses = cloudExpenses
                saveExpenses()
            }
            return
        }

        // Create a set of cloud expense IDs for efficient lookup
        let cloudExpenseIDs = Set(cloudExpenses.map { $0.id })
        
        // Remove local expenses that don't exist in cloud (were deleted elsewhere)
        let localExpensesBeforeRemoval = expenses.count
        expenses.removeAll { !cloudExpenseIDs.contains($0.id) }
        let removedCount = localExpensesBeforeRemoval - expenses.count

        // Add cloud expenses that don't exist locally
        var addedCount = 0
        for cloudExpense in cloudExpenses {
            if !expenses.contains(where: { $0.id == cloudExpense.id }) {
                expenses.append(cloudExpense)
                addedCount += 1
                print("🔄 syncFromiCloud: Added cloud expense \(cloudExpense.id)")
            }
        }

        // Sort by transactionDate (newest first)
        expenses.sort { ($0.transactionDate ?? $0.date) > ($1.transactionDate ?? $1.date) }

        print("🔄 syncFromiCloud: Removed \(removedCount) deleted items, added \(addedCount) new items")
        print("🔄 syncFromiCloud: Final count: \(expenses.count) expenses")

        await MainActor.run {
            saveExpenses()
        }
    }

    func performFullSync() async throws {
        print("🚀 Starting full sync process...")
        
        // First check what's in CloudKit to determine sync strategy
        let cloudExpenses = try await cloudKitManager.fetchExpenses()
        
        if cloudExpenses.isEmpty {
            print("📱 CloudKit is empty - this appears to be first sync")
            print("📤 Uploading local data to CloudKit...")
            try await syncToiCloud()
            print("✅ First sync completed - local data uploaded to CloudKit")
        } else {
            print("🔄 CloudKit has data - performing bidirectional sync...")
            // First sync local data to cloud
            try await syncToiCloud()
            // Then fetch any new data from cloud
            try await syncFromiCloud()
            print("✅ Bidirectional sync completed")
        }
    }
}

// MARK: - Add Expense App Intent (iOS 16+)
@available(iOS 16.0, *)
struct AddExpenseAppIntent: AppIntent {
    static var title: LocalizedStringResource = "Add Expense to FinPin"
    static var description = IntentDescription("Add an expense to FinPin by providing expense details as text")

    // This is crucial for Shortcuts discovery
    static var openAppWhenRun: Bool = false
    
    static var parameterSummary: some ParameterSummary {
        Summary("Add expense: \(\.$expenseText)")
    }
    
    @Parameter(
        title: "Expense Text", 
        description: "Describe your expense (e.g., 'Spent $25 at Starbucks')",
        requestValueDialog: IntentDialog("What expense would you like to add?")
    )
    var expenseText: String
    
    @MainActor
    func perform() async throws -> some IntentResult & ProvidesDialog {
        guard !expenseText.isEmpty else {
            throw AddExpenseError.missingText
        }
        
        print("🎯 AddExpenseAppIntent.perform() called with text: \(expenseText)")
        
        // Process the expense using SiriShortcutsManager
        let success = await SiriShortcutsManager.shared.processExpenseFromSiri(text: expenseText)
        
        // Always return success since SiriShortcutsManager handles fallback parsing
        // This prevents the "error 1" message in Shortcuts
        if success {
            return .result(dialog: "✅ Successfully added :)")
        } else {
            // Even if the advanced parsing failed, the fallback should have worked
            print("🔄 AddExpenseAppIntent: Using fallback parsing result")
            return .result(dialog: "✅ Successfully added :)")
        }
    }
}

// MARK: - Error Handling
@available(iOS 16.0, *)
enum AddExpenseError: Swift.Error, LocalizedError {
    case missingText
    case processingFailed
    case deviceRegistrationFailed
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .missingText:
            return "Please provide expense details"
        case .processingFailed:
            return "Failed to process expense. The app will save a basic record using fallback parsing."
        case .deviceRegistrationFailed:
            return "Failed to register device for advanced parsing. Using basic parsing instead."
        case .networkError:
            return "Network error. Please check your connection and try again."
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .missingText:
            return "Try saying something like 'I spent 25 dollars at Starbucks'"
        case .processingFailed:
            return "Your expense has been saved with basic information. You can edit it later in the app."
        case .deviceRegistrationFailed:
            return "Try using the main app first to set up advanced parsing."
        case .networkError:
            return "Check your internet connection and try again."
        }
    }
}

@main
struct FinPinApp: App {
    let dataManager = DataManager.shared
    @StateObject private var migrationManager = MigrationManager.shared

    init() {
        // Load custom endpoint settings on app launch
        loadCustomEndpointSettings()

        // Check if this is the first launch and fetch exchange rates
        checkFirstLaunchAndFetchRates()

        // Initialize Siri Shortcuts Manager to ensure intents are registered
        _ = SiriShortcutsManager.shared
        print("✅ SiriShortcutsManager initialized")

        // App Intents will be auto-discovered for iOS 16+
        if #available(iOS 16.0, *) {
            print("✅ App Intents framework available for iOS 16+")
        }

        // Modern App Intents (iOS 16+) are auto-discovered
        print("✅ App Intents framework available - shortcuts will be auto-discovered")
    }

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(dataManager)
                .environmentObject(migrationManager)
                .onOpenURL { url in
                    handleShortcutURL(url)
                }
                .onContinueUserActivity("app.finpin.v1.addExpenseVoice") { userActivity in
                    handleVoiceExpenseActivity(userActivity)
                }
                .onContinueUserActivity("app.finpin.v1.addExpenseText") { userActivity in
                    handleTextExpenseActivity(userActivity)
                }
                .onAppear {
                    // Perform migration check on app launch
                    Task {
                        await migrationManager.checkAndPerformMigration()
                    }
                }
                .overlay {
                    // Show migration alert if needed
                    if migrationManager.showMigrationAlert {
                        Color.black.opacity(0.3)
                            .ignoresSafeArea()
                            .overlay {
                                MigrationAlertView()
                            }
                    }
                }
        }
    }
    
    // MARK: - App Shortcuts Registration
    @available(iOS 16.0, *)
    static var appShortcuts: [AppShortcut] {
        [AppShortcut(
            intent: AddExpenseAppIntent(),
            phrases: [
                "Add expense to FinPin",
                "Record expense in FinPin",
                "Log expense to FinPin",
                "FinPin add expense",
                "Add expense with FinPin"
            ],
            shortTitle: "Add Expense",
            systemImageName: "plus.circle"
        )]
    }

    private func handleShortcutURL(_ url: URL) {
        print("🔗 Received URL: \(url)")
        print("🔗 URL scheme: \(url.scheme ?? "nil")")
        print("🔗 URL host: \(url.host ?? "nil")")

        guard url.scheme == "finpin" else {
            print("❌ Invalid scheme: \(url.scheme ?? "nil")")
            return
        }

        // Parse the URL to extract text data and X-Callback-URL parameters
        if url.host == "add-expense" {
            let components = URLComponents(url: url, resolvingAgainstBaseURL: false)
            print("🔗 URL components: \(components?.queryItems ?? [])")

            // Extract X-Callback-URL parameters
            let xSuccessURL = components?.queryItems?.first(where: { $0.name == "x-success" })?.value
            let xErrorURL = components?.queryItems?.first(where: { $0.name == "x-error" })?.value
            let xCancelURL = components?.queryItems?.first(where: { $0.name == "x-cancel" })?.value

            print("🔗 X-Callback URLs - Success: \(xSuccessURL ?? "nil"), Error: \(xErrorURL ?? "nil"), Cancel: \(xCancelURL ?? "nil")")

            if let textItem = components?.queryItems?.first(where: { $0.name == "text" }),
               let text = textItem.value {
                print("✅ Processing text from URL: \(text)")
                // Process the text from Shortcuts with callback URLs
                processShortcutText(text, successURL: xSuccessURL, errorURL: xErrorURL)
            } else {
                print("❌ No text parameter found in URL")
                // Call error callback if available
                if let errorURL = xErrorURL {
                    callbackToShortcuts(errorURL, message: "No text parameter found")
                }
            }
        } else {
            print("❌ Invalid host: \(url.host ?? "nil"), expected: add-expense")
        }
    }

    private func processShortcutText(_ text: String, successURL: String? = nil, errorURL: String? = nil) {
        print("📝 Processing shortcut text: \(text)")

        // Show processing toast
        NotificationCenter.default.post(
            name: .showToast,
            object: ToastMessage(
                message: "Analyzing text ...",
                type: .info,
                duration: 30.0
            )
        )

        Task { @MainActor in
            do {
                // Parse the text using Cloudflare AI service
                let parsedExpense = try await parseWithServerAPI(text)
                dataManager.addExpense(parsedExpense)
                print("✅ Successfully added expense: \(parsedExpense.merchant ?? "Unknown") - \(parsedExpense.amount)")

                // Show success toast
                NotificationCenter.default.post(
                    name: .showToast,
                    object: ToastMessage(
                        message: "Expense added successfully\n\(parsedExpense.formattedAmount) at \(parsedExpense.displayMerchant)",
                        type: .success
                    )
                )

                // Call success callback if available
                if let successURL = successURL {
                    let message = "Expense added: \(parsedExpense.formattedAmount) at \(parsedExpense.displayMerchant)"
                    callbackToShortcuts(successURL, message: message)
                }

            } catch {
                print("❌ Cloudflare AI parsing failed: \(error)")

                // Show error toast
                NotificationCenter.default.post(
                    name: .showToast,
                    object: ToastMessage(
                        message: "Failed to parse expense\nPlease check your internet connection and try again",
                        type: .error
                    )
                )

                // Call error callback if available
                if let errorURL = errorURL {
                    callbackToShortcuts(errorURL, message: "Failed to parse expense: \(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - X-Callback-URL Support
    private func callbackToShortcuts(_ callbackURL: String, message: String? = nil) {
        print("🔄 Calling back to Shortcuts: \(callbackURL)")

        var urlString = callbackURL

        // Add message parameter if provided
        if let message = message {
            let separator = callbackURL.contains("?") ? "&" : "?"
            let encodedMessage = message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? message
            urlString += "\(separator)message=\(encodedMessage)"
        }

        guard let url = URL(string: urlString) else {
            print("❌ Invalid callback URL: \(urlString)")
            return
        }

        // Use UIApplication to open the callback URL
        DispatchQueue.main.async {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url) { success in
                    print(success ? "✅ Successfully called back to Shortcuts" : "❌ Failed to call back to Shortcuts")
                }
            } else {
                print("❌ Cannot open callback URL: \(url)")
            }
        }
    }

    private func parseWithServerAPI(_ text: String) async throws -> ExpenseRecord {
        // Use CloudflareExpenseParser for intelligent parsing
        let parsedExpense = try await CloudflareExpenseParser.shared.parseExpense(from: text)

        // Create expense record with parsed data
        var expense = ExpenseRecord(
            id: UUID(),
            amount: parsedExpense.amount,
            currency: parsedExpense.currency,
            merchant: parsedExpense.merchant,
            date: parsedExpense.date ?? Date(),
            transactionDate: parsedExpense.transactionDate,
            tags: parsedExpense.tags + ["Shortcuts"],
            paymentMethod: parsedExpense.paymentMethod,
            notes: parsedExpense.notes,
            rawText: text
        )
        
        print("🕐 parseWithServerAPI: Created ExpenseRecord from CloudflareExpenseParser:")
        print("   - date (record creation): \(expense.date)")
        print("   - transactionDate (actual): \(expense.transactionDate?.description ?? "nil")")
        print("   - effectiveDate: \(expense.effectiveDate)")

        // Add multi-currency pre-storage
        expense = await addMultiCurrencyAmounts(to: expense)

        return expense
    }

    private func addMultiCurrencyAmounts(to expense: ExpenseRecord) async -> ExpenseRecord {
        var enhancedExpense = expense
        let cloudflareService = CloudflareExchangeRateService.shared

        var majorCurrencyAmounts: [String: Decimal] = [:]
        var exchangeRatesSnapshot: [String: Double] = [:]

        // 为每种主流货币计算并存储金额
        for targetCurrency in CurrencyHelper.majorCurrencies {
            if targetCurrency == expense.currency {
                // 原始货币，直接存储
                majorCurrencyAmounts[targetCurrency] = expense.amount
                exchangeRatesSnapshot[targetCurrency] = 1.0
            } else {
                // 转换到目标货币
                if let rate = cloudflareService.getRate(from: expense.currency, to: targetCurrency) {
                    let convertedAmount = expense.amount * Decimal(rate)
                    majorCurrencyAmounts[targetCurrency] = convertedAmount
                    exchangeRatesSnapshot[targetCurrency] = rate
                }
            }
        }

        enhancedExpense.majorCurrencyAmounts = majorCurrencyAmounts
        enhancedExpense.exchangeRatesSnapshot = exchangeRatesSnapshot

        return enhancedExpense
    }

    // MARK: - User Activity Handlers
    private func handleVoiceExpenseActivity(_ userActivity: NSUserActivity) {
        print("🎤 Handling voice expense activity")

        // Extract text from user activity
        if let text = userActivity.userInfo?["text"] as? String {
            Task {
                await SiriShortcutsManager.shared.processExpenseFromSiri(text: text)
            }
        } else {
            print("⚠️ No text found in voice activity")
        }
    }

    private func handleTextExpenseActivity(_ userActivity: NSUserActivity) {
        print("📝 Handling text expense activity")

        // Extract text from user activity - use the same processing as URL shortcuts
        if let text = userActivity.userInfo?["text"] as? String {
            processShortcutText(text)
        } else {
            print("⚠️ No text found in text activity")
        }
    }

    private func checkFirstLaunchAndFetchRates() {
        let userDefaults = UserDefaults.standard
        let hasLaunchedBefore = userDefaults.bool(forKey: "hasLaunchedBefore")

        if !hasLaunchedBefore {
            print("📱 First launch detected, fetching exchange rates and registering device...")
            userDefaults.set(true, forKey: "hasLaunchedBefore")

            // Fetch exchange rates and register device on first launch
            Task {
                let exchangeService = CloudflareExchangeRateService.shared
                // 直接调用fetchRatesForCurrency确保获取汇率
                await exchangeService.fetchRatesForCurrency("USD")
                print("✅ Exchange rates fetched on first launch")

                // Use unified device registration through APIClient
                await ensureDeviceRegistration()
            }
        } else {
            print("📱 Not first launch, checking device registration...")
            // For existing users, ensure device registration is valid
            Task {
                await ensureDeviceRegistration()
            }
        }
    }

    @MainActor
    private func ensureDeviceRegistration() async {
        // Check if device is already registered using legacy UserDefaults
        let deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId != nil && keySeed != nil {
            print("✅ Device already registered")
            return
        }

        print("🔐 Device not registered, attempting registration...")

        do {
            let newDeviceId = UUID().uuidString

            let endpointManager = APIEndpointManager.shared
            let url = URL(string: endpointManager.getDeviceRegisterURL())!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("FinPin-AppLaunch/1.0", forHTTPHeaderField: "User-Agent")
            request.timeoutInterval = 30.0

            let requestBody: [String: Any] = [
                "device_id": newDeviceId,
                "device_info": [
                    "model": UIDevice.current.model,
                    "os_version": UIDevice.current.systemVersion,
                    "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                    "platform": "ios"
                ]
            ]

            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                print("❌ Device registration failed: HTTP \((response as? HTTPURLResponse)?.statusCode ?? 0)")
                return
            }

            let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]

            if let success = responseData?["success"] as? Bool, success,
               let data = responseData?["data"] as? [String: Any],
               let keySeed = data["key_seed"] as? String {

                UserDefaults.standard.set(newDeviceId, forKey: "debug_device_id")
                UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")

                print("✅ Device registration successful")
                print("📱 Device ID: \(newDeviceId)")
                print("🔑 Key seed stored securely")
            } else {
                print("❌ Device registration failed: Invalid response")
            }

        } catch {
            print("❌ Device registration error: \(error.localizedDescription)")
            // Don't block the app, registration will be retried when needed
        }
    }



    private func loadCustomEndpointSettings() {
        let isUsingCustomEndpoint = UserDefaults.standard.bool(forKey: "isUsingCustomEndpoint")
        let customEndpoint = UserDefaults.standard.string(forKey: "customAPIEndpoint") ?? ""

        if isUsingCustomEndpoint && !customEndpoint.isEmpty {
            // Validate URL format
            guard let url = URL(string: customEndpoint),
                  url.scheme == "https" else {
                print("Warning: Invalid custom endpoint URL: \(customEndpoint)")
                return
            }

            print("Custom API endpoint loaded: \(customEndpoint)")
            // Note: The actual endpoint configuration will be handled by APIClient
            // when it reads these UserDefaults values
        } else {
            print("Using default API endpoint")
        }
    }
}

// MARK: - Cloudflare Expense Parser
class CloudflareExpenseParser {
    static let shared = CloudflareExpenseParser()

    private var baseURL: String {
        // Use the new API configuration system
        let configManager = APIConfigurationManager.shared
        let endpointManager = APIEndpointManager.shared

        if configManager.isCustomModeEnabled, let activeConfig = configManager.activeConfiguration {
            print("🔧 CloudflareExpenseParser: Using custom API configuration: \(activeConfig.name)")
            return activeConfig.baseURL
        } else {
            print("🔧 CloudflareExpenseParser: Using new FinPin hosted endpoint (v1.1.0)")
            return endpointManager.getCurrentBaseURL()
        }
    }

    private init() {}

    struct ParsedExpense {
        let amount: Decimal
        let currency: String
        let merchant: String?
        let paymentMethod: String?
        let paymentCard: String?
        let location: String?
        let date: Date? // Record creation time
        let transactionDate: Date? // Actual transaction time from receipt
        let originalTimestamp: String? // Original timestamp string from API
        let tags: [String]
        let notes: String?
        let category: String?
        let confidence: Double?
    }

    struct CloudflareParseRequest: Codable {
        let text: String
        let source: String
    }

    struct CloudflareParseResponse: Codable {
        let success: Bool
        let data: ExpenseData?
        let error: String?

        struct ExpenseData: Codable {
            let amount: Double
            let currency: String
            let merchant: String?
            let date: String?
            let tags: [String]?
            let notes: String?
            let category: String?
        }
    }

    func parseExpense(from text: String) async throws -> ParsedExpense {
        print("🚀 CloudflareExpenseParser: Starting parse request...")
        print("📝 Text to parse: \(text.prefix(100))\(text.count > 100 ? "..." : "")")
        print("🌐 Using endpoint: \(baseURL)")

        let configManager = APIConfigurationManager.shared

        // Check if using custom API configuration
        if configManager.isCustomModeEnabled, let activeConfig = configManager.activeConfiguration {
            print("🔧 CloudflareExpenseParser: Using custom API configuration: \(activeConfig.name) (\(activeConfig.provider.displayName))")
            return try await parseExpenseWithCustomAPI(text: text, configuration: activeConfig)
        } else {
            print("🔧 CloudflareExpenseParser: Using FinPin hosted API")
            return try await parseExpenseWithFinPinAPI(text: text)
        }
    }

    private func parseExpenseWithFinPinAPI(text: String) async throws -> ParsedExpense {
        let endpointManager = APIEndpointManager.shared
        let parseURL = endpointManager.getExpenseParseURL()
        print("🌐 Using FinPin hosted endpoint: \(parseURL)")

        // Priority 1: Try to extract time using regex from original text (preserves local time)
        let regexExtractedTime = TimeExtractor.shared.extractLocalTime(from: text)
        if let extractedTime = regexExtractedTime {
            print("✅ CloudflareExpenseParser: Found local time via regex: \(TimeExtractor.shared.formatLocalTime(extractedTime))")
        }

        // Check if we have stored credentials, if not, try to register device first
        var deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        var keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId == nil || keySeed == nil {
            print("🔄 Device credentials missing, attempting registration...")
            // Try to register device first
            await ensureDeviceRegistration()

            // Check again after registration attempt
            deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
            keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

            guard let finalDeviceId = deviceId, let finalKeySeed = keySeed else {
                throw NSError(domain: "CloudflareExpenseParser", code: -1, userInfo: [NSLocalizedDescriptionKey: "Unable to register device. Please check your network connection and try again."])
            }
        }

        // Use the verified credentials
        let finalDeviceId = deviceId!
        let finalKeySeed = keySeed!

        // Create request body matching the format used by image import
        let requestBodyDict: [String: Any] = [
            "text": text,
            "context": [
                "timestamp": ISO8601DateFormatter().string(from: Date()),
                "timezone_offset": TimeZone.current.secondsFromGMT() / 3600, // Add current timezone offset in hours
                "image_metadata": [
                    "format": "text",
                    "source": "url_scheme"
                ]
            ]
        ]

        // Create JSON matching JavaScript JSON.stringify() format (compact, no extra spaces)
        let requestBodyData = try JSONSerialization.data(withJSONObject: requestBodyDict, options: [])
        let requestBodyString = String(data: requestBodyData, encoding: .utf8) ?? ""

        // Generate timestamp and signature
        let timestamp = String(Int(Date().timeIntervalSince1970 * 1000))
        let signature = generateHMACSignature(
            deviceId: finalDeviceId,
            timestamp: timestamp,
            requestBody: requestBodyString,
            keySeed: finalKeySeed
        )

        let url = URL(string: parseURL)!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("FinPin-URLScheme/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue(finalDeviceId, forHTTPHeaderField: "x-device-id")
        request.setValue(timestamp, forHTTPHeaderField: "x-timestamp")
        request.setValue(signature, forHTTPHeaderField: "x-signature")
        request.timeoutInterval = 30.0
        request.httpBody = requestBodyData

        do {
            let (data, response) = try await URLSession.shared.data(for: request)
            return try parseFinPinResponse(data: data, response: response, regexExtractedTime: regexExtractedTime)
        } catch {
            print("❌ Network error: \(error)")
            throw error
        }
    }

    private func parseExpenseWithCustomAPI(text: String, configuration: APIConfiguration) async throws -> ParsedExpense {
        print("🌐 Using custom API endpoint: \(configuration.baseURL)")

        // For custom API, we need to call the appropriate adapter directly
        switch configuration.provider {
        case .openai:
            return try await parseExpenseWithOpenAI(text: text, configuration: configuration)
        case .anthropic:
            return try await parseExpenseWithAnthropic(text: text, configuration: configuration)
        case .finpin:
            // This shouldn't happen in custom mode, but fallback to FinPin API
            return try await parseExpenseWithFinPinAPI(text: text)
        }
    }

    // MARK: - Unified Prompt System
    private func getUnifiedSystemPrompt() -> String {
        // CRITICAL: Always preserve local time from receipt text
        let timezoneInstruction = """
- CRITICAL TIMESTAMP RULES:
  - Extract the EXACT time from the receipt text (打印时间, 创单时间, 交易时间, Transaction Time, etc.)
  - Use the EXACT format found in the text, do NOT add timezone suffixes like 'Z' or '+08:00'
  - If time format is "2025-08-16 17:20:51", return "2025-08-16T17:20:51" (add T but NO timezone)
  - The time represents LOCAL time where the transaction occurred
  - Do NOT convert to UTC or add any timezone information
  - NEVER add 'Z' suffix or any timezone offset to the timestamp
  - Example: Receipt shows "2025-08-16 17:20:51" → Return "2025-08-16T17:20:51" (NOT "2025-08-16T17:20:51Z")
  - Preserve the original local time exactly as it appears on the receipt
"""

        return """
        You are an expert financial transaction parser specializing in mobile payment receipts and bank transaction records. Extract structured information from payment text in any language with high accuracy.

        CRITICAL: Respond with valid JSON only. No explanations, comments, or additional text.

        EXTRACTION TARGETS:
        - amount: Numerical value only (string, remove currency symbols, commas, spaces)
        - currency: ISO 4217 code (USD, GBP, EUR, CNY, JPY, ISK, etc.)
        - merchant: Primary business/merchant name (clean, without extra info)
        - payment_method: Payment method (Apple Pay, Google Pay, Alipay, WeChat Pay, Credit Card, Debit Card, etc.)
        - payment_card: Specific card/bank (Monzo, HSBC, Starling, Chase, Visa, Mastercard, etc.)
        - location: Geographic location (city, country, or address)
        - timestamp: ISO 8601 format (YYYY-MM-DDTHH:mm:ssZ) or null
        - confidence: Accuracy score (0.0-1.0)
        - extensions: Additional metadata

        PARSING RULES:
        1. AMOUNT: Look for numerical values with currency symbols (£, $, ¥, €, kr, ISK, etc.)
           - Remove all non-numeric characters except decimal points
           - Handle formats: "£9.65", "ISK 2,799", "¥25.00", "$12.50"

        2. CURRENCY: Map symbols and codes to ISO standards
           - £ → GBP, $ → USD, ¥ → CNY/JPY, € → EUR, kr/ISK → ISK
           - Look for explicit codes: USD, GBP, EUR, CNY, JPY, ISK
           - If no explicit currency is mentioned but the text contains Chinese payment-related keywords (e.g., "财付通", "零钱", "小程序", "淘宝", "支付宝", "余额宝"), infer CNY.
           - For English/UK contexts without explicit currency, infer GBP.
           - For US contexts, infer USD.
           - When no explicit currency is detected, use comprehensive inference (in priority order):
             - PRIORITY 1: User's location context (e.g., user in UK → GBP, user in China → CNY, user in Iceland → ISK)
             - PRIORITY 2: Merchant/location information from text (e.g., "Iceland" → ISK, "Japan" → JPY, "China/中国" → CNY)
             - PRIORITY 3: Text language context (e.g., Chinese characters → CNY, Icelandic text → ISK, Japanese text → JPY)
             - PRIORITY 4: Payment method context (e.g., Alipay/支付宝 → CNY, WeChat Pay/微信支付 → CNY)
             - PRIORITY 5: Merchant brand context (e.g., known Chinese brands → CNY, known UK brands → GBP)
           - Apply inference even for ambiguous amounts without clear currency symbols
           - Default to USD only if all inference methods fail

        3. MERCHANT: Extract primary business name
           - Prioritize recognizable brand names: "Costa Coffee", "Starbucks", "McDonald's"
           - Clean up: "Costa Coffee，英格兰 Hounslow" → "Costa Coffee"
           - Ignore: transaction IDs, addresses, extra descriptors

        4. PAYMENT_METHOD: Identify payment method
           - Mobile: Apple Pay, Google Pay, Samsung Pay
           - Chinese: Alipay, WeChat Pay, 支付宝, 微信支付
           - Cards: Credit Card, Debit Card, Visa, Mastercard
           - If card type mentioned: "Visa Debit Card" → "Debit Card"

        5. PAYMENT_CARD: Identify bank/card provider
           - Banks: Monzo, HSBC, Chase, Starling, Revolut, etc.
           - Card types: Visa, Mastercard, American Express
           - Format: "HSBC UK Visa Debit Card" → "HSBC"

        6. LOCATION: Extract geographic information
           - Cities: "Hounslow", "凱夫拉維克", "Beijing"
           - Countries: "英格兰" → "England", "Iceland"
           - Airports: "机场" indicates airport location

        7. TIMESTAMP: Parse actual transaction date/time from the receipt text
           - PRIORITIZE time found in the receipt text over any user provided timestamp
           - Look for explicit transaction time labels: "Transaction Time:", "Time:", "Date:", "交易时间:", "时间:", "日期:", etc.
           - Prioritize dates with clear transaction context over general dates
           - Formats: "2023-09-20 01:47", "2022/12/16 14:09", "09:41", "2023年09月20日 01:47"
           \(timezoneInstruction)
           - TIMEZONE INFERENCE: When no explicit timezone is provided, infer from context:
             * Chinese payment apps (支付宝, 微信支付): Assume UTC+8 (China Standard Time)
             * Japanese merchants/text: Assume UTC+9 (Japan Standard Time)
             * UK merchants/locations: Assume UTC+0 (GMT) or UTC+1 (BST)
             * US merchants/locations: Assume appropriate US timezone (UTC-5 to UTC-8)
             * European merchants: Assume UTC+1 (CET) or UTC+2 (CEST)
             * If location/merchant context unclear, use user's current timezone
           - Use user provided timestamp ONLY as fallback if no time is found in text, applying the same timezone adjustment
           - Set to null if no time information available at all

        8. EXTENSIONS: Add contextual information
           - category: Food & Beverage, Transportation, Shopping, Accommodation, etc.
           - tags: Relevant keywords ["coffee", "airport", "hotel", "restaurant"]
           - description: Brief summary of the transaction

        9. CONFIDENCE: Base on information clarity
           - 0.9-1.0: All key fields clearly identified
           - 0.7-0.9: Most fields identified, some ambiguity
           - 0.5-0.7: Basic info only, significant ambiguity
           - <0.5: Very unclear or incomplete

        LANGUAGE HANDLING:
        - English: Standard processing
        - Chinese: Handle mixed Chinese/English text
        - Other languages: Extract recognizable elements

        EXAMPLE RESPONSES:

        Costa Coffee Transaction:
        {
          "amount": "9.65",
          "currency": "GBP",
          "merchant": "Costa Coffee",
          "payment_method": "Debit Card",
          "payment_card": "HSBC",
          "location": "Hounslow, England",
          "timestamp": "2023-09-20T01:47:00Z",
          "confidence": 0.92,
          "extensions": {
            "category": "Food & Beverage",
            "tags": ["coffee", "food", "airport"],
            "description": "Costa Coffee purchase at Hounslow"
          }
        }

        Chinese Receipt Transaction:
        {
          "amount": "25.00",
          "currency": "CNY",
          "merchant": "星巴克",
          "payment_method": "支付宝",
          "payment_card": null,
          "location": "北京",
          "timestamp": "2023-09-20T14:30:00Z",
          "confidence": 0.95,
          "extensions": {
            "category": "Food & Beverage",
            "tags": ["coffee", "chinese"],
            "description": "星巴克消费"
          }
        }

        Hotel Transaction:
        {
          "amount": "2799",
          "currency": "ISK",
          "merchant": "Aurora Star Airport Hotel",
          "payment_method": "Credit Card",
          "payment_card": "Monzo",
          "location": "Keflavik, Iceland",
          "timestamp": "2022-12-16T14:09:00Z",
          "confidence": 0.88,
          "extensions": {
            "category": "Accommodation",
            "tags": ["hotel", "airport", "travel"],
            "description": "Airport hotel payment in Iceland"
          }
        }
        """
    }

    private func parseExpenseWithOpenAI(text: String, configuration: APIConfiguration) async throws -> ParsedExpense {
        print("🤖 OpenAI API: Starting request to \(configuration.baseURL)")

        // Priority 1: Try to extract time using regex from original text (preserves local time)
        let regexExtractedTime = TimeExtractor.shared.extractLocalTime(from: text)
        if let extractedTime = regexExtractedTime {
            print("✅ OpenAI API: Found local time via regex: \(TimeExtractor.shared.formatLocalTime(extractedTime))")
        }

        // Create OpenAI API request
        let url = URL(string: "\(configuration.baseURL)/chat/completions")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("Bearer \(configuration.apiKey)", forHTTPHeaderField: "Authorization")
        request.timeoutInterval = 30

        let systemPrompt = getUnifiedSystemPrompt()

        let messages = [
            ["role": "system", "content": systemPrompt],
            ["role": "user", "content": text]
        ]

        let requestBody: [String: Any] = [
            "model": configuration.modelName ?? "gpt-4",
            "messages": messages,
            "max_tokens": configuration.maxTokens ?? 1000,
            "temperature": configuration.temperature ?? 0.1
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        print("🤖 OpenAI API: Sending request...")
        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "OpenAI", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
        }

        print("🤖 OpenAI API: Response status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("❌ OpenAI API Error: \(errorMessage)")
            throw NSError(domain: "OpenAI", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API request failed: \(errorMessage)"])
        }

        // Parse OpenAI response
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let choices = json["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String else {
            print("❌ Failed to parse OpenAI response structure")
            throw NSError(domain: "OpenAI", code: -2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse response"])
        }

        print("🤖 OpenAI API Response: \(content)")

        // Parse the JSON content from the AI response
        guard let contentData = content.data(using: .utf8),
              let expenseJson = try? JSONSerialization.jsonObject(with: contentData) as? [String: Any] else {
            print("❌ Failed to parse expense JSON from AI response")
            throw NSError(domain: "OpenAI", code: -3, userInfo: [NSLocalizedDescriptionKey: "Failed to parse expense data"])
        }

        // Extract expense data with full server-compatible structure
        let amountString = expenseJson["amount"] as? String ?? "0"
        let amount = Decimal(string: amountString) ?? Decimal(0)
        let currency = expenseJson["currency"] as? String ?? "USD"
        let merchant = expenseJson["merchant"] as? String
        let paymentMethod = expenseJson["payment_method"] as? String
        let paymentCard = expenseJson["payment_card"] as? String
        let location = expenseJson["location"] as? String
        let timestampString = expenseJson["timestamp"] as? String
        let confidence = expenseJson["confidence"] as? Double ?? 0.5

        // Extract extensions data
        let extensions = expenseJson["extensions"] as? [String: Any] ?? [:]
        let category = extensions["category"] as? String
        let tags = extensions["tags"] as? [String] ?? []
        let description = extensions["description"] as? String

        // Parse timestamp - Priority 1: Use regex-extracted time, Priority 2: Use LLM timestamp
        var transactionDate: Date? = nil
        var originalTimestamp: String? = nil

        if let regexTime = regexExtractedTime {
            // Use regex-extracted local time (highest priority)
            transactionDate = regexTime
            originalTimestamp = TimeExtractor.shared.formatLocalTime(regexTime)
            print("✅ OpenAI API: Using regex-extracted local time: \(originalTimestamp!)")
        } else if let timestampString = timestampString, !timestampString.isEmpty && timestampString != "null" {
            print("🕐 OpenAI API: Fallback to LLM timestamp: \(timestampString)")
            originalTimestamp = timestampString

            // Try to parse as local time first (without timezone conversion)
            let localFormatter = DateFormatter()
            localFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            localFormatter.timeZone = TimeZone.current

            if let localTime = localFormatter.date(from: timestampString.replacingOccurrences(of: "Z", with: "")) {
                transactionDate = localTime
                print("✅ OpenAI API: Parsed LLM timestamp as local time: \(TimeExtractor.shared.formatLocalTime(localTime))")
            } else {
                // Fallback to ISO8601 parsing
                let formatter = ISO8601DateFormatter()
                transactionDate = formatter.date(from: timestampString)
                print("🕐 OpenAI API: Parsed as ISO8601: \(transactionDate?.description ?? "nil")")
            }
        }

        print("✅ OpenAI API: Parsed expense - \(amount) \(currency) at \(merchant ?? "Unknown")")
        if let location = location {
            print("📍 OpenAI API: Location - \(location)")
        }
        if let paymentCard = paymentCard {
            print("💳 OpenAI API: Payment card - \(paymentCard)")
        }

        return ParsedExpense(
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            date: Date(),
            transactionDate: transactionDate,
            originalTimestamp: originalTimestamp,
            tags: tags,
            notes: description,
            category: category,
            confidence: confidence
        )
    }

    private func parseExpenseWithAnthropic(text: String, configuration: APIConfiguration) async throws -> ParsedExpense {
        print("🤖 Anthropic API: Starting request to \(configuration.baseURL)")

        // Priority 1: Try to extract time using regex from original text (preserves local time)
        let regexExtractedTime = TimeExtractor.shared.extractLocalTime(from: text)
        if let extractedTime = regexExtractedTime {
            print("✅ Anthropic API: Found local time via regex: \(TimeExtractor.shared.formatLocalTime(extractedTime))")
        }

        // Create Anthropic API request
        let url = URL(string: "\(configuration.baseURL)/messages")!
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(configuration.apiKey, forHTTPHeaderField: "x-api-key")
        request.setValue("2023-06-01", forHTTPHeaderField: "anthropic-version")
        request.timeoutInterval = 30

        let systemPrompt = getUnifiedSystemPrompt()

        let requestBody: [String: Any] = [
            "model": configuration.modelName ?? "claude-3-5-sonnet-20241022",
            "max_tokens": configuration.maxTokens ?? 1000,
            "system": systemPrompt,
            "messages": [
                ["role": "user", "content": "Parse this transaction text: \(text)"]
            ]
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        print("🤖 Anthropic API: Sending request...")
        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw NSError(domain: "Anthropic", code: -1, userInfo: [NSLocalizedDescriptionKey: "Invalid response"])
        }

        print("🤖 Anthropic API: Response status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            let errorMessage = String(data: data, encoding: .utf8) ?? "Unknown error"
            print("❌ Anthropic API Error: \(errorMessage)")
            throw NSError(domain: "Anthropic", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: "API request failed: \(errorMessage)"])
        }

        // Parse Anthropic response
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let content = json["content"] as? [[String: Any]],
              let firstContent = content.first,
              let text = firstContent["text"] as? String else {
            print("❌ Failed to parse Anthropic response structure")
            throw NSError(domain: "Anthropic", code: -2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse response"])
        }

        print("🤖 Anthropic API Response: \(text)")

        // Parse the JSON content from the AI response
        guard let contentData = text.data(using: .utf8),
              let expenseJson = try? JSONSerialization.jsonObject(with: contentData) as? [String: Any] else {
            print("❌ Failed to parse expense JSON from AI response")
            throw NSError(domain: "Anthropic", code: -3, userInfo: [NSLocalizedDescriptionKey: "Failed to parse expense data"])
        }

        // Extract expense data with full server-compatible structure
        let amountString = expenseJson["amount"] as? String ?? "0"
        let amount = Decimal(string: amountString) ?? Decimal(0)
        let currency = expenseJson["currency"] as? String ?? "USD"
        let merchant = expenseJson["merchant"] as? String
        let paymentMethod = expenseJson["payment_method"] as? String
        let paymentCard = expenseJson["payment_card"] as? String
        let location = expenseJson["location"] as? String
        let timestampString = expenseJson["timestamp"] as? String
        let confidence = expenseJson["confidence"] as? Double ?? 0.5

        // Extract extensions data
        let extensions = expenseJson["extensions"] as? [String: Any] ?? [:]
        let category = extensions["category"] as? String
        let tags = extensions["tags"] as? [String] ?? []
        let description = extensions["description"] as? String

        // Parse timestamp - Priority 1: Use regex-extracted time, Priority 2: Use LLM timestamp
        var transactionDate: Date? = nil
        var originalTimestamp: String? = nil

        if let regexTime = regexExtractedTime {
            // Use regex-extracted local time (highest priority)
            transactionDate = regexTime
            originalTimestamp = TimeExtractor.shared.formatLocalTime(regexTime)
            print("✅ Anthropic API: Using regex-extracted local time: \(originalTimestamp!)")
        } else if let timestampString = timestampString, !timestampString.isEmpty && timestampString != "null" {
            print("🕐 Anthropic API: Fallback to LLM timestamp: \(timestampString)")
            originalTimestamp = timestampString

            // Try to parse as local time first (without timezone conversion)
            let localFormatter = DateFormatter()
            localFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            localFormatter.timeZone = TimeZone.current

            if let localTime = localFormatter.date(from: timestampString.replacingOccurrences(of: "Z", with: "")) {
                transactionDate = localTime
                print("✅ Anthropic API: Parsed LLM timestamp as local time: \(TimeExtractor.shared.formatLocalTime(localTime))")
            } else {
                // Fallback to ISO8601 parsing
                let formatter = ISO8601DateFormatter()
                transactionDate = formatter.date(from: timestampString)
                print("🕐 Anthropic API: Parsed as ISO8601: \(transactionDate?.description ?? "nil")")
            }
        }

        print("✅ Anthropic API: Parsed expense - \(amount) \(currency) at \(merchant ?? "Unknown")")
        if let location = location {
            print("📍 Anthropic API: Location - \(location)")
        }
        if let paymentCard = paymentCard {
            print("💳 Anthropic API: Payment card - \(paymentCard)")
        }

        return ParsedExpense(
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            date: Date(),
            transactionDate: transactionDate,
            originalTimestamp: originalTimestamp,
            tags: tags,
            notes: description,
            category: category,
            confidence: confidence
        )
    }

    private func parseFinPinResponse(data: Data, response: URLResponse, regexExtractedTime: Date?) throws -> ParsedExpense {
        guard let httpResponse = response as? HTTPURLResponse else {
            print("❌ Invalid response type")
            throw NSError(domain: "CloudflareExpenseParser", code: 1, userInfo: [NSLocalizedDescriptionKey: "Invalid response type"])
        }

        print("📡 HTTP Status: \(httpResponse.statusCode)")

        guard httpResponse.statusCode == 200 else {
            let errorMessage = "HTTP \(httpResponse.statusCode): \(HTTPURLResponse.localizedString(forStatusCode: httpResponse.statusCode))"
            print("❌ \(errorMessage)")
            throw NSError(domain: "CloudflareExpenseParser", code: httpResponse.statusCode, userInfo: [NSLocalizedDescriptionKey: errorMessage])
        }

        // Print server response for debugging
        let responseString = String(data: data, encoding: .utf8) ?? "Unknown error"
        print("🔍 CLOUDFLARE PARSER SERVER RESPONSE JSON:")
        print(responseString)
        print("🔍 END CLOUDFLARE PARSER SERVER RESPONSE")

        // Parse response using the same format as image import
        guard let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let success = json["success"] as? Bool, success,
              let responseData = json["data"] as? [String: Any] else {
            print("❌ Failed to parse server response: \(responseString)")
            throw NSError(domain: "CloudflareExpenseParser", code: 2, userInfo: [NSLocalizedDescriptionKey: "Failed to parse server response: \(responseString)"])
        }

        // Extract response data
        let amountString = responseData["amount"] as? String ?? "0"
        let currency = responseData["currency"] as? String ?? "USD"
        let merchant = responseData["merchant"] as? String
        let paymentMethod = responseData["payment_method"] as? String
        let paymentCard = responseData["payment_card"] as? String
        let location = responseData["location"] as? String
        let confidence = responseData["confidence"] as? Double ?? 0.5

        // Debug: Log the payment method extraction
        print("💳 CloudflareExpenseParser: Extracted payment_method from server: \(paymentMethod ?? "nil")")

        // Parse timestamp - Priority 1: Use regex-extracted time, Priority 2: Use LLM timestamp
        var transactionDate: Date? = nil
        var originalTimestamp: String? = nil

        if let regexTime = regexExtractedTime {
            // Use regex-extracted local time (highest priority)
            transactionDate = regexTime
            originalTimestamp = TimeExtractor.shared.formatLocalTime(regexTime)
            print("✅ CloudflareExpenseParser: Using regex-extracted local time: \(originalTimestamp!)")
        } else if let timestampString = responseData["timestamp"] as? String {
            print("🕐 CloudflareExpenseParser: Fallback to LLM timestamp: \(timestampString)")
            originalTimestamp = timestampString

            // Try to parse as local time first (without timezone conversion)
            let localFormatter = DateFormatter()
            localFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss"
            localFormatter.timeZone = TimeZone.current

            if let localTime = localFormatter.date(from: timestampString.replacingOccurrences(of: "Z", with: "")) {
                transactionDate = localTime
                print("✅ CloudflareExpenseParser: Parsed LLM timestamp as local time: \(TimeExtractor.shared.formatLocalTime(localTime))")
            } else {
                // Fallback to ISO8601 parsing
                let formatter = ISO8601DateFormatter()
                if let utcDate = formatter.date(from: timestampString) {
                    transactionDate = utcDate
                    print("🕐 CloudflareExpenseParser: Parsed as UTC time: \(utcDate)")
                } else {
                    print("⚠️ CloudflareExpenseParser: Failed to parse timestamp: \(timestampString)")
                }
            }
        } else {
            print("❌ CloudflareExpenseParser: No timestamp found in response data or original text")
        }

        let extensions = responseData["extensions"] as? [String: Any]
        let category = extensions?["category"] as? String
        let tags = extensions?["tags"] as? [String] ?? []
        let description = extensions?["description"] as? String

        print("✅ Parse successful: \(amountString) \(currency) at \(merchant ?? "Unknown")")
        if let transactionDate = transactionDate {
            print("🕐 Transaction time: \(transactionDate)")
        }

        // Convert amount string to Decimal
        let amount = Decimal(string: amountString) ?? Decimal(0)

        let parsedExpense = ParsedExpense(
            amount: amount,
            currency: currency,
            merchant: merchant,
            paymentMethod: paymentMethod,
            paymentCard: paymentCard,
            location: location,
            date: Date(), // Record creation time
            transactionDate: transactionDate, // Actual transaction time from receipt
            originalTimestamp: originalTimestamp,
            tags: tags,
            notes: description,
            category: category,
            confidence: confidence
        )

        print("✅ CloudflareExpenseParser: Returning ParsedExpense:")
        print("   - amount: \(amount) \(currency)")
        print("   - paymentMethod: \(paymentMethod ?? "nil")")
        print("   - date (record creation): \(Date())")
        print("   - transactionDate (actual): \(transactionDate?.description ?? "nil")")

        return parsedExpense
    }

    // MARK: - HMAC Signature Generation
    private func generateHMACSignature(deviceId: String, timestamp: String, requestBody: String, keySeed: String) -> String {
        print("🔒 CloudflareExpenseParser HMAC Signature Generation:")

        // Hash the request body using SHA-256 (matching server implementation)
        let requestBodyData = Data(requestBody.utf8)
        let requestBodyHash = SHA256.hash(data: requestBodyData)
        let requestBodyHashString = requestBodyHash.compactMap { String(format: "%02x", $0) }.joined()

        print("   Request Body Hash: \(requestBodyHashString.prefix(40))...")

        // Create message: timestamp + deviceId + requestBodyHash (matching server format)
        let message = "\(timestamp)\(deviceId)\(requestBodyHashString)"
        let messageData = Data(message.utf8)

        print("   Message to Sign: \(message.prefix(100))\(message.count > 100 ? "..." : "")")

        // Create HMAC key from keySeed
        let keyData = Data(keySeed.utf8)
        let key = SymmetricKey(data: keyData)

        // Generate HMAC-SHA256 signature
        let signature = HMAC<SHA256>.authenticationCode(for: messageData, using: key)
        let base64Signature = Data(signature).base64EncodedString()

        print("   Final Signature: \(base64Signature.prefix(40))...")
        print("🔒 HMAC signature generation completed")

        return base64Signature
    }

    // MARK: - Device Registration
    private func ensureDeviceRegistration() async {
        // Check if device is already registered using legacy UserDefaults
        let deviceId = UserDefaults.standard.string(forKey: "debug_device_id")
        let keySeed = UserDefaults.standard.string(forKey: "debug_key_seed")

        if deviceId != nil && keySeed != nil {
            print("✅ Device already registered (CloudflareExpenseParser)")
            return
        }

        print("🔐 Device not registered, attempting registration (CloudflareExpenseParser)...")

        do {
            let newDeviceId = UUID().uuidString

            let endpointManager = APIEndpointManager.shared
            let url = URL(string: endpointManager.getDeviceRegisterURL())!
            var request = URLRequest(url: url)
            request.httpMethod = "POST"
            request.setValue("application/json", forHTTPHeaderField: "Content-Type")
            request.setValue("FinPin-CloudflareParser/1.0", forHTTPHeaderField: "User-Agent")
            request.timeoutInterval = 30.0

            let requestBody: [String: Any] = [
                "device_id": newDeviceId,
                "device_info": [
                    "model": UIDevice.current.model,
                    "os_version": UIDevice.current.systemVersion,
                    "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0.0",
                    "platform": "ios"
                ]
            ]

            request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse,
                  httpResponse.statusCode == 200 else {
                print("❌ Device registration failed: HTTP \((response as? HTTPURLResponse)?.statusCode ?? 0)")
                return
            }

            let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]

            if let success = responseData?["success"] as? Bool, success,
               let data = responseData?["data"] as? [String: Any],
               let keySeed = data["key_seed"] as? String {

                UserDefaults.standard.set(newDeviceId, forKey: "debug_device_id")
                UserDefaults.standard.set(keySeed, forKey: "debug_key_seed")

                print("✅ Device registration successful (CloudflareExpenseParser)")
                print("📱 Device ID: \(newDeviceId)")
                print("🔑 Key seed stored securely")
            } else {
                print("❌ Device registration failed: Invalid response")
            }

        } catch {
            print("❌ Device registration error: \(error.localizedDescription)")
            // Don't block the app, registration will be retried when needed
        }
    }
}

// MARK: - Siri Shortcuts Manager
@MainActor
class SiriShortcutsManager: ObservableObject {
    static let shared = SiriShortcutsManager()

    @Published var isEnabled = false
    @Published var availableShortcuts: [INShortcut] = []

    private init() {
        loadSettings()
        setupShortcuts()
    }

    // MARK: - Settings Management
    private func loadSettings() {
        isEnabled = UserDefaults.standard.bool(forKey: "siri_shortcuts_enabled")
    }

    func saveSettings() {
        UserDefaults.standard.set(isEnabled, forKey: "siri_shortcuts_enabled")
    }

    func toggleShortcuts(_ enabled: Bool) {
        isEnabled = enabled
        saveSettings()

        if enabled {
            // Request Siri authorization if needed
            if #available(iOS 12.0, *) {
                let authStatus = INPreferences.siriAuthorizationStatus()
                if authStatus == .notDetermined {
                    INPreferences.requestSiriAuthorization { [weak self] status in
                        DispatchQueue.main.async {
                            if status == .authorized {
                                self?.setupShortcuts()
                            } else {
                                print("❌ Siri authorization denied, disabling shortcuts")
                                self?.isEnabled = false
                                self?.saveSettings()
                            }
                        }
                    }
                } else if authStatus == .authorized {
                    setupShortcuts()
                } else {
                    print("❌ Siri not authorized, cannot enable shortcuts")
                    isEnabled = false
                    saveSettings()
                }
            }
        } else {
            removeAllShortcuts()
        }
    }

    // MARK: - Shortcuts Setup
    private func setupShortcuts() {
        guard isEnabled else { return }

        // Check Siri authorization before setting up shortcuts
        if #available(iOS 12.0, *) {
            let authStatus = INPreferences.siriAuthorizationStatus()
            guard authStatus == .authorized else {
                print("⚠️ Siri not authorized, status: \(authStatus)")
                return
            }
        }

        // Create shortcuts for both voice and text input
        let voiceShortcut = createVoiceExpenseShortcut()
        let textShortcut = createTextExpenseShortcut()
        availableShortcuts = [voiceShortcut, textShortcut]

        // Donate shortcuts to Siri
        donateShortcuts()
    }

    private func createVoiceExpenseShortcut() -> INShortcut {
        // Create user activity for Siri voice commands
        let userActivity = NSUserActivity(activityType: "app.finpin.v1.addExpenseVoice")
        userActivity.title = "Add Expense with Voice"
        userActivity.suggestedInvocationPhrase = "Add expense to FinPin"
        userActivity.isEligibleForPrediction = true
        userActivity.isEligibleForSearch = true
        userActivity.userInfo = ["inputType": "voice"]

        let shortcut = INShortcut(userActivity: userActivity)
        return shortcut
    }

    private func createTextExpenseShortcut() -> INShortcut {
        // Create user activity for Shortcuts app text input
        let userActivity = NSUserActivity(activityType: "app.finpin.v1.addExpenseText")
        userActivity.title = "Add Expense with Text"
        userActivity.suggestedInvocationPhrase = "Add text expense to FinPin"
        userActivity.isEligibleForPrediction = true
        userActivity.isEligibleForSearch = true
        userActivity.userInfo = ["inputType": "text"]

        let shortcut = INShortcut(userActivity: userActivity)
        return shortcut
    }

    private func donateShortcuts() {
        for shortcut in availableShortcuts {
            if let intent = shortcut.intent {
                let interaction = INInteraction(intent: intent, response: nil)
                interaction.donate { error in
                    if let error = error {
                        print("❌ Failed to donate shortcut: \(error)")
                    } else {
                        print("✅ Successfully donated shortcut")
                    }
                }
            }
        }
    }

    private func removeAllShortcuts() {
        INInteraction.deleteAll { error in
            if let error = error {
                print("❌ Failed to remove shortcuts: \(error)")
            } else {
                print("✅ Successfully removed all shortcuts")
            }
        }
        availableShortcuts.removeAll()
    }

    // MARK: - Expense Processing
    func processExpenseFromSiri(text: String) async -> Bool {
        print("🎤 Processing Siri voice input: \(text)")

        // Use Cloudflare service for intelligent parsing
        //return await processExpenseWithCloudflare(text: text, source: "Siri Voice")
        return await processExpenseWithCloudflare(text: text, source: "Shortcuts")
    }

    func processExpenseFromText(text: String) async -> Bool {
        print("📝 Processing Shortcuts text input: \(text)")

        // Use Cloudflare service for intelligent parsing
        return await processExpenseWithCloudflare(text: text, source: "Shortcuts")
    }

    private func processExpenseWithCloudflare(text: String, source: String) async -> Bool {
        do {
            // Call Cloudflare expense parsing service
            let parsedExpense = try await CloudflareExpenseParser.shared.parseExpense(from: text)
            
            // Debug: Log payment method from server
            print("💳 processExpenseWithCloudflare: Server returned payment method: \(parsedExpense.paymentMethod ?? "nil")")
            print("💳 processExpenseWithCloudflare: Source parameter: \(source)")

            // Create expense record with parsed data
            var expense = ExpenseRecord(
                id: UUID(),
                amount: parsedExpense.amount,
                currency: parsedExpense.currency,
                merchant: parsedExpense.merchant,
                date: parsedExpense.date ?? Date(),
                transactionDate: parsedExpense.transactionDate,
                tags: parsedExpense.tags + [source],
                paymentMethod: parsedExpense.paymentMethod, // ✅ FIX: Use server-parsed payment method, not source
                notes: parsedExpense.notes,
                rawText: text
            )
            
            // Debug: Log final payment method
            print("💳 processExpenseWithCloudflare: Final expense payment method: \(expense.paymentMethod ?? "nil")")

            // Add multi-currency pre-storage
            expense = await addMultiCurrencyAmounts(to: expense)

            // Save the expense
            await saveExpense(expense)

            print("✅ Successfully processed expense via \(source): \(expense.merchant ?? "Unknown") - \(expense.amount) \(expense.currency)")
            return true

        } catch {
            print("❌ Failed to process expense via \(source): \(error.localizedDescription)")

            // Fallback to local parsing if Cloudflare fails
            let fallbackExpense = createFallbackExpense(from: text, source: source)
            await saveExpense(fallbackExpense)

            return false
        }
    }

    private func parseSimpleExpense(from text: String) -> ExpenseRecord {
        // Simple parsing for voice input - extract amount and use text as merchant
        let amount = extractAmount(from: text) ?? 0.0
        let merchant = text.replacingOccurrences(of: String(amount), with: "").trimmingCharacters(in: .whitespacesAndNewlines)

        return ExpenseRecord(
            id: UUID(),
            amount: Decimal(amount),
            currency: "USD",
            merchant: merchant.isEmpty ? "Siri Expense" : merchant,
            date: Date(),
            tags: ["Siri"],
            paymentMethod: "Unknown" // ✅ FIX: Use Unknown instead of hardcoded value
        )
    }

    private func parseAdvancedExpense(from text: String) -> ExpenseRecord {
        // Advanced parsing for text input from Shortcuts
        let lines = text.components(separatedBy: .newlines)
        var amount: Decimal = 0
        var merchant: String?
        var currency = "USD"
        var category: String?
        let notes: String? = nil

        // Parse each line for different information
        for line in lines {
            let trimmed = line.trimmingCharacters(in: .whitespacesAndNewlines)

            // Look for amount patterns with currency symbols
            if let match = trimmed.range(of: #"[\$£€¥]?(\d+\.?\d*)"#, options: .regularExpression) {
                let amountString = String(trimmed[match]).replacingOccurrences(of: "[^0-9.]", with: "", options: .regularExpression)
                amount = Decimal(string: amountString) ?? 0

                // Detect currency from symbols
                if trimmed.contains("£") { currency = "GBP" }
                else if trimmed.contains("€") { currency = "EUR" }
                else if trimmed.contains("¥") { currency = "CNY" }
                else { currency = "USD" }
            }

            // Look for merchant name (first non-amount line)
            if merchant == nil && !trimmed.isEmpty && !trimmed.contains(where: { "0123456789$£€¥".contains($0) }) {
                merchant = trimmed
            }

            // Look for category keywords
            if category == nil {
                let lowercased = trimmed.lowercased()
                if lowercased.contains("food") || lowercased.contains("restaurant") || lowercased.contains("cafe") {
                    category = "Food & Dining"
                } else if lowercased.contains("gas") || lowercased.contains("fuel") || lowercased.contains("transport") {
                    category = "Transportation"
                } else if lowercased.contains("shop") || lowercased.contains("store") || lowercased.contains("retail") {
                    category = "Shopping"
                }
            }
        }

        return ExpenseRecord(
            id: UUID(),
            amount: amount,
            currency: currency,
            merchant: merchant ?? "Shortcuts Entry",
            date: Date(),
            tags: ["Shortcuts"],
            paymentMethod: "Unknown", // ✅ FIX: Use Unknown instead of hardcoded value
            notes: notes,
            rawText: text
        )
    }

    private func extractAmount(from text: String) -> Double? {
        let pattern = #"\d+\.?\d*"#
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(text.startIndex..<text.endIndex, in: text)

        if let match = regex?.firstMatch(in: text, range: range) {
            let matchRange = Range(match.range, in: text)!
            return Double(text[matchRange])
        }

        return nil
    }

    private func addMultiCurrencyAmounts(to expense: ExpenseRecord) async -> ExpenseRecord {
        var enhancedExpense = expense
        let cloudflareService = CloudflareExchangeRateService.shared

        var majorCurrencyAmounts: [String: Decimal] = [:]
        var exchangeRatesSnapshot: [String: Double] = [:]

        // 为每种主流货币计算并存储金额
        for targetCurrency in CurrencyHelper.majorCurrencies {
            if targetCurrency == expense.currency {
                // 原始货币，直接存储
                majorCurrencyAmounts[targetCurrency] = expense.amount
                exchangeRatesSnapshot[targetCurrency] = 1.0
            } else {
                // 转换到目标货币
                if let rate = cloudflareService.getRate(from: expense.currency, to: targetCurrency) {
                    let convertedAmount = expense.amount * Decimal(rate)
                    majorCurrencyAmounts[targetCurrency] = convertedAmount
                    exchangeRatesSnapshot[targetCurrency] = rate
                }
            }
        }

        enhancedExpense.majorCurrencyAmounts = majorCurrencyAmounts
        enhancedExpense.exchangeRatesSnapshot = exchangeRatesSnapshot

        return enhancedExpense
    }

    private func createFallbackExpense(from text: String, source: String) -> ExpenseRecord {
        // Simple fallback parsing when Cloudflare service fails
        let amount = extractAmount(from: text) ?? 0.0
        let merchant = text.replacingOccurrences(of: String(amount), with: "").trimmingCharacters(in: .whitespacesAndNewlines)

        return ExpenseRecord(
            id: UUID(),
            amount: Decimal(amount),
            currency: "USD", // Default to USD for fallback
            merchant: merchant.isEmpty ? "\(source) Entry" : merchant,
            date: Date(),
            tags: [source, "Fallback"],
            paymentMethod: "Unknown", // ✅ FIX: Use Unknown instead of source
            rawText: text
        )
    }

    private func saveExpense(_ expense: ExpenseRecord) async {
        // Save through DataManager
        let dataManager = DataManager.shared
        dataManager.addExpense(expense)
        print("✅ Expense saved from \(expense.paymentMethod ?? "Unknown"): \(expense.merchant ?? "Unknown") - \(expense.amount) \(expense.currency)")
    }
}



// MARK: - App Intents Package Registration
@available(iOS 17.0, *)
extension FinPinApp {
    static var appIntentsPackage: some AppIntentsPackage {
        return FinPinAppIntentsPackage()
    }
}

@available(iOS 17.0, *)
struct FinPinAppIntentsPackage: AppIntentsPackage {
    static var includedPackages: [any AppIntentsPackage.Type] = []
}

// MARK: - Toast Notification System
extension Notification.Name {
    static let showToast = Notification.Name("showToast")
    static let showPurchaseView = Notification.Name("showPurchaseView")
    static let subscriptionStatusChanged = Notification.Name("subscriptionStatusChanged")
}


